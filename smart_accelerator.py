"""
نظام التسريع الذكي - Smart Download Accelerator
تحسين السرعة تلقائياً باستخدام خوارزميات ذكية
"""

import threading
import time
import statistics
from datetime import datetime, timedelta
from collections import deque
import json
import os

class SmartAccelerator:
    """نظام التسريع الذكي للتحميلات"""
    
    def __init__(self):
        self.enabled = True
        self.learning_mode = True
        self.optimization_level = "balanced"  # conservative, balanced, aggressive
        
        # بيانات الأداء
        self.performance_history = deque(maxlen=1000)
        self.connection_performance = {}
        self.server_performance = {}
        self.network_conditions = deque(maxlen=100)
        
        # إعدادات التحسين
        self.min_connections = 1
        self.max_connections = 16
        self.optimal_connections = 4
        self.chunk_size_range = (1024, 1024*1024)  # 1KB to 1MB
        self.optimal_chunk_size = 8192
        
        # خوارزميات التحسين
        self.algorithms = {
            'adaptive_connections': True,
            'dynamic_chunk_sizing': True,
            'server_load_balancing': True,
            'network_condition_adaptation': True,
            'predictive_optimization': True
        }
        
        # بيانات التعلم
        self.learning_data = self.load_learning_data()
        
        # بدء المراقبة
        self.start_monitoring()
    
    def load_learning_data(self):
        """تحميل بيانات التعلم المحفوظة"""
        try:
            if os.path.exists('accelerator_learning.json'):
                with open('accelerator_learning.json', 'r', encoding='utf-8') as f:
                    return json.load(f)
            return {
                'optimal_settings': {},
                'server_patterns': {},
                'network_patterns': {},
                'user_preferences': {}
            }
        except Exception as e:
            print(f"خطأ في تحميل بيانات التعلم: {e}")
            return {}
    
    def save_learning_data(self):
        """حفظ بيانات التعلم"""
        try:
            with open('accelerator_learning.json', 'w', encoding='utf-8') as f:
                json.dump(self.learning_data, f, ensure_ascii=False, indent=2, default=str)
        except Exception as e:
            print(f"خطأ في حفظ بيانات التعلم: {e}")
    
    def start_monitoring(self):
        """بدء مراقبة الأداء"""
        if not self.enabled:
            return
        
        def monitoring_loop():
            while self.enabled:
                try:
                    self.analyze_network_conditions()
                    self.update_optimization_parameters()
                    time.sleep(5)  # فحص كل 5 ثوان
                except Exception as e:
                    print(f"خطأ في مراقبة الأداء: {e}")
                    time.sleep(10)
        
        monitor_thread = threading.Thread(target=monitoring_loop, daemon=True)
        monitor_thread.start()
    
    def analyze_network_conditions(self):
        """تحليل حالة الشبكة"""
        try:
            import psutil
            
            # الحصول على إحصائيات الشبكة
            net_io = psutil.net_io_counters()
            
            condition = {
                'timestamp': datetime.now(),
                'bytes_sent': net_io.bytes_sent,
                'bytes_recv': net_io.bytes_recv,
                'packets_sent': net_io.packets_sent,
                'packets_recv': net_io.packets_recv,
                'errors_in': net_io.errin,
                'errors_out': net_io.errout,
                'drops_in': net_io.dropin,
                'drops_out': net_io.dropout
            }
            
            self.network_conditions.append(condition)
            
            # تحليل جودة الشبكة
            self.analyze_network_quality()
            
        except ImportError:
            pass  # psutil غير متوفر
        except Exception as e:
            print(f"خطأ في تحليل حالة الشبكة: {e}")
    
    def analyze_network_quality(self):
        """تحليل جودة الشبكة"""
        if len(self.network_conditions) < 2:
            return
        
        recent = list(self.network_conditions)[-10:]  # آخر 10 قراءات
        
        # حساب معدل الأخطاء
        error_rates = []
        for i in range(1, len(recent)):
            prev = recent[i-1]
            curr = recent[i]
            
            total_packets = (curr['packets_sent'] - prev['packets_sent']) + (curr['packets_recv'] - prev['packets_recv'])
            total_errors = (curr['errors_in'] - prev['errors_in']) + (curr['errors_out'] - prev['errors_out'])
            
            if total_packets > 0:
                error_rate = total_errors / total_packets
                error_rates.append(error_rate)
        
        # تحديد جودة الشبكة
        if error_rates:
            avg_error_rate = statistics.mean(error_rates)
            
            if avg_error_rate < 0.001:
                network_quality = "excellent"
            elif avg_error_rate < 0.01:
                network_quality = "good"
            elif avg_error_rate < 0.05:
                network_quality = "fair"
            else:
                network_quality = "poor"
            
            # تحديث إعدادات التحسين حسب جودة الشبكة
            self.adapt_to_network_quality(network_quality)
    
    def adapt_to_network_quality(self, quality):
        """تكييف الإعدادات حسب جودة الشبكة"""
        if quality == "excellent":
            self.optimal_connections = min(self.max_connections, 8)
            self.optimal_chunk_size = 65536  # 64KB
        elif quality == "good":
            self.optimal_connections = 6
            self.optimal_chunk_size = 32768  # 32KB
        elif quality == "fair":
            self.optimal_connections = 4
            self.optimal_chunk_size = 16384  # 16KB
        else:  # poor
            self.optimal_connections = 2
            self.optimal_chunk_size = 8192   # 8KB
    
    def optimize_download_settings(self, url, file_size=0, current_speed=0):
        """تحسين إعدادات التحميل لرابط معين"""
        if not self.enabled:
            return {
                'connections': 4,
                'chunk_size': 8192,
                'retry_delay': 1,
                'timeout': 30
            }
        
        # تحليل الخادم
        server_info = self.analyze_server(url)
        
        # تحليل حجم الملف
        file_size_category = self.categorize_file_size(file_size)
        
        # الحصول على الإعدادات المثلى
        optimal_settings = self.calculate_optimal_settings(
            server_info, file_size_category, current_speed
        )
        
        # تطبيق التعلم الآلي
        if self.learning_mode:
            optimal_settings = self.apply_machine_learning(url, optimal_settings)
        
        return optimal_settings
    
    def analyze_server(self, url):
        """تحليل أداء الخادم"""
        from urllib.parse import urlparse
        
        domain = urlparse(url).netloc
        
        if domain not in self.server_performance:
            self.server_performance[domain] = {
                'response_times': deque(maxlen=50),
                'success_rate': 1.0,
                'optimal_connections': 4,
                'supports_resume': True,
                'max_connections_tested': 4
            }
        
        return self.server_performance[domain]
    
    def categorize_file_size(self, file_size):
        """تصنيف حجم الملف"""
        if file_size == 0:
            return "unknown"
        elif file_size < 1024 * 1024:  # أقل من 1MB
            return "small"
        elif file_size < 100 * 1024 * 1024:  # أقل من 100MB
            return "medium"
        elif file_size < 1024 * 1024 * 1024:  # أقل من 1GB
            return "large"
        else:
            return "very_large"
    
    def calculate_optimal_settings(self, server_info, file_size_category, current_speed):
        """حساب الإعدادات المثلى"""
        settings = {
            'connections': self.optimal_connections,
            'chunk_size': self.optimal_chunk_size,
            'retry_delay': 1,
            'timeout': 30
        }
        
        # تحسين حسب حجم الملف
        if file_size_category == "small":
            settings['connections'] = min(2, self.optimal_connections)
            settings['chunk_size'] = 4096
        elif file_size_category == "medium":
            settings['connections'] = self.optimal_connections
            settings['chunk_size'] = self.optimal_chunk_size
        elif file_size_category == "large":
            settings['connections'] = min(self.max_connections, self.optimal_connections + 2)
            settings['chunk_size'] = min(131072, self.optimal_chunk_size * 2)  # 128KB max
        elif file_size_category == "very_large":
            settings['connections'] = self.max_connections
            settings['chunk_size'] = 131072  # 128KB
        
        # تحسين حسب أداء الخادم
        if server_info:
            # استخدام البيانات التاريخية للخادم
            if 'optimal_connections' in server_info:
                settings['connections'] = min(settings['connections'], server_info['optimal_connections'])
            
            # تحسين المهلة الزمنية حسب أوقات الاستجابة
            if server_info['response_times']:
                avg_response = statistics.mean(server_info['response_times'])
                if avg_response > 5:  # استجابة بطيئة
                    settings['timeout'] = 60
                    settings['retry_delay'] = 3
                elif avg_response > 2:
                    settings['timeout'] = 45
                    settings['retry_delay'] = 2
        
        # تحسين حسب السرعة الحالية
        if current_speed > 0:
            # إذا كانت السرعة منخفضة، قلل عدد الاتصالات
            if current_speed < 100 * 1024:  # أقل من 100KB/s
                settings['connections'] = max(1, settings['connections'] // 2)
            # إذا كانت السرعة عالية، زد عدد الاتصالات
            elif current_speed > 1024 * 1024:  # أكثر من 1MB/s
                settings['connections'] = min(self.max_connections, settings['connections'] + 2)
        
        return settings
    
    def apply_machine_learning(self, url, settings):
        """تطبيق التعلم الآلي لتحسين الإعدادات"""
        from urllib.parse import urlparse
        
        domain = urlparse(url).netloc
        
        # البحث عن أنماط مشابهة في البيانات التاريخية
        if domain in self.learning_data.get('optimal_settings', {}):
            historical_settings = self.learning_data['optimal_settings'][domain]
            
            # دمج الإعدادات التاريخية مع الحالية
            if 'best_connections' in historical_settings:
                # استخدام متوسط مرجح
                historical_connections = historical_settings['best_connections']
                settings['connections'] = int((settings['connections'] + historical_connections) / 2)
            
            if 'best_chunk_size' in historical_settings:
                historical_chunk = historical_settings['best_chunk_size']
                settings['chunk_size'] = int((settings['chunk_size'] + historical_chunk) / 2)
        
        return settings
    
    def record_performance(self, url, settings, performance_data):
        """تسجيل أداء التحميل للتعلم"""
        if not self.learning_mode:
            return
        
        from urllib.parse import urlparse
        
        domain = urlparse(url).netloc
        
        # تسجيل الأداء
        performance_record = {
            'timestamp': datetime.now(),
            'settings': settings,
            'performance': performance_data,
            'success': performance_data.get('success', False),
            'speed': performance_data.get('average_speed', 0),
            'efficiency': performance_data.get('efficiency', 0)
        }
        
        self.performance_history.append(performance_record)
        
        # تحديث بيانات التعلم للخادم
        if domain not in self.learning_data.get('optimal_settings', {}):
            if 'optimal_settings' not in self.learning_data:
                self.learning_data['optimal_settings'] = {}
            self.learning_data['optimal_settings'][domain] = {}
        
        domain_data = self.learning_data['optimal_settings'][domain]
        
        # تحديث أفضل إعدادات إذا كان الأداء أفضل
        current_efficiency = performance_data.get('efficiency', 0)
        best_efficiency = domain_data.get('best_efficiency', 0)
        
        if current_efficiency > best_efficiency:
            domain_data.update({
                'best_efficiency': current_efficiency,
                'best_connections': settings['connections'],
                'best_chunk_size': settings['chunk_size'],
                'best_timeout': settings['timeout'],
                'last_updated': datetime.now().isoformat()
            })
            
            # حفظ البيانات
            self.save_learning_data()
    
    def update_optimization_parameters(self):
        """تحديث معاملات التحسين"""
        if len(self.performance_history) < 10:
            return
        
        recent_performance = list(self.performance_history)[-50:]  # آخر 50 تحميل
        
        # تحليل الأداء العام
        successful_downloads = [p for p in recent_performance if p['performance'].get('success', False)]
        
        if successful_downloads:
            # حساب متوسط السرعة
            avg_speeds = [p['performance']['speed'] for p in successful_downloads]
            overall_avg_speed = statistics.mean(avg_speeds)
            
            # تحليل أفضل إعدادات
            best_performance = max(successful_downloads, key=lambda x: x['performance'].get('efficiency', 0))
            best_settings = best_performance['settings']
            
            # تحديث الإعدادات المثلى العامة
            self.optimal_connections = best_settings['connections']
            self.optimal_chunk_size = best_settings['chunk_size']
    
    def get_acceleration_status(self):
        """الحصول على حالة التسريع"""
        return {
            'enabled': self.enabled,
            'learning_mode': self.learning_mode,
            'optimization_level': self.optimization_level,
            'optimal_connections': self.optimal_connections,
            'optimal_chunk_size': self.optimal_chunk_size,
            'performance_samples': len(self.performance_history),
            'servers_analyzed': len(self.server_performance),
            'network_quality': self.get_current_network_quality()
        }
    
    def get_current_network_quality(self):
        """الحصول على جودة الشبكة الحالية"""
        if len(self.network_conditions) < 2:
            return "unknown"
        
        # تحليل آخر قراءات الشبكة
        recent = list(self.network_conditions)[-5:]
        
        # حساب معدل الأخطاء
        total_errors = 0
        total_packets = 0
        
        for i in range(1, len(recent)):
            prev = recent[i-1]
            curr = recent[i]
            
            packets = (curr['packets_sent'] - prev['packets_sent']) + (curr['packets_recv'] - prev['packets_recv'])
            errors = (curr['errors_in'] - prev['errors_in']) + (curr['errors_out'] - prev['errors_out'])
            
            total_packets += packets
            total_errors += errors
        
        if total_packets > 0:
            error_rate = total_errors / total_packets
            
            if error_rate < 0.001:
                return "excellent"
            elif error_rate < 0.01:
                return "good"
            elif error_rate < 0.05:
                return "fair"
            else:
                return "poor"
        
        return "unknown"
    
    def reset_learning_data(self):
        """إعادة تعيين بيانات التعلم"""
        self.learning_data = {
            'optimal_settings': {},
            'server_patterns': {},
            'network_patterns': {},
            'user_preferences': {}
        }
        self.performance_history.clear()
        self.server_performance.clear()
        self.save_learning_data()
    
    def export_performance_data(self):
        """تصدير بيانات الأداء"""
        export_data = {
            'performance_history': list(self.performance_history),
            'server_performance': dict(self.server_performance),
            'learning_data': self.learning_data,
            'current_settings': {
                'optimal_connections': self.optimal_connections,
                'optimal_chunk_size': self.optimal_chunk_size,
                'optimization_level': self.optimization_level
            },
            'export_timestamp': datetime.now().isoformat()
        }
        
        filename = f"accelerator_data_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(export_data, f, ensure_ascii=False, indent=2, default=str)
            return filename
        except Exception as e:
            print(f"خطأ في تصدير بيانات الأداء: {e}")
            return None

# إنشاء مثيل عام لنظام التسريع الذكي
smart_accelerator = SmartAccelerator()

class NetworkMonitor:
    """مراقب الشبكة المتقدم"""

    def __init__(self):
        self.monitoring_active = True
        self.ping_history = deque(maxlen=100)
        self.bandwidth_history = deque(maxlen=100)
        self.connection_quality = "unknown"

        # بدء المراقبة
        self.start_network_monitoring()

    def start_network_monitoring(self):
        """بدء مراقبة الشبكة"""
        def monitor_loop():
            while self.monitoring_active:
                try:
                    self.check_connection_quality()
                    self.measure_bandwidth()
                    time.sleep(10)  # فحص كل 10 ثوان
                except Exception as e:
                    print(f"خطأ في مراقبة الشبكة: {e}")
                    time.sleep(30)

        monitor_thread = threading.Thread(target=monitor_loop, daemon=True)
        monitor_thread.start()

    def check_connection_quality(self):
        """فحص جودة الاتصال"""
        try:
            import subprocess
            import platform

            # تحديد أمر ping حسب نظام التشغيل
            if platform.system().lower() == "windows":
                cmd = ["ping", "-n", "1", "8.8.8.8"]
            else:
                cmd = ["ping", "-c", "1", "8.8.8.8"]

            start_time = time.time()
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=5)
            end_time = time.time()

            if result.returncode == 0:
                ping_time = (end_time - start_time) * 1000  # تحويل إلى ميلي ثانية
                self.ping_history.append({
                    'timestamp': datetime.now(),
                    'ping_time': ping_time,
                    'success': True
                })

                # تحديد جودة الاتصال
                if ping_time < 50:
                    self.connection_quality = "excellent"
                elif ping_time < 100:
                    self.connection_quality = "good"
                elif ping_time < 200:
                    self.connection_quality = "fair"
                else:
                    self.connection_quality = "poor"
            else:
                self.ping_history.append({
                    'timestamp': datetime.now(),
                    'ping_time': None,
                    'success': False
                })
                self.connection_quality = "disconnected"

        except Exception as e:
            print(f"خطأ في فحص جودة الاتصال: {e}")

    def measure_bandwidth(self):
        """قياس عرض النطاق المتاح"""
        try:
            # قياس بسيط لعرض النطاق باستخدام تحميل ملف صغير
            import requests

            test_url = "http://httpbin.org/bytes/1024"  # 1KB test file

            start_time = time.time()
            response = requests.get(test_url, timeout=5)
            end_time = time.time()

            if response.status_code == 200:
                download_time = end_time - start_time
                file_size = len(response.content)
                bandwidth = (file_size * 8) / download_time  # bits per second

                self.bandwidth_history.append({
                    'timestamp': datetime.now(),
                    'bandwidth': bandwidth,
                    'download_time': download_time
                })

        except Exception as e:
            print(f"خطأ في قياس عرض النطاق: {e}")

    def get_network_status(self):
        """الحصول على حالة الشبكة"""
        avg_ping = None
        avg_bandwidth = None

        if self.ping_history:
            successful_pings = [p['ping_time'] for p in self.ping_history if p['success'] and p['ping_time']]
            if successful_pings:
                avg_ping = statistics.mean(successful_pings)

        if self.bandwidth_history:
            bandwidths = [b['bandwidth'] for b in self.bandwidth_history]
            if bandwidths:
                avg_bandwidth = statistics.mean(bandwidths)

        return {
            'connection_quality': self.connection_quality,
            'average_ping': avg_ping,
            'average_bandwidth': avg_bandwidth,
            'ping_samples': len(self.ping_history),
            'bandwidth_samples': len(self.bandwidth_history)
        }

# إنشاء مثيل مراقب الشبكة
network_monitor = NetworkMonitor()
