# Internet Download Manager - مدير التحميل المتقدم

برنامج تحميل متقدم مع واجهة رسومية حديثة مشابهة لبرنامج Internet Download Manager (IDM)، مصمم لتحميل الملفات من الإنترنت بسهولة وفعالية عالية.

## 🎨 التصميم الجديد

تم تحسين الواجهة لتصبح شبيهة ببرنامج IDM الشهير مع:
- **تصميم حديث ومتطور** مع ألوان وخطوط احترافية
- **شريط أدوات متقدم** مع أيقونات وأزرار سريعة
- **شريط جانبي للفئات** لتنظيم التحميلات
- **شريط حالة سفلي** يعرض معلومات النظام
- **جدول تحميلات متقدم** مع ألوان مميزة للحالات

## 🌟 المميزات الأساسية

### واجهة المستخدم المحسنة
- ✅ تصميم شبيه ببرنامج IDM الأصلي
- ✅ واجهة رسومية باللغة العربية مع دعم كامل
- ✅ ألوان وخطوط احترافية (Segoe UI)
- ✅ شريط أدوات متقدم مع أيقونات
- ✅ شريط جانبي لتصنيف التحميلات
- ✅ شريط حالة سفلي مع معلومات النظام

### مميزات التحميل
- ✅ تحميل الملفات من الروابط المباشرة
- ✅ شريط تقدم متقدم مع تفاصيل شاملة
- ✅ عرض سرعة التحميل والوقت المتبقي
- ✅ إمكانية إيقاف وإستئناف التحميل
- ✅ اختيار مجلد الحفظ المخصص
- ✅ تسمية الملفات المخصصة
- ✅ سجل التحميلات مع التفاصيل الكاملة

### المميزات المتقدمة الجديدة
- 🔄 استئناف التحميل المتقطع مع دعم كامل
- 📊 عرض تفصيلي للإحصائيات والتقارير
- 💾 حفظ سجل التحميلات تلقائياً مع تصنيف
- 🛡️ التحقق من صحة الروابط قبل التحميل
- ⚡ تحميل متعدد الخيوط لتحسين الأداء
- 🎯 تصنيف التحميلات (نشطة، مكتملة، متوقفة، فاشلة)
- 🖱️ قائمة سياق متقدمة (كليك يمين)
- ⌨️ اختصارات لوحة المفاتيح
- 🎨 ثيم متقدم مع ألوان مخصصة للحالات
- 📈 شريط حالة يعرض معلومات النظام

## متطلبات التشغيل

### متطلبات النظام
- Python 3.7 أو أحدث
- نظام التشغيل: Windows, macOS, أو Linux
- اتصال بالإنترنت

### المكتبات المطلوبة
```
requests>=2.31.0
tqdm>=4.65.0
urllib3>=2.0.0
```

## 🚀 التثبيت والتشغيل

### الطريقة الأولى: التشغيل السريع
```bash
# تشغيل الإصدار المحسن بتصميم IDM
python start.py
```

### الطريقة الثانية: التشغيل اليدوي
```bash
# 1. تثبيت المتطلبات
pip install -r requirements.txt

# 2. تشغيل الإصدار الأساسي
python main.py

# أو تشغيل الإصدار المحسن
python main_idm.py
```

### الطريقة الثالثة: ملفات التشغيل
```bash
# Windows
run.bat

# Linux/Mac
./run.sh
```

## كيفية الاستخدام

### الخطوات الأساسية
1. **إدخال الرابط**: ضع رابط الملف المراد تحميله في حقل "رابط التحميل"
2. **اختيار المجلد**: حدد مجلد الحفظ أو استخدم المجلد الافتراضي
3. **تسمية الملف** (اختياري): يمكنك تغيير اسم الملف
4. **بدء التحميل**: اضغط على زر "بدء التحميل"

### أزرار التحكم
- **بدء التحميل**: لبدء عملية التحميل
- **إيقاف مؤقت**: لإيقاف التحميل مؤقتاً
- **استئناف**: لاستكمال التحميل المتوقف
- **إيقاف**: لإلغاء التحميل نهائياً

### معلومات التقدم
- شريط التقدم يعرض النسبة المئوية للتحميل
- عرض الحجم المحمل والحجم الإجمالي
- سرعة التحميل الحالية
- الوقت المتبقي المتوقع

## الملفات المكونة للبرنامج

### الملفات الأساسية
- `main.py`: الملف الرئيسي وواجهة المستخدم
- `downloader.py`: وحدة التحميل الأساسية
- `requirements.txt`: قائمة المكتبات المطلوبة

### الملفات المساعدة
- `download_history.json`: سجل التحميلات (ينشأ تلقائياً)
- `README.md`: دليل الاستخدام

## استكشاف الأخطاء

### المشاكل الشائعة وحلولها

**1. خطأ في تثبيت المكتبات**
```bash
pip install --upgrade pip
pip install -r requirements.txt
```

**2. مشكلة في الرابط**
- تأكد من صحة الرابط
- تأكد من أن الرابط يشير إلى ملف مباشر

**3. مشكلة في الصلاحيات**
- تأكد من صلاحيات الكتابة في مجلد الحفظ
- جرب تشغيل البرنامج كمدير

**4. مشكلة في الاتصال**
- تحقق من اتصال الإنترنت
- تأكد من عدم حجب الرابط من قبل جدار الحماية

## الدعم والتطوير

### التحديثات المستقبلية
- [ ] دعم تحميل متعدد الملفات
- [ ] دعم قوائم التحميل
- [ ] دعم المواقع المحمية بكلمة مرور
- [ ] دعم البروكسي
- [ ] واجهة ويب

### المساهمة
نرحب بالمساهمات لتطوير البرنامج. يمكنك:
- الإبلاغ عن الأخطاء
- اقتراح مميزات جديدة
- تحسين الكود
- ترجمة البرنامج

## الترخيص

هذا البرنامج مجاني ومفتوح المصدر للاستخدام الشخصي والتعليمي.

## معلومات الاتصال

للدعم الفني أو الاستفسارات، يمكنك التواصل من خلال:
- إنشاء issue في المستودع
- التواصل المباشر

---

**ملاحظة**: تأكد من احترام حقوق الطبع والنشر عند تحميل الملفات من الإنترنت.
