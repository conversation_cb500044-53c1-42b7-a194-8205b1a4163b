"""
مدير اللغات - Language Manager
إدارة اللغات مع دعم العربية والإنجليزية
"""

import json
import os
from datetime import datetime

class LanguageManager:
    """مدير اللغات"""
    
    def __init__(self):
        self.current_language = "ar"  # العربية افتراضياً
        self.languages = self.get_available_languages()
        self.load_language_preference()
    
    def get_available_languages(self):
        """الحصول على اللغات المتاحة"""
        return {
            "ar": {
                "name": "العربية",
                "code": "ar",
                "direction": "rtl",
                "translations": {
                    # عام
                    "app_title": "Internet Download Manager - مدير التحميل",
                    "version": "الإصدار",
                    "loading": "جاري التحميل...",
                    "please_wait": "يرجى الانتظار...",
                    "error": "خطأ",
                    "warning": "تحذير",
                    "info": "معلومات",
                    "success": "نجح",
                    "cancel": "إلغاء",
                    "ok": "موافق",
                    "yes": "نعم",
                    "no": "لا",
                    "close": "إغلاق",
                    "save": "حفظ",
                    "delete": "حذف",
                    "edit": "تعديل",
                    "copy": "نسخ",
                    "paste": "لصق",
                    "cut": "قص",
                    "select_all": "تحديد الكل",
                    
                    # القوائم
                    "file": "ملف",
                    "edit_menu": "تحرير",
                    "view": "عرض",
                    "tools": "أدوات",
                    "help": "مساعدة",
                    "settings": "إعدادات",
                    "preferences": "تفضيلات",
                    "about": "حول",
                    "exit": "خروج",
                    
                    # التحميل
                    "new_download": "تحميل جديد",
                    "download_url": "رابط التحميل",
                    "save_to": "حفظ في",
                    "filename": "اسم الملف",
                    "file_size": "حجم الملف",
                    "download_speed": "سرعة التحميل",
                    "time_remaining": "الوقت المتبقي",
                    "progress": "التقدم",
                    "status": "الحالة",
                    "priority": "الأولوية",
                    "connections": "الاتصالات",
                    "start_download": "بدء التحميل",
                    "pause_download": "إيقاف مؤقت",
                    "resume_download": "استئناف",
                    "stop_download": "إيقاف",
                    "restart_download": "إعادة بدء",
                    "remove_download": "إزالة التحميل",
                    "delete_download": "حذف التحميل",
                    "download_completed": "اكتمل التحميل",
                    "download_failed": "فشل التحميل",
                    "download_cancelled": "تم إلغاء التحميل",
                    
                    # حالات التحميل
                    "pending": "في الانتظار",
                    "downloading": "جاري التحميل",
                    "paused": "متوقف مؤقتاً",
                    "completed": "مكتمل",
                    "failed": "فشل",
                    "cancelled": "ملغي",
                    
                    # الأولويات
                    "priority_low": "منخفضة",
                    "priority_normal": "عادية",
                    "priority_high": "عالية",
                    "priority_urgent": "عاجلة",
                    
                    # الجدولة
                    "schedule": "جدولة",
                    "schedule_immediate": "فوري",
                    "schedule_delayed": "مؤجل",
                    "schedule_at_time": "في وقت محدد",
                    "schedule_recurring": "متكرر",
                    "schedule_conditional": "مشروط",
                    
                    # الإعدادات
                    "general_settings": "إعدادات عامة",
                    "download_settings": "إعدادات التحميل",
                    "network_settings": "إعدادات الشبكة",
                    "interface_settings": "إعدادات الواجهة",
                    "advanced_settings": "إعدادات متقدمة",
                    "max_connections": "عدد الاتصالات القصوى",
                    "max_concurrent": "عدد التحميلات المتزامنة",
                    "bandwidth_limit": "حد عرض النطاق",
                    "auto_retry": "إعادة المحاولة التلقائية",
                    "notifications": "الإشعارات",
                    "theme": "المظهر",
                    "language": "اللغة",
                    
                    # الأزرار
                    "browse": "تصفح",
                    "check_url": "فحص الرابط",
                    "preview": "معاينة",
                    "apply": "تطبيق",
                    "reset": "إعادة تعيين",
                    "import": "استيراد",
                    "export": "تصدير",
                    "backup": "نسخ احتياطي",
                    "restore": "استعادة",
                    
                    # الرسائل
                    "invalid_url": "رابط غير صحيح",
                    "file_exists": "الملف موجود بالفعل",
                    "insufficient_space": "مساحة غير كافية",
                    "network_error": "خطأ في الشبكة",
                    "permission_denied": "ليس لديك صلاحية",
                    "download_complete_msg": "تم تحميل الملف بنجاح!",
                    "download_failed_msg": "فشل في تحميل الملف",
                    "confirm_delete": "هل تريد حذف هذا العنصر؟",
                    "confirm_delete_all": "هل تريد حذف جميع العناصر؟",
                    "operation_cancelled": "تم إلغاء العملية",
                    "operation_completed": "تمت العملية بنجاح",
                    
                    # الإحصائيات
                    "statistics": "إحصائيات",
                    "total_downloads": "إجمالي التحميلات",
                    "active_downloads": "التحميلات النشطة",
                    "completed_downloads": "التحميلات المكتملة",
                    "failed_downloads": "التحميلات الفاشلة",
                    "total_size": "الحجم الإجمالي",
                    "average_speed": "متوسط السرعة",
                    "session_time": "وقت الجلسة",
                    
                    # الفئات
                    "all_downloads": "جميع التحميلات",
                    "active": "نشطة",
                    "completed_category": "مكتملة",
                    "failed_category": "فاشلة",
                    "scheduled": "مجدولة",
                    
                    # أخرى
                    "drag_drop_hint": "اسحب وأفلت الروابط هنا",
                    "no_downloads": "لا توجد تحميلات",
                    "loading_downloads": "جاري تحميل قائمة التحميلات...",
                    "checking_updates": "فحص التحديثات...",
                    "update_available": "يتوفر تحديث جديد",
                    "up_to_date": "البرنامج محدث",
                }
            },
            
            "en": {
                "name": "English",
                "code": "en",
                "direction": "ltr",
                "translations": {
                    # General
                    "app_title": "Internet Download Manager",
                    "version": "Version",
                    "loading": "Loading...",
                    "please_wait": "Please wait...",
                    "error": "Error",
                    "warning": "Warning",
                    "info": "Information",
                    "success": "Success",
                    "cancel": "Cancel",
                    "ok": "OK",
                    "yes": "Yes",
                    "no": "No",
                    "close": "Close",
                    "save": "Save",
                    "delete": "Delete",
                    "edit": "Edit",
                    "copy": "Copy",
                    "paste": "Paste",
                    "cut": "Cut",
                    "select_all": "Select All",
                    
                    # Menus
                    "file": "File",
                    "edit_menu": "Edit",
                    "view": "View",
                    "tools": "Tools",
                    "help": "Help",
                    "settings": "Settings",
                    "preferences": "Preferences",
                    "about": "About",
                    "exit": "Exit",
                    
                    # Download
                    "new_download": "New Download",
                    "download_url": "Download URL",
                    "save_to": "Save to",
                    "filename": "Filename",
                    "file_size": "File Size",
                    "download_speed": "Download Speed",
                    "time_remaining": "Time Remaining",
                    "progress": "Progress",
                    "status": "Status",
                    "priority": "Priority",
                    "connections": "Connections",
                    "start_download": "Start Download",
                    "pause_download": "Pause",
                    "resume_download": "Resume",
                    "stop_download": "Stop",
                    "restart_download": "Restart",
                    "remove_download": "Remove Download",
                    "delete_download": "Delete Download",
                    "download_completed": "Download Completed",
                    "download_failed": "Download Failed",
                    "download_cancelled": "Download Cancelled",
                    
                    # Download Status
                    "pending": "Pending",
                    "downloading": "Downloading",
                    "paused": "Paused",
                    "completed": "Completed",
                    "failed": "Failed",
                    "cancelled": "Cancelled",
                    
                    # Priorities
                    "priority_low": "Low",
                    "priority_normal": "Normal",
                    "priority_high": "High",
                    "priority_urgent": "Urgent",
                    
                    # Scheduling
                    "schedule": "Schedule",
                    "schedule_immediate": "Immediate",
                    "schedule_delayed": "Delayed",
                    "schedule_at_time": "At Specific Time",
                    "schedule_recurring": "Recurring",
                    "schedule_conditional": "Conditional",
                    
                    # Settings
                    "general_settings": "General Settings",
                    "download_settings": "Download Settings",
                    "network_settings": "Network Settings",
                    "interface_settings": "Interface Settings",
                    "advanced_settings": "Advanced Settings",
                    "max_connections": "Max Connections",
                    "max_concurrent": "Max Concurrent Downloads",
                    "bandwidth_limit": "Bandwidth Limit",
                    "auto_retry": "Auto Retry",
                    "notifications": "Notifications",
                    "theme": "Theme",
                    "language": "Language",
                    
                    # Buttons
                    "browse": "Browse",
                    "check_url": "Check URL",
                    "preview": "Preview",
                    "apply": "Apply",
                    "reset": "Reset",
                    "import": "Import",
                    "export": "Export",
                    "backup": "Backup",
                    "restore": "Restore",
                    
                    # Messages
                    "invalid_url": "Invalid URL",
                    "file_exists": "File already exists",
                    "insufficient_space": "Insufficient disk space",
                    "network_error": "Network error",
                    "permission_denied": "Permission denied",
                    "download_complete_msg": "File downloaded successfully!",
                    "download_failed_msg": "Failed to download file",
                    "confirm_delete": "Do you want to delete this item?",
                    "confirm_delete_all": "Do you want to delete all items?",
                    "operation_cancelled": "Operation cancelled",
                    "operation_completed": "Operation completed successfully",
                    
                    # Statistics
                    "statistics": "Statistics",
                    "total_downloads": "Total Downloads",
                    "active_downloads": "Active Downloads",
                    "completed_downloads": "Completed Downloads",
                    "failed_downloads": "Failed Downloads",
                    "total_size": "Total Size",
                    "average_speed": "Average Speed",
                    "session_time": "Session Time",
                    
                    # Categories
                    "all_downloads": "All Downloads",
                    "active": "Active",
                    "completed_category": "Completed",
                    "failed_category": "Failed",
                    "scheduled": "Scheduled",
                    
                    # Other
                    "drag_drop_hint": "Drag and drop URLs here",
                    "no_downloads": "No downloads",
                    "loading_downloads": "Loading downloads list...",
                    "checking_updates": "Checking for updates...",
                    "update_available": "Update available",
                    "up_to_date": "Up to date",
                }
            }
        }
    
    def get_current_language(self):
        """الحصول على اللغة الحالية"""
        return self.languages.get(self.current_language, self.languages["ar"])
    
    def set_language(self, language_code):
        """تعيين لغة جديدة"""
        if language_code in self.languages:
            self.current_language = language_code
            self.save_language_preference()
            return True
        return False
    
    def get_text(self, key, default=None):
        """الحصول على النص المترجم"""
        current_lang = self.get_current_language()
        translations = current_lang.get("translations", {})
        return translations.get(key, default or key)
    
    def get_language_names(self):
        """الحصول على أسماء اللغات المتاحة"""
        return {code: lang["name"] for code, lang in self.languages.items()}
    
    def get_language_direction(self):
        """الحصول على اتجاه النص للغة الحالية"""
        current_lang = self.get_current_language()
        return current_lang.get("direction", "ltr")
    
    def is_rtl(self):
        """التحقق من كون اللغة من اليمين لليسار"""
        return self.get_language_direction() == "rtl"
    
    def save_language_preference(self, filename="language_settings.json"):
        """حفظ تفضيلات اللغة"""
        try:
            settings = {
                'current_language': self.current_language,
                'saved_at': datetime.now().isoformat()
            }
            
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(settings, f, ensure_ascii=False, indent=2)
            
            return True
        except Exception as e:
            print(f"خطأ في حفظ تفضيلات اللغة: {e}")
            return False
    
    def load_language_preference(self, filename="language_settings.json"):
        """تحميل تفضيلات اللغة"""
        try:
            if os.path.exists(filename):
                with open(filename, 'r', encoding='utf-8') as f:
                    settings = json.load(f)
                
                language_code = settings.get('current_language', 'ar')
                if language_code in self.languages:
                    self.current_language = language_code
                    return True
            
            return False
        except Exception as e:
            print(f"خطأ في تحميل تفضيلات اللغة: {e}")
            return False
    
    def format_file_size(self, size_bytes):
        """تنسيق حجم الملف حسب اللغة"""
        if size_bytes == 0:
            return self.get_text("no_size", "0 B")
        
        size_names = ["B", "KB", "MB", "GB", "TB"]
        i = 0
        while size_bytes >= 1024 and i < len(size_names) - 1:
            size_bytes /= 1024.0
            i += 1
        
        if self.is_rtl():
            return f"{size_bytes:.2f} {size_names[i]}"
        else:
            return f"{size_bytes:.2f} {size_names[i]}"
    
    def format_speed(self, speed_bps):
        """تنسيق السرعة حسب اللغة"""
        return self.format_file_size(speed_bps) + "/s"
    
    def format_time(self, seconds):
        """تنسيق الوقت حسب اللغة"""
        if seconds < 60:
            unit = self.get_text("seconds", "seconds") if not self.is_rtl() else "ثانية"
            return f"{int(seconds)} {unit}"
        elif seconds < 3600:
            unit = self.get_text("minutes", "minutes") if not self.is_rtl() else "دقيقة"
            return f"{int(seconds / 60)} {unit}"
        else:
            unit = self.get_text("hours", "hours") if not self.is_rtl() else "ساعة"
            return f"{int(seconds / 3600)} {unit}"

# إنشاء مثيل عام لمدير اللغات
language_manager = LanguageManager()
