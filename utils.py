"""
وحدة الأدوات المساعدة لبرنامج مدير التحميل
Utility functions for Download Manager
"""

import os
import re
import hashlib
import mimetypes
from urllib.parse import urlparse, unquote
from pathlib import Path
import requests
from config import Config

class FileUtils:
    """أدوات التعامل مع الملفات"""
    
    @staticmethod
    def get_file_size_formatted(size_bytes):
        """تحويل حجم الملف إلى تنسيق قابل للقراءة"""
        if size_bytes == 0:
            return "0 B"
        
        size_names = ["B", "KB", "MB", "GB", "TB", "PB"]
        i = 0
        while size_bytes >= 1024 and i < len(size_names) - 1:
            size_bytes /= 1024.0
            i += 1
        
        return f"{size_bytes:.2f} {size_names[i]}"
    
    @staticmethod
    def get_file_extension(filename):
        """الحصول على امتداد الملف"""
        return Path(filename).suffix.lower()
    
    @staticmethod
    def is_valid_filename(filename):
        """التحقق من صحة اسم الملف"""
        if not filename or filename.strip() == "":
            return False
        
        # التحقق من الأحرف غير المسموحة
        invalid_chars = '<>:"/\\|?*'
        return not any(char in filename for char in invalid_chars)
    
    @staticmethod
    def sanitize_filename(filename):
        """تنظيف اسم الملف"""
        if not filename:
            return "downloaded_file"
        
        # إزالة الأحرف غير المسموحة
        filename = re.sub(r'[<>:"/\\|?*]', '_', filename)
        
        # إزالة المسافات الزائدة والنقاط في البداية والنهاية
        filename = filename.strip('. ')
        
        # التأكد من عدم كون الاسم فارغاً
        return filename if filename else "downloaded_file"
    
    @staticmethod
    def get_unique_filename(directory, filename):
        """الحصول على اسم ملف فريد في المجلد"""
        base_path = Path(directory) / filename
        
        if not base_path.exists():
            return filename
        
        # إضافة رقم للاسم
        name = base_path.stem
        extension = base_path.suffix
        counter = 1
        
        while True:
            new_filename = f"{name} ({counter}){extension}"
            new_path = Path(directory) / new_filename
            
            if not new_path.exists():
                return new_filename
            
            counter += 1
    
    @staticmethod
    def calculate_file_hash(file_path, algorithm='md5'):
        """حساب hash للملف"""
        hash_func = hashlib.new(algorithm)
        
        try:
            with open(file_path, 'rb') as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    hash_func.update(chunk)
            return hash_func.hexdigest()
        except:
            return None

class URLUtils:
    """أدوات التعامل مع الروابط"""
    
    @staticmethod
    def is_valid_url(url):
        """التحقق من صحة الرابط"""
        try:
            result = urlparse(url)
            return all([result.scheme, result.netloc])
        except:
            return False
    
    @staticmethod
    def get_filename_from_url(url):
        """استخراج اسم الملف من الرابط"""
        try:
            parsed_url = urlparse(url)
            filename = unquote(os.path.basename(parsed_url.path))
            
            # إذا لم يكن هناك اسم ملف في الرابط
            if not filename or '.' not in filename:
                # محاولة الحصول على اسم الملف من headers
                try:
                    response = requests.head(url, allow_redirects=True, timeout=10)
                    content_disposition = response.headers.get('content-disposition')
                    if content_disposition:
                        filename_match = re.findall('filename=(.+)', content_disposition)
                        if filename_match:
                            filename = filename_match[0].strip('"\'')
                except:
                    pass
            
            # إذا لم نحصل على اسم ملف، استخدم اسماً افتراضياً
            if not filename or '.' not in filename:
                filename = "downloaded_file"
            
            return FileUtils.sanitize_filename(filename)
        except:
            return "downloaded_file"
    
    @staticmethod
    def get_file_info(url):
        """الحصول على معلومات الملف من الرابط"""
        try:
            response = requests.head(url, allow_redirects=True, timeout=10)
            
            info = {
                'size': 0,
                'filename': URLUtils.get_filename_from_url(url),
                'content_type': 'application/octet-stream',
                'supports_resume': False,
                'last_modified': None
            }
            
            # حجم الملف
            if 'content-length' in response.headers:
                info['size'] = int(response.headers['content-length'])
            
            # نوع المحتوى
            if 'content-type' in response.headers:
                info['content_type'] = response.headers['content-type']
            
            # دعم الاستئناف
            if 'accept-ranges' in response.headers:
                info['supports_resume'] = response.headers['accept-ranges'] == 'bytes'
            
            # تاريخ آخر تعديل
            if 'last-modified' in response.headers:
                info['last_modified'] = response.headers['last-modified']
            
            return info
        except:
            return {
                'size': 0,
                'filename': URLUtils.get_filename_from_url(url),
                'content_type': 'application/octet-stream',
                'supports_resume': False,
                'last_modified': None
            }
    
    @staticmethod
    def normalize_url(url):
        """تطبيع الرابط"""
        url = url.strip()
        
        # إضافة http إذا لم يكن موجوداً
        if not url.startswith(('http://', 'https://')):
            url = 'http://' + url
        
        return url

class TimeUtils:
    """أدوات التعامل مع الوقت"""
    
    @staticmethod
    def format_duration(seconds):
        """تحويل الثواني إلى تنسيق قابل للقراءة"""
        if seconds < 0:
            return "غير محدد"
        
        if seconds < 60:
            return f"{int(seconds)} ثانية"
        elif seconds < 3600:
            minutes = int(seconds / 60)
            return f"{minutes} دقيقة"
        elif seconds < 86400:
            hours = int(seconds / 3600)
            minutes = int((seconds % 3600) / 60)
            return f"{hours} ساعة و {minutes} دقيقة"
        else:
            days = int(seconds / 86400)
            hours = int((seconds % 86400) / 3600)
            return f"{days} يوم و {hours} ساعة"
    
    @staticmethod
    def calculate_eta(downloaded, total, elapsed_time):
        """حساب الوقت المتبقي المتوقع"""
        if downloaded <= 0 or elapsed_time <= 0 or total <= downloaded:
            return "غير محدد"
        
        speed = downloaded / elapsed_time
        remaining_bytes = total - downloaded
        eta_seconds = remaining_bytes / speed
        
        return TimeUtils.format_duration(eta_seconds)
    
    @staticmethod
    def calculate_speed(bytes_downloaded, elapsed_time):
        """حساب سرعة التحميل"""
        if elapsed_time <= 0:
            return "0 B/s"
        
        speed = bytes_downloaded / elapsed_time
        return FileUtils.get_file_size_formatted(speed) + "/s"

class ValidationUtils:
    """أدوات التحقق والتصديق"""
    
    @staticmethod
    def validate_download_path(path):
        """التحقق من صحة مسار التحميل"""
        try:
            path_obj = Path(path)
            
            # التحقق من وجود المسار
            if not path_obj.exists():
                # محاولة إنشاء المسار
                path_obj.mkdir(parents=True, exist_ok=True)
            
            # التحقق من صلاحيات الكتابة
            test_file = path_obj / "test_write_permission.tmp"
            try:
                test_file.touch()
                test_file.unlink()
                return True, "مسار صحيح"
            except:
                return False, "ليس لديك صلاحية الكتابة في هذا المجلد"
                
        except Exception as e:
            return False, f"مسار غير صحيح: {str(e)}"
    
    @staticmethod
    def validate_url_accessibility(url):
        """التحقق من إمكانية الوصول للرابط"""
        try:
            response = requests.head(url, timeout=10, allow_redirects=True)
            
            if response.status_code == 200:
                return True, "الرابط متاح"
            elif response.status_code == 404:
                return False, "الملف غير موجود (404)"
            elif response.status_code == 403:
                return False, "ممنوع الوصول (403)"
            else:
                return False, f"خطأ في الخادم ({response.status_code})"
                
        except requests.exceptions.Timeout:
            return False, "انتهت مهلة الاتصال"
        except requests.exceptions.ConnectionError:
            return False, "خطأ في الاتصال"
        except Exception as e:
            return False, f"خطأ: {str(e)}"

# دوال مساعدة سريعة
def format_size(size_bytes):
    """دالة سريعة لتحويل الحجم"""
    return FileUtils.get_file_size_formatted(size_bytes)

def format_time(seconds):
    """دالة سريعة لتحويل الوقت"""
    return TimeUtils.format_duration(seconds)

def clean_filename(filename):
    """دالة سريعة لتنظيف اسم الملف"""
    return FileUtils.sanitize_filename(filename)
