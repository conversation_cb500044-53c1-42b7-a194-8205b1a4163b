"""
نافذة إعدادات التسريع الذكي - Smart Accelerator Settings Dialog
إدارة وتكوين نظام التسريع الذكي
"""

import tkinter as tk
from tkinter import ttk, messagebox
from datetime import datetime
from theme_manager import theme_manager
from language_manager import language_manager

class AcceleratorSettingsDialog:
    """نافذة إعدادات التسريع الذكي"""
    
    def __init__(self, parent, smart_accelerator):
        self.parent = parent
        self.smart_accelerator = smart_accelerator
        
        # إنشاء النافذة
        self.dialog = tk.Toplevel(parent)
        self.dialog.title(language_manager.get_text("smart_accelerator_settings", "إعدادات التسريع الذكي"))
        self.dialog.geometry("800x600")
        self.dialog.resizable(True, True)
        self.dialog.transient(parent)
        self.dialog.grab_set()
        
        # الحصول على الثيم الحالي
        self.colors = theme_manager.get_current_theme()
        
        # تطبيق الثيم على النافذة
        theme_manager.apply_theme_to_widget(self.dialog, "main_window")
        
        # إعداد الواجهة
        self.setup_ui()
        
        # تحميل الإعدادات الحالية
        self.load_current_settings()
        
        # توسيط النافذة
        self.center_window()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # الإطار الرئيسي
        main_frame = tk.Frame(self.dialog)
        main_frame.pack(fill='both', expand=True, padx=10, pady=10)
        theme_manager.apply_theme_to_widget(main_frame, "frame")
        
        # شريط الأدوات العلوي
        self.create_toolbar(main_frame)
        
        # دفتر التبويبات
        self.notebook = ttk.Notebook(main_frame)
        self.notebook.pack(fill='both', expand=True, pady=(10, 0))
        
        # تبويب الإعدادات العامة
        self.create_general_tab()
        
        # تبويب الخوارزميات
        self.create_algorithms_tab()
        
        # تبويب الحالة والإحصائيات
        self.create_status_tab()
        
        # تبويب الأداء
        self.create_performance_tab()
        
        # أزرار التحكم
        self.create_control_buttons(main_frame)
    
    def create_toolbar(self, parent):
        """إنشاء شريط الأدوات"""
        toolbar_frame = tk.Frame(parent)
        toolbar_frame.pack(fill='x', pady=(0, 10))
        theme_manager.apply_theme_to_widget(toolbar_frame, "toolbar")
        
        # عنوان
        title_label = tk.Label(toolbar_frame, 
                              text="⚡ " + language_manager.get_text("smart_accelerator", "التسريع الذكي"),
                              font=('Segoe UI', 12, 'bold'))
        title_label.pack(side='left', padx=10, pady=10)
        theme_manager.apply_theme_to_widget(title_label, "label")
        
        # حالة التسريع
        status_frame = tk.Frame(toolbar_frame)
        status_frame.pack(side='right', padx=10, pady=5)
        theme_manager.apply_theme_to_widget(status_frame, "frame")
        
        status_text = "مفعل" if self.smart_accelerator.enabled else "معطل"
        status_color = self.colors['success'] if self.smart_accelerator.enabled else self.colors['error']
        
        status_label = tk.Label(status_frame, 
                               text=f"الحالة: {status_text}",
                               fg=status_color,
                               font=('Segoe UI', 10, 'bold'))
        status_label.pack()
        theme_manager.apply_theme_to_widget(status_label, "label")
    
    def create_general_tab(self):
        """تبويب الإعدادات العامة"""
        general_frame = ttk.Frame(self.notebook)
        self.notebook.add(general_frame, text=language_manager.get_text("general", "عام"))
        
        # إعدادات التفعيل
        activation_frame = tk.LabelFrame(general_frame, 
                                        text=" " + language_manager.get_text("activation_settings", "إعدادات التفعيل") + " ",
                                        font=('Segoe UI', 10, 'bold'))
        activation_frame.pack(fill='x', padx=10, pady=10)
        theme_manager.apply_theme_to_widget(activation_frame, "labelframe")
        
        # تفعيل التسريع الذكي
        self.enabled_var = tk.BooleanVar(value=self.smart_accelerator.enabled)
        enabled_check = tk.Checkbutton(activation_frame, 
                                      text=language_manager.get_text("enable_smart_accelerator", "تفعيل التسريع الذكي"),
                                      variable=self.enabled_var,
                                      font=('Segoe UI', 10))
        enabled_check.pack(anchor='w', padx=15, pady=10)
        theme_manager.apply_theme_to_widget(enabled_check, "label")
        
        # تفعيل وضع التعلم
        self.learning_var = tk.BooleanVar(value=self.smart_accelerator.learning_mode)
        learning_check = tk.Checkbutton(activation_frame, 
                                       text=language_manager.get_text("enable_learning_mode", "تفعيل وضع التعلم"),
                                       variable=self.learning_var,
                                       font=('Segoe UI', 10))
        learning_check.pack(anchor='w', padx=15, pady=5)
        theme_manager.apply_theme_to_widget(learning_check, "label")
        
        # مستوى التحسين
        optimization_frame = tk.LabelFrame(general_frame, 
                                          text=" " + language_manager.get_text("optimization_level", "مستوى التحسين") + " ",
                                          font=('Segoe UI', 10, 'bold'))
        optimization_frame.pack(fill='x', padx=10, pady=10)
        theme_manager.apply_theme_to_widget(optimization_frame, "labelframe")
        
        self.optimization_var = tk.StringVar(value=self.smart_accelerator.optimization_level)
        
        # خيارات مستوى التحسين
        optimization_options = [
            ("conservative", "محافظ - أداء مستقر"),
            ("balanced", "متوازن - توازن بين الأداء والاستقرار"),
            ("aggressive", "قوي - أقصى أداء")
        ]
        
        for value, text in optimization_options:
            radio = tk.Radiobutton(optimization_frame, 
                                  text=text,
                                  variable=self.optimization_var,
                                  value=value,
                                  font=('Segoe UI', 9))
            radio.pack(anchor='w', padx=15, pady=5)
            theme_manager.apply_theme_to_widget(radio, "label")
        
        # إعدادات الاتصالات
        connections_frame = tk.LabelFrame(general_frame, 
                                         text=" " + language_manager.get_text("connection_settings", "إعدادات الاتصالات") + " ",
                                         font=('Segoe UI', 10, 'bold'))
        connections_frame.pack(fill='x', padx=10, pady=10)
        theme_manager.apply_theme_to_widget(connections_frame, "labelframe")
        
        # الحد الأدنى للاتصالات
        min_conn_frame = tk.Frame(connections_frame)
        min_conn_frame.pack(fill='x', padx=15, pady=5)
        theme_manager.apply_theme_to_widget(min_conn_frame, "frame")
        
        tk.Label(min_conn_frame, text="الحد الأدنى للاتصالات:").pack(side='left')
        theme_manager.apply_theme_to_widget(min_conn_frame.winfo_children()[-1], "label")
        
        self.min_connections_var = tk.IntVar(value=self.smart_accelerator.min_connections)
        min_conn_spin = tk.Spinbox(min_conn_frame, from_=1, to=8, textvariable=self.min_connections_var, width=10)
        min_conn_spin.pack(side='left', padx=10)
        
        # الحد الأقصى للاتصالات
        max_conn_frame = tk.Frame(connections_frame)
        max_conn_frame.pack(fill='x', padx=15, pady=5)
        theme_manager.apply_theme_to_widget(max_conn_frame, "frame")
        
        tk.Label(max_conn_frame, text="الحد الأقصى للاتصالات:").pack(side='left')
        theme_manager.apply_theme_to_widget(max_conn_frame.winfo_children()[-1], "label")
        
        self.max_connections_var = tk.IntVar(value=self.smart_accelerator.max_connections)
        max_conn_spin = tk.Spinbox(max_conn_frame, from_=2, to=32, textvariable=self.max_connections_var, width=10)
        max_conn_spin.pack(side='left', padx=10)
    
    def create_algorithms_tab(self):
        """تبويب الخوارزميات"""
        algorithms_frame = ttk.Frame(self.notebook)
        self.notebook.add(algorithms_frame, text=language_manager.get_text("algorithms", "خوارزميات"))
        
        # خوارزميات التحسين
        algo_frame = tk.LabelFrame(algorithms_frame, 
                                  text=" " + language_manager.get_text("optimization_algorithms", "خوارزميات التحسين") + " ",
                                  font=('Segoe UI', 10, 'bold'))
        algo_frame.pack(fill='x', padx=10, pady=10)
        theme_manager.apply_theme_to_widget(algo_frame, "labelframe")
        
        # متغيرات الخوارزميات
        self.algorithm_vars = {}
        
        algorithms = [
            ("adaptive_connections", "التكيف مع عدد الاتصالات", "تحسين عدد الاتصالات تلقائياً حسب الأداء"),
            ("dynamic_chunk_sizing", "تحجيم الأجزاء الديناميكي", "تحسين حجم أجزاء التحميل حسب السرعة"),
            ("server_load_balancing", "توازن حمولة الخادم", "توزيع الطلبات على خوادم متعددة"),
            ("network_condition_adaptation", "التكيف مع حالة الشبكة", "تحسين الإعدادات حسب جودة الشبكة"),
            ("predictive_optimization", "التحسين التنبؤي", "استخدام التعلم الآلي للتنبؤ بأفضل إعدادات")
        ]
        
        for algo_key, title, description in algorithms:
            # إطار الخوارزمية
            algo_item_frame = tk.Frame(algo_frame)
            algo_item_frame.pack(fill='x', padx=10, pady=5)
            theme_manager.apply_theme_to_widget(algo_item_frame, "frame")
            
            # متغير التفعيل
            var = tk.BooleanVar(value=self.smart_accelerator.algorithms.get(algo_key, True))
            self.algorithm_vars[algo_key] = var
            
            # مربع الاختيار
            check = tk.Checkbutton(algo_item_frame, 
                                  text=title,
                                  variable=var,
                                  font=('Segoe UI', 10, 'bold'))
            check.pack(anchor='w')
            theme_manager.apply_theme_to_widget(check, "label")
            
            # الوصف
            desc_label = tk.Label(algo_item_frame, 
                                 text=description,
                                 font=('Segoe UI', 8),
                                 wraplength=600)
            desc_label.pack(anchor='w', padx=20)
            theme_manager.apply_theme_to_widget(desc_label, "label_secondary")
    
    def create_status_tab(self):
        """تبويب الحالة والإحصائيات"""
        status_frame = ttk.Frame(self.notebook)
        self.notebook.add(status_frame, text=language_manager.get_text("status", "الحالة"))
        
        # حالة التسريع
        current_status_frame = tk.LabelFrame(status_frame, 
                                           text=" " + language_manager.get_text("current_status", "الحالة الحالية") + " ",
                                           font=('Segoe UI', 10, 'bold'))
        current_status_frame.pack(fill='x', padx=10, pady=10)
        theme_manager.apply_theme_to_widget(current_status_frame, "labelframe")
        
        # نص الحالة
        self.status_text = tk.Text(current_status_frame, height=15, wrap='word')
        self.status_text.pack(fill='both', expand=True, padx=15, pady=15)
        theme_manager.apply_theme_to_widget(self.status_text, "entry")
        
        # أزرار إدارة البيانات
        data_buttons_frame = tk.Frame(status_frame)
        data_buttons_frame.pack(fill='x', padx=10, pady=10)
        theme_manager.apply_theme_to_widget(data_buttons_frame, "frame")
        
        # زر تصدير البيانات
        export_btn = tk.Button(data_buttons_frame, 
                              text="📤 " + language_manager.get_text("export_data", "تصدير البيانات"),
                              command=self.export_performance_data)
        export_btn.pack(side='left', padx=5)
        theme_manager.apply_theme_to_widget(export_btn, "button_secondary")
        
        # زر إعادة تعيين البيانات
        reset_btn = tk.Button(data_buttons_frame, 
                             text="🔄 " + language_manager.get_text("reset_data", "إعادة تعيين البيانات"),
                             command=self.reset_learning_data)
        reset_btn.pack(side='left', padx=5)
        theme_manager.apply_theme_to_widget(reset_btn, "button_warning")
        
        # تحديث الحالة
        self.update_status_display()
    
    def create_performance_tab(self):
        """تبويب الأداء"""
        performance_frame = ttk.Frame(self.notebook)
        self.notebook.add(performance_frame, text=language_manager.get_text("performance", "الأداء"))
        
        # مقاييس الأداء
        metrics_frame = tk.LabelFrame(performance_frame, 
                                     text=" " + language_manager.get_text("performance_metrics", "مقاييس الأداء") + " ",
                                     font=('Segoe UI', 10, 'bold'))
        metrics_frame.pack(fill='both', expand=True, padx=10, pady=10)
        theme_manager.apply_theme_to_widget(metrics_frame, "labelframe")
        
        # جدول مقاييس الأداء
        columns = ('المقياس', 'القيمة الحالية', 'المتوسط', 'الأفضل')
        self.performance_tree = ttk.Treeview(metrics_frame, columns=columns, show='headings', height=12)
        
        for col in columns:
            self.performance_tree.heading(col, text=col)
            self.performance_tree.column(col, width=150, minwidth=100)
        
        # شريط تمرير
        perf_scrollbar = ttk.Scrollbar(metrics_frame, orient='vertical', command=self.performance_tree.yview)
        self.performance_tree.configure(yscrollcommand=perf_scrollbar.set)
        
        # ترتيب العناصر
        self.performance_tree.pack(side='left', fill='both', expand=True, padx=15, pady=15)
        perf_scrollbar.pack(side='right', fill='y')
        
        # تحديث بيانات الأداء
        self.update_performance_display()
    
    def create_control_buttons(self, parent):
        """إنشاء أزرار التحكم"""
        buttons_frame = tk.Frame(parent)
        buttons_frame.pack(fill='x', pady=(10, 0))
        theme_manager.apply_theme_to_widget(buttons_frame, "frame")
        
        # زر حفظ
        save_btn = tk.Button(buttons_frame, 
                            text="💾 " + language_manager.get_text("save", "حفظ"),
                            command=self.save_settings)
        save_btn.pack(side='left', padx=5)
        theme_manager.apply_theme_to_widget(save_btn, "button_primary")
        
        # زر إعادة تحميل
        reload_btn = tk.Button(buttons_frame, 
                              text="🔄 " + language_manager.get_text("reload", "إعادة تحميل"),
                              command=self.load_current_settings)
        reload_btn.pack(side='left', padx=5)
        theme_manager.apply_theme_to_widget(reload_btn, "button_secondary")
        
        # زر إغلاق
        close_btn = tk.Button(buttons_frame, 
                             text="❌ " + language_manager.get_text("close", "إغلاق"),
                             command=self.close_dialog)
        close_btn.pack(side='right', padx=5)
        theme_manager.apply_theme_to_widget(close_btn, "button_secondary")
    
    def load_current_settings(self):
        """تحميل الإعدادات الحالية"""
        try:
            # تحديث المتغيرات
            self.enabled_var.set(self.smart_accelerator.enabled)
            self.learning_var.set(self.smart_accelerator.learning_mode)
            self.optimization_var.set(self.smart_accelerator.optimization_level)
            self.min_connections_var.set(self.smart_accelerator.min_connections)
            self.max_connections_var.set(self.smart_accelerator.max_connections)
            
            # تحديث متغيرات الخوارزميات
            for algo_key, var in self.algorithm_vars.items():
                var.set(self.smart_accelerator.algorithms.get(algo_key, True))
            
            # تحديث العروض
            self.update_status_display()
            self.update_performance_display()
            
        except Exception as e:
            messagebox.showerror(language_manager.get_text("error"), 
                               f"خطأ في تحميل الإعدادات:\n{str(e)}")
    
    def save_settings(self):
        """حفظ الإعدادات"""
        try:
            # حفظ الإعدادات العامة
            self.smart_accelerator.enabled = self.enabled_var.get()
            self.smart_accelerator.learning_mode = self.learning_var.get()
            self.smart_accelerator.optimization_level = self.optimization_var.get()
            self.smart_accelerator.min_connections = self.min_connections_var.get()
            self.smart_accelerator.max_connections = self.max_connections_var.get()
            
            # حفظ إعدادات الخوارزميات
            for algo_key, var in self.algorithm_vars.items():
                self.smart_accelerator.algorithms[algo_key] = var.get()
            
            # حفظ البيانات
            self.smart_accelerator.save_learning_data()
            
            messagebox.showinfo(language_manager.get_text("success"), 
                              "تم حفظ الإعدادات بنجاح!")
            
            # تحديث العروض
            self.update_status_display()
            
        except Exception as e:
            messagebox.showerror(language_manager.get_text("error"), 
                               f"خطأ في حفظ الإعدادات:\n{str(e)}")
    
    def update_status_display(self):
        """تحديث عرض الحالة"""
        try:
            status = self.smart_accelerator.get_acceleration_status()
            
            status_text = f"""
حالة التسريع الذكي:
{'=' * 50}

الحالة العامة:
- التسريع الذكي: {'مفعل' if status['enabled'] else 'معطل'}
- وضع التعلم: {'مفعل' if status['learning_mode'] else 'معطل'}
- مستوى التحسين: {status['optimization_level']}

الإعدادات الحالية:
- عدد الاتصالات المثلى: {status['optimal_connections']}
- حجم الجزء المثلى: {self.format_size(status['optimal_chunk_size'])}

إحصائيات التعلم:
- عينات الأداء: {status['performance_samples']}
- الخوادم المحللة: {status['servers_analyzed']}
- جودة الشبكة: {status['network_quality']}

الخوارزميات النشطة:
"""
            
            for algo_key, enabled in self.smart_accelerator.algorithms.items():
                algo_names = {
                    'adaptive_connections': 'التكيف مع الاتصالات',
                    'dynamic_chunk_sizing': 'تحجيم الأجزاء الديناميكي',
                    'server_load_balancing': 'توازن حمولة الخادم',
                    'network_condition_adaptation': 'التكيف مع الشبكة',
                    'predictive_optimization': 'التحسين التنبؤي'
                }
                
                algo_name = algo_names.get(algo_key, algo_key)
                status_icon = "✅" if enabled else "❌"
                status_text += f"- {algo_name}: {status_icon}\n"
            
            status_text += f"\nآخر تحديث: {datetime.now().strftime('%H:%M:%S')}"
            
            self.status_text.delete('1.0', tk.END)
            self.status_text.insert('1.0', status_text)
            
        except Exception as e:
            error_text = f"خطأ في تحديث الحالة:\n{str(e)}"
            self.status_text.delete('1.0', tk.END)
            self.status_text.insert('1.0', error_text)
    
    def update_performance_display(self):
        """تحديث عرض الأداء"""
        try:
            # مسح البيانات الحالية
            for item in self.performance_tree.get_children():
                self.performance_tree.delete(item)
            
            # الحصول على بيانات الأداء
            status = self.smart_accelerator.get_acceleration_status()
            
            # إضافة البيانات
            performance_data = [
                ("عدد الاتصالات المثلى", str(status['optimal_connections']), "4", "16"),
                ("حجم الجزء المثلى", self.format_size(status['optimal_chunk_size']), "8 KB", "128 KB"),
                ("عينات الأداء", str(status['performance_samples']), "50", "1000"),
                ("الخوادم المحللة", str(status['servers_analyzed']), "5", "100"),
                ("جودة الشبكة", status['network_quality'], "good", "excellent"),
                ("حالة التسريع", "مفعل" if status['enabled'] else "معطل", "مفعل", "مفعل"),
                ("وضع التعلم", "مفعل" if status['learning_mode'] else "معطل", "مفعل", "مفعل")
            ]
            
            for metric, current, average, best in performance_data:
                self.performance_tree.insert('', 'end', values=(metric, current, average, best))
            
        except Exception as e:
            print(f"خطأ في تحديث عرض الأداء: {e}")
    
    def export_performance_data(self):
        """تصدير بيانات الأداء"""
        try:
            filename = self.smart_accelerator.export_performance_data()
            if filename:
                messagebox.showinfo(language_manager.get_text("success"), 
                                  f"تم تصدير بيانات الأداء إلى:\n{filename}")
            else:
                messagebox.showerror(language_manager.get_text("error"), 
                                   "فشل في تصدير بيانات الأداء")
        except Exception as e:
            messagebox.showerror(language_manager.get_text("error"), 
                               f"خطأ في التصدير:\n{str(e)}")
    
    def reset_learning_data(self):
        """إعادة تعيين بيانات التعلم"""
        if messagebox.askyesno(language_manager.get_text("warning"), 
                             "هل تريد إعادة تعيين جميع بيانات التعلم؟\nسيتم فقدان جميع التحسينات المتعلمة."):
            try:
                self.smart_accelerator.reset_learning_data()
                messagebox.showinfo(language_manager.get_text("success"), 
                                  "تم إعادة تعيين بيانات التعلم بنجاح!")
                self.update_status_display()
                self.update_performance_display()
            except Exception as e:
                messagebox.showerror(language_manager.get_text("error"), 
                                   f"خطأ في إعادة التعيين:\n{str(e)}")
    
    def format_size(self, size_bytes):
        """تنسيق حجم الملف"""
        if size_bytes == 0:
            return "0 B"
        
        size_names = ["B", "KB", "MB", "GB", "TB"]
        i = 0
        while size_bytes >= 1024 and i < len(size_names) - 1:
            size_bytes /= 1024.0
            i += 1
        
        return f"{size_bytes:.2f} {size_names[i]}"
    
    def center_window(self):
        """توسيط النافذة"""
        self.dialog.update_idletasks()
        x = (self.dialog.winfo_screenwidth() // 2) - (self.dialog.winfo_width() // 2)
        y = (self.dialog.winfo_screenheight() // 2) - (self.dialog.winfo_height() // 2)
        self.dialog.geometry(f"+{x}+{y}")
    
    def close_dialog(self):
        """إغلاق النافذة"""
        self.dialog.destroy()
    
    def show(self):
        """عرض النافذة"""
        self.dialog.wait_window()
