"""
نوافذ الحوار المتقدمة - Advanced Dialog Windows
نوافذ متقدمة للإعدادات والتحميل والجدولة مع دعم الثيمات واللغات
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import os
from datetime import datetime, timedelta
from download_manager import DownloadPriority
from scheduler import ScheduleType, ScheduleCondition
from theme_manager import theme_manager
from language_manager import language_manager

class AdvancedDownloadDialog:
    """نافذة تحميل متقدمة"""

    def __init__(self, parent, download_manager, scheduler):
        self.parent = parent
        self.download_manager = download_manager
        self.scheduler = scheduler
        self.result = None

        # إنشاء النافذة
        self.dialog = tk.Toplevel(parent)
        self.dialog.title("تحميل جديد متقدم")
        self.dialog.geometry("600x700")
        self.dialog.resizable(False, False)
        self.dialog.transient(parent)
        self.dialog.grab_set()

        # ألوان
        self.colors = {
            'bg': '#f8f9fa',
            'panel': '#ffffff',
            'accent': '#0d6efd',
            'success': '#198754',
            'text': '#212529'
        }

        self.dialog.configure(bg=self.colors['bg'])

        # متغيرات
        self.url_var = tk.StringVar()
        self.save_path_var = tk.StringVar(value=os.path.expanduser("~/Downloads"))
        self.filename_var = tk.StringVar()
        self.priority_var = tk.StringVar(value="عادي")
        self.schedule_type_var = tk.StringVar(value="فوري")
        self.connections_var = tk.IntVar(value=4)
        self.retry_var = tk.BooleanVar(value=True)
        self.bandwidth_limit_var = tk.IntVar(value=0)

        # إعداد الواجهة
        self.setup_ui()

        # توسيط النافذة
        self.center_window()

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # إطار التمرير
        canvas = tk.Canvas(self.dialog, bg=self.colors['bg'])
        scrollbar = ttk.Scrollbar(self.dialog, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        # معلومات التحميل الأساسية
        self.create_basic_info_section(scrollable_frame)

        # إعدادات التحميل المتقدمة
        self.create_advanced_settings_section(scrollable_frame)

        # إعدادات الجدولة
        self.create_schedule_section(scrollable_frame)

        # أزرار التحكم
        self.create_control_buttons(scrollable_frame)

        # ترتيب العناصر
        canvas.pack(side="left", fill="both", expand=True, padx=10, pady=10)
        scrollbar.pack(side="right", fill="y")

    def create_basic_info_section(self, parent):
        """قسم المعلومات الأساسية"""
        frame = tk.LabelFrame(parent, text=" معلومات التحميل الأساسية ",
                             font=('Segoe UI', 10, 'bold'),
                             bg=self.colors['panel'], fg=self.colors['text'])
        frame.pack(fill='x', pady=10, padx=5)

        # الرابط
        tk.Label(frame, text="🔗 رابط التحميل:", bg=self.colors['panel']).grid(row=0, column=0, sticky='w', padx=10, pady=5)
        url_entry = tk.Entry(frame, textvariable=self.url_var, width=60)
        url_entry.grid(row=0, column=1, columnspan=2, sticky='ew', padx=10, pady=5)

        # زر فحص الرابط
        check_btn = tk.Button(frame, text="فحص الرابط", command=self.check_url)
        check_btn.grid(row=0, column=3, padx=5, pady=5)

        # مجلد الحفظ
        tk.Label(frame, text="📁 مجلد الحفظ:", bg=self.colors['panel']).grid(row=1, column=0, sticky='w', padx=10, pady=5)
        path_entry = tk.Entry(frame, textvariable=self.save_path_var, width=50)
        path_entry.grid(row=1, column=1, columnspan=2, sticky='ew', padx=10, pady=5)

        browse_btn = tk.Button(frame, text="تصفح", command=self.browse_folder)
        browse_btn.grid(row=1, column=3, padx=5, pady=5)

        # اسم الملف
        tk.Label(frame, text="📄 اسم الملف:", bg=self.colors['panel']).grid(row=2, column=0, sticky='w', padx=10, pady=5)
        filename_entry = tk.Entry(frame, textvariable=self.filename_var, width=60)
        filename_entry.grid(row=2, column=1, columnspan=2, sticky='ew', padx=10, pady=5)

        # الأولوية
        tk.Label(frame, text="⭐ الأولوية:", bg=self.colors['panel']).grid(row=3, column=0, sticky='w', padx=10, pady=5)
        priority_combo = ttk.Combobox(frame, textvariable=self.priority_var,
                                     values=["منخفضة", "عادية", "عالية", "عاجلة"], state="readonly")
        priority_combo.grid(row=3, column=1, sticky='w', padx=10, pady=5)

        frame.columnconfigure(1, weight=1)

    def create_advanced_settings_section(self, parent):
        """قسم الإعدادات المتقدمة"""
        frame = tk.LabelFrame(parent, text=" إعدادات التحميل المتقدمة ",
                             font=('Segoe UI', 10, 'bold'),
                             bg=self.colors['panel'], fg=self.colors['text'])
        frame.pack(fill='x', pady=10, padx=5)

        # عدد الاتصالات
        tk.Label(frame, text="🔗 عدد الاتصالات:", bg=self.colors['panel']).grid(row=0, column=0, sticky='w', padx=10, pady=5)
        connections_spin = tk.Spinbox(frame, from_=1, to=16, textvariable=self.connections_var, width=10)
        connections_spin.grid(row=0, column=1, sticky='w', padx=10, pady=5)

        # إعادة المحاولة
        retry_check = tk.Checkbutton(frame, text="إعادة المحاولة عند الفشل",
                                    variable=self.retry_var, bg=self.colors['panel'])
        retry_check.grid(row=0, column=2, sticky='w', padx=10, pady=5)

        # حد عرض النطاق
        tk.Label(frame, text="📊 حد السرعة (KB/s):", bg=self.colors['panel']).grid(row=1, column=0, sticky='w', padx=10, pady=5)
        bandwidth_spin = tk.Spinbox(frame, from_=0, to=10000, textvariable=self.bandwidth_limit_var, width=10)
        bandwidth_spin.grid(row=1, column=1, sticky='w', padx=10, pady=5)

        tk.Label(frame, text="(0 = بدون حد)", font=('Segoe UI', 8),
                bg=self.colors['panel'], fg='gray').grid(row=1, column=2, sticky='w', padx=10, pady=5)

    def create_schedule_section(self, parent):
        """قسم الجدولة"""
        frame = tk.LabelFrame(parent, text=" إعدادات الجدولة ",
                             font=('Segoe UI', 10, 'bold'),
                             bg=self.colors['panel'], fg=self.colors['text'])
        frame.pack(fill='x', pady=10, padx=5)

        # نوع الجدولة
        tk.Label(frame, text="⏰ نوع الجدولة:", bg=self.colors['panel']).grid(row=0, column=0, sticky='w', padx=10, pady=5)
        schedule_combo = ttk.Combobox(frame, textvariable=self.schedule_type_var,
                                     values=["فوري", "مؤجل", "مجدول", "متكرر"], state="readonly")
        schedule_combo.grid(row=0, column=1, sticky='w', padx=10, pady=5)
        schedule_combo.bind('<<ComboboxSelected>>', self.on_schedule_type_change)

        # إطار الجدولة المتقدمة
        self.schedule_frame = tk.Frame(frame, bg=self.colors['panel'])
        self.schedule_frame.grid(row=1, column=0, columnspan=3, sticky='ew', padx=10, pady=5)

        frame.columnconfigure(2, weight=1)

    def on_schedule_type_change(self, event=None):
        """تغيير نوع الجدولة"""
        # مسح الإطار
        for widget in self.schedule_frame.winfo_children():
            widget.destroy()

        schedule_type = self.schedule_type_var.get()

        if schedule_type == "مؤجل":
            tk.Label(self.schedule_frame, text="التأجيل (دقائق):", bg=self.colors['panel']).pack(side='left', padx=5)
            self.delay_var = tk.IntVar(value=30)
            delay_spin = tk.Spinbox(self.schedule_frame, from_=1, to=1440, textvariable=self.delay_var, width=10)
            delay_spin.pack(side='left', padx=5)

        elif schedule_type == "مجدول":
            tk.Label(self.schedule_frame, text="التاريخ والوقت:", bg=self.colors['panel']).pack(side='left', padx=5)

            # تاريخ
            self.date_var = tk.StringVar(value=datetime.now().strftime("%Y-%m-%d"))
            date_entry = tk.Entry(self.schedule_frame, textvariable=self.date_var, width=12)
            date_entry.pack(side='left', padx=5)

            # وقت
            self.time_var = tk.StringVar(value="12:00")
            time_entry = tk.Entry(self.schedule_frame, textvariable=self.time_var, width=8)
            time_entry.pack(side='left', padx=5)

        elif schedule_type == "متكرر":
            tk.Label(self.schedule_frame, text="التكرار كل (دقائق):", bg=self.colors['panel']).pack(side='left', padx=5)
            self.interval_var = tk.IntVar(value=60)
            interval_spin = tk.Spinbox(self.schedule_frame, from_=1, to=10080, textvariable=self.interval_var, width=10)
            interval_spin.pack(side='left', padx=5)

    def create_control_buttons(self, parent):
        """أزرار التحكم"""
        frame = tk.Frame(parent, bg=self.colors['bg'])
        frame.pack(fill='x', pady=20, padx=5)

        # زر إضافة
        add_btn = tk.Button(frame, text="🚀 إضافة التحميل",
                           font=('Segoe UI', 10, 'bold'),
                           bg=self.colors['accent'], fg='white',
                           padx=20, pady=8,
                           command=self.add_download)
        add_btn.pack(side='left', padx=10)

        # زر إلغاء
        cancel_btn = tk.Button(frame, text="❌ إلغاء",
                              font=('Segoe UI', 10),
                              bg='#6c757d', fg='white',
                              padx=20, pady=8,
                              command=self.cancel)
        cancel_btn.pack(side='right', padx=10)

        # زر معاينة
        preview_btn = tk.Button(frame, text="👁️ معاينة",
                               font=('Segoe UI', 10),
                               bg='#17a2b8', fg='white',
                               padx=20, pady=8,
                               command=self.preview_download)
        preview_btn.pack(side='right', padx=10)

    def check_url(self):
        """فحص الرابط"""
        url = self.url_var.get().strip()
        if not url:
            messagebox.showerror("خطأ", "يرجى إدخال رابط")
            return

        try:
            # استخدام المحمل لفحص الرابط
            from downloader import AdvancedDownloader
            downloader = AdvancedDownloader()

            if downloader.is_valid_url(url):
                file_info = downloader.get_file_info(url)

                if file_info.get('size', 0) > 0:
                    size_text = downloader.format_size(file_info['size'])
                    filename = file_info.get('filename', '')

                    if filename and not self.filename_var.get():
                        self.filename_var.set(filename)

                    messagebox.showinfo("معلومات الملف",
                                      f"الرابط صحيح!\n\n"
                                      f"اسم الملف: {filename}\n"
                                      f"الحجم: {size_text}\n"
                                      f"النوع: {file_info.get('content_type', 'غير محدد')}")
                else:
                    messagebox.showwarning("تحذير", "الرابط صحيح لكن لا يمكن تحديد حجم الملف")
            else:
                messagebox.showerror("خطأ", "رابط غير صحيح")

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في فحص الرابط:\n{str(e)}")

    def browse_folder(self):
        """تصفح المجلدات"""
        folder = filedialog.askdirectory(initialdir=self.save_path_var.get())
        if folder:
            self.save_path_var.set(folder)

    def preview_download(self):
        """معاينة إعدادات التحميل"""
        settings = self.get_download_settings()
        if settings:
            preview_text = f"""
معاينة إعدادات التحميل:

الرابط: {settings['url']}
مجلد الحفظ: {settings['save_path']}
اسم الملف: {settings['filename'] or 'تلقائي'}
الأولوية: {settings['priority']}
عدد الاتصالات: {settings['connections']}
إعادة المحاولة: {'نعم' if settings['retry'] else 'لا'}
حد السرعة: {settings['bandwidth_limit'] or 'بدون حد'} KB/s
نوع الجدولة: {settings['schedule_type']}
            """

            messagebox.showinfo("معاينة التحميل", preview_text)

    def get_download_settings(self):
        """الحصول على إعدادات التحميل"""
        url = self.url_var.get().strip()
        if not url:
            messagebox.showerror("خطأ", "يرجى إدخال رابط التحميل")
            return None

        save_path = self.save_path_var.get().strip()
        if not save_path:
            messagebox.showerror("خطأ", "يرجى تحديد مجلد الحفظ")
            return None

        # تحويل الأولوية
        priority_map = {
            "منخفضة": DownloadPriority.LOW,
            "عادية": DownloadPriority.NORMAL,
            "عالية": DownloadPriority.HIGH,
            "عاجلة": DownloadPriority.URGENT
        }

        return {
            'url': url,
            'save_path': save_path,
            'filename': self.filename_var.get().strip() or None,
            'priority': priority_map.get(self.priority_var.get(), DownloadPriority.NORMAL),
            'connections': self.connections_var.get(),
            'retry': self.retry_var.get(),
            'bandwidth_limit': self.bandwidth_limit_var.get() if self.bandwidth_limit_var.get() > 0 else None,
            'schedule_type': self.schedule_type_var.get()
        }

    def add_download(self):
        """إضافة التحميل"""
        settings = self.get_download_settings()
        if not settings:
            return

        try:
            schedule_type = settings['schedule_type']

            if schedule_type == "فوري":
                # إضافة مباشرة إلى مدير التحميلات
                download_id = self.download_manager.add_download(
                    settings['url'],
                    settings['save_path'],
                    settings['filename'],
                    settings['priority']
                )
                self.result = {'type': 'immediate', 'id': download_id}

            else:
                # إضافة إلى المجدول
                schedule_id = self.scheduler.add_scheduled_download(
                    settings['url'],
                    settings['save_path'],
                    settings['filename'],
                    settings['priority']
                )

                # تطبيق إعدادات الجدولة
                if schedule_type == "مؤجل":
                    delay = getattr(self, 'delay_var', tk.IntVar(value=30)).get()
                    self.scheduler.schedule_delayed(schedule_id, delay)

                elif schedule_type == "مجدول":
                    date_str = getattr(self, 'date_var', tk.StringVar()).get()
                    time_str = getattr(self, 'time_var', tk.StringVar()).get()
                    try:
                        scheduled_datetime = datetime.strptime(f"{date_str} {time_str}", "%Y-%m-%d %H:%M")
                        self.scheduler.schedule_at_time(schedule_id, scheduled_datetime)
                    except ValueError:
                        messagebox.showerror("خطأ", "تنسيق التاريخ أو الوقت غير صحيح")
                        return

                elif schedule_type == "متكرر":
                    interval = getattr(self, 'interval_var', tk.IntVar(value=60)).get()
                    self.scheduler.schedule_recurring(schedule_id, interval)

                # تطبيق حد عرض النطاق
                if settings['bandwidth_limit']:
                    self.scheduler.set_bandwidth_limit(schedule_id, settings['bandwidth_limit'])

                self.result = {'type': 'scheduled', 'id': schedule_id}

            messagebox.showinfo("نجح", "تم إضافة التحميل بنجاح!")
            self.dialog.destroy()

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في إضافة التحميل:\n{str(e)}")

    def cancel(self):
        """إلغاء"""
        self.result = None
        self.dialog.destroy()

    def center_window(self):
        """توسيط النافذة"""
        self.dialog.update_idletasks()
        x = (self.dialog.winfo_screenwidth() // 2) - (self.dialog.winfo_width() // 2)
        y = (self.dialog.winfo_screenheight() // 2) - (self.dialog.winfo_height() // 2)
        self.dialog.geometry(f"+{x}+{y}")

    def show(self):
        """عرض النافذة وانتظار النتيجة"""
        self.dialog.wait_window()
        return self.result

class AdvancedSettingsDialog:
    """نافذة الإعدادات المتقدمة"""

    def __init__(self, parent, current_settings=None):
        self.parent = parent
        self.current_settings = current_settings or {}
        self.result = None

        # إنشاء النافذة
        self.dialog = tk.Toplevel(parent)
        self.dialog.title(language_manager.get_text("advanced_settings"))
        self.dialog.geometry("700x600")
        self.dialog.resizable(False, False)
        self.dialog.transient(parent)
        self.dialog.grab_set()

        # الحصول على الألوان من مدير الثيم
        self.colors = theme_manager.get_current_theme()

        # تطبيق الثيم على النافذة
        theme_manager.apply_theme_to_widget(self.dialog, "main_window")

        # متغيرات الإعدادات
        self.max_connections_var = tk.IntVar(value=self.current_settings.get('max_connections', 4))
        self.auto_retry_var = tk.BooleanVar(value=self.current_settings.get('auto_retry', True))
        self.bandwidth_limit_var = tk.IntVar(value=self.current_settings.get('bandwidth_limit', 0))
        self.download_notifications_var = tk.BooleanVar(value=self.current_settings.get('download_notifications', True))
        self.default_save_path_var = tk.StringVar(value=self.current_settings.get('default_save_path', ''))
        self.max_concurrent_var = tk.IntVar(value=self.current_settings.get('max_concurrent', 3))
        self.chunk_size_var = tk.IntVar(value=self.current_settings.get('chunk_size', 8192))
        self.timeout_var = tk.IntVar(value=self.current_settings.get('timeout', 30))
        self.verify_ssl_var = tk.BooleanVar(value=self.current_settings.get('verify_ssl', True))
        self.auto_start_var = tk.BooleanVar(value=self.current_settings.get('auto_start', True))

        # إعداد الواجهة
        self.setup_ui()

        # توسيط النافذة
        self.center_window()

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # دفتر التبويبات
        notebook = ttk.Notebook(self.dialog)
        notebook.pack(fill='both', expand=True, padx=10, pady=10)

        # تبويب التحميل
        download_frame = ttk.Frame(notebook)
        notebook.add(download_frame, text="إعدادات التحميل")
        self.create_download_settings(download_frame)

        # تبويب الشبكة
        network_frame = ttk.Frame(notebook)
        notebook.add(network_frame, text="إعدادات الشبكة")
        self.create_network_settings(network_frame)

        # تبويب الواجهة
        interface_frame = ttk.Frame(notebook)
        notebook.add(interface_frame, text="إعدادات الواجهة")
        self.create_interface_settings(interface_frame)

        # تبويب متقدم
        advanced_frame = ttk.Frame(notebook)
        notebook.add(advanced_frame, text=language_manager.get_text("advanced_settings"))
        self.create_advanced_settings(advanced_frame)

        # تبويب الثيم واللغة
        appearance_frame = ttk.Frame(notebook)
        notebook.add(appearance_frame, text=language_manager.get_text("appearance", "المظهر"))
        self.create_appearance_settings(appearance_frame)

        # أزرار التحكم
        self.create_control_buttons()

    def create_download_settings(self, parent):
        """إعدادات التحميل"""
        # إطار الإعدادات الأساسية
        basic_frame = tk.LabelFrame(parent, text=" الإعدادات الأساسية ",
                                   font=('Segoe UI', 10, 'bold'))
        basic_frame.pack(fill='x', padx=10, pady=10)

        # عدد الاتصالات المتزامنة
        tk.Label(basic_frame, text="عدد الاتصالات المتزامنة:").grid(row=0, column=0, sticky='w', padx=10, pady=5)
        connections_spin = tk.Spinbox(basic_frame, from_=1, to=16, textvariable=self.max_connections_var, width=10)
        connections_spin.grid(row=0, column=1, sticky='w', padx=10, pady=5)

        # عدد التحميلات المتزامنة
        tk.Label(basic_frame, text="عدد التحميلات المتزامنة:").grid(row=1, column=0, sticky='w', padx=10, pady=5)
        concurrent_spin = tk.Spinbox(basic_frame, from_=1, to=10, textvariable=self.max_concurrent_var, width=10)
        concurrent_spin.grid(row=1, column=1, sticky='w', padx=10, pady=5)

        # مجلد الحفظ الافتراضي
        tk.Label(basic_frame, text="مجلد الحفظ الافتراضي:").grid(row=2, column=0, sticky='w', padx=10, pady=5)
        path_frame = tk.Frame(basic_frame)
        path_frame.grid(row=2, column=1, columnspan=2, sticky='ew', padx=10, pady=5)

        path_entry = tk.Entry(path_frame, textvariable=self.default_save_path_var, width=40)
        path_entry.pack(side='left', fill='x', expand=True)

        browse_btn = tk.Button(path_frame, text="تصفح", command=self.browse_default_folder)
        browse_btn.pack(side='right', padx=(5, 0))

        # إعادة المحاولة التلقائية
        retry_check = tk.Checkbutton(basic_frame, text="إعادة المحاولة التلقائية عند الفشل",
                                    variable=self.auto_retry_var)
        retry_check.grid(row=3, column=0, columnspan=2, sticky='w', padx=10, pady=5)

        # البدء التلقائي
        auto_start_check = tk.Checkbutton(basic_frame, text="بدء التحميلات تلقائياً",
                                         variable=self.auto_start_var)
        auto_start_check.grid(row=4, column=0, columnspan=2, sticky='w', padx=10, pady=5)

        basic_frame.columnconfigure(1, weight=1)

    def create_network_settings(self, parent):
        """إعدادات الشبكة"""
        # إطار عرض النطاق
        bandwidth_frame = tk.LabelFrame(parent, text=" إعدادات عرض النطاق ",
                                       font=('Segoe UI', 10, 'bold'))
        bandwidth_frame.pack(fill='x', padx=10, pady=10)

        # حد عرض النطاق
        tk.Label(bandwidth_frame, text="حد عرض النطاق الإجمالي (KB/s):").grid(row=0, column=0, sticky='w', padx=10, pady=5)
        bandwidth_spin = tk.Spinbox(bandwidth_frame, from_=0, to=100000, textvariable=self.bandwidth_limit_var, width=15)
        bandwidth_spin.grid(row=0, column=1, sticky='w', padx=10, pady=5)

        tk.Label(bandwidth_frame, text="(0 = بدون حد)", font=('Segoe UI', 8),
                fg='gray').grid(row=0, column=2, sticky='w', padx=10, pady=5)

        # إطار الاتصال
        connection_frame = tk.LabelFrame(parent, text=" إعدادات الاتصال ",
                                        font=('Segoe UI', 10, 'bold'))
        connection_frame.pack(fill='x', padx=10, pady=10)

        # مهلة الاتصال
        tk.Label(connection_frame, text="مهلة الاتصال (ثانية):").grid(row=0, column=0, sticky='w', padx=10, pady=5)
        timeout_spin = tk.Spinbox(connection_frame, from_=5, to=300, textvariable=self.timeout_var, width=10)
        timeout_spin.grid(row=0, column=1, sticky='w', padx=10, pady=5)

        # حجم الجزء
        tk.Label(connection_frame, text="حجم الجزء (بايت):").grid(row=1, column=0, sticky='w', padx=10, pady=5)
        chunk_spin = tk.Spinbox(connection_frame, from_=1024, to=1048576, increment=1024,
                               textvariable=self.chunk_size_var, width=15)
        chunk_spin.grid(row=1, column=1, sticky='w', padx=10, pady=5)

        # التحقق من SSL
        ssl_check = tk.Checkbutton(connection_frame, text="التحقق من شهادات SSL",
                                  variable=self.verify_ssl_var)
        ssl_check.grid(row=2, column=0, columnspan=2, sticky='w', padx=10, pady=5)

    def create_interface_settings(self, parent):
        """إعدادات الواجهة"""
        # إطار الإشعارات
        notifications_frame = tk.LabelFrame(parent, text=" الإشعارات ",
                                           font=('Segoe UI', 10, 'bold'))
        notifications_frame.pack(fill='x', padx=10, pady=10)

        # إشعارات التحميل
        notifications_check = tk.Checkbutton(notifications_frame, text="إشعارات اكتمال التحميل",
                                            variable=self.download_notifications_var)
        notifications_check.grid(row=0, column=0, sticky='w', padx=10, pady=5)

        # إطار العرض
        display_frame = tk.LabelFrame(parent, text=" إعدادات العرض ",
                                     font=('Segoe UI', 10, 'bold'))
        display_frame.pack(fill='x', padx=10, pady=10)

        # يمكن إضافة إعدادات أخرى هنا
        tk.Label(display_frame, text="إعدادات العرض ستتوفر في الإصدارات القادمة",
                fg='gray').pack(padx=10, pady=20)

    def create_advanced_settings(self, parent):
        """الإعدادات المتقدمة"""
        # إطار الأداء
        performance_frame = tk.LabelFrame(parent, text=" إعدادات الأداء ",
                                         font=('Segoe UI', 10, 'bold'))
        performance_frame.pack(fill='x', padx=10, pady=10)

        tk.Label(performance_frame, text="ملاحظة: تغيير هذه الإعدادات قد يؤثر على الأداء",
                fg='orange', font=('Segoe UI', 9, 'italic')).pack(padx=10, pady=5)

        # معلومات النظام
        info_frame = tk.LabelFrame(parent, text=" معلومات النظام ",
                                  font=('Segoe UI', 10, 'bold'))
        info_frame.pack(fill='x', padx=10, pady=10)

        # عرض معلومات النظام
        try:
            import platform
            import psutil

            system_info = f"""
نظام التشغيل: {platform.system()} {platform.release()}
المعالج: {platform.processor()}
الذاكرة: {psutil.virtual_memory().total // (1024**3)} GB
مساحة القرص: {psutil.disk_usage('/').total // (1024**3)} GB
            """
        except:
            system_info = "لا يمكن الحصول على معلومات النظام"

        info_text = tk.Text(info_frame, height=6, width=60, font=('Consolas', 9))
        info_text.pack(padx=10, pady=10)
        info_text.insert('1.0', system_info)
        info_text.config(state='disabled')

    def create_appearance_settings(self, parent):
        """إعدادات المظهر والثيم واللغة"""
        # إطار الثيم
        theme_frame = tk.LabelFrame(parent, text=" " + language_manager.get_text("theme") + " ",
                                   font=('Segoe UI', 10, 'bold'))
        theme_frame.pack(fill='x', padx=10, pady=10)
        theme_manager.apply_theme_to_widget(theme_frame, "labelframe")

        # اختيار الثيم
        tk.Label(theme_frame, text=language_manager.get_text("theme") + ":").grid(row=0, column=0, sticky='w', padx=10, pady=5)
        theme_manager.apply_theme_to_widget(theme_frame.winfo_children()[-1], "label")

        self.theme_var = tk.StringVar(value=theme_manager.current_theme)
        theme_combo = ttk.Combobox(theme_frame, textvariable=self.theme_var,
                                  values=list(theme_manager.get_theme_names()), state="readonly")
        theme_combo.grid(row=0, column=1, sticky='w', padx=10, pady=5)
        theme_combo.bind('<<ComboboxSelected>>', self.on_theme_change)

        # معاينة الثيم
        preview_btn = tk.Button(theme_frame, text=language_manager.get_text("preview"),
                               command=self.preview_theme)
        preview_btn.grid(row=0, column=2, padx=10, pady=5)
        theme_manager.apply_theme_to_widget(preview_btn, "button_secondary")

        # إطار اللغة
        language_frame = tk.LabelFrame(parent, text=" " + language_manager.get_text("language") + " ",
                                      font=('Segoe UI', 10, 'bold'))
        language_frame.pack(fill='x', padx=10, pady=10)
        theme_manager.apply_theme_to_widget(language_frame, "labelframe")

        # اختيار اللغة
        tk.Label(language_frame, text=language_manager.get_text("language") + ":").grid(row=0, column=0, sticky='w', padx=10, pady=5)
        theme_manager.apply_theme_to_widget(language_frame.winfo_children()[-1], "label")

        self.language_var = tk.StringVar(value=language_manager.current_language)
        language_names = language_manager.get_language_names()
        language_combo = ttk.Combobox(language_frame, textvariable=self.language_var,
                                     values=list(language_names.values()), state="readonly")
        language_combo.grid(row=0, column=1, sticky='w', padx=10, pady=5)
        language_combo.bind('<<ComboboxSelected>>', self.on_language_change)

        # معاينة اللغة
        preview_lang_btn = tk.Button(language_frame, text=language_manager.get_text("preview"),
                                    command=self.preview_language)
        preview_lang_btn.grid(row=0, column=2, padx=10, pady=5)
        theme_manager.apply_theme_to_widget(preview_lang_btn, "button_secondary")

        # إطار الخطوط
        font_frame = tk.LabelFrame(parent, text=" " + language_manager.get_text("fonts", "الخطوط") + " ",
                                  font=('Segoe UI', 10, 'bold'))
        font_frame.pack(fill='x', padx=10, pady=10)
        theme_manager.apply_theme_to_widget(font_frame, "labelframe")

        # حجم الخط
        tk.Label(font_frame, text=language_manager.get_text("font_size", "حجم الخط") + ":").grid(row=0, column=0, sticky='w', padx=10, pady=5)
        theme_manager.apply_theme_to_widget(font_frame.winfo_children()[-1], "label")

        self.font_size_var = tk.IntVar(value=9)
        font_size_spin = tk.Spinbox(font_frame, from_=8, to=16, textvariable=self.font_size_var, width=10)
        font_size_spin.grid(row=0, column=1, sticky='w', padx=10, pady=5)

        # نوع الخط
        tk.Label(font_frame, text=language_manager.get_text("font_family", "نوع الخط") + ":").grid(row=1, column=0, sticky='w', padx=10, pady=5)
        theme_manager.apply_theme_to_widget(font_frame.winfo_children()[-1], "label")

        self.font_family_var = tk.StringVar(value="Segoe UI")
        font_family_combo = ttk.Combobox(font_frame, textvariable=self.font_family_var,
                                        values=["Segoe UI", "Arial", "Tahoma", "Calibri", "Times New Roman"],
                                        state="readonly")
        font_family_combo.grid(row=1, column=1, sticky='w', padx=10, pady=5)

        # إطار الألوان المخصصة
        custom_colors_frame = tk.LabelFrame(parent, text=" " + language_manager.get_text("custom_colors", "ألوان مخصصة") + " ",
                                           font=('Segoe UI', 10, 'bold'))
        custom_colors_frame.pack(fill='x', padx=10, pady=10)
        theme_manager.apply_theme_to_widget(custom_colors_frame, "labelframe")

        # عرض الألوان الحالية
        colors_display_frame = tk.Frame(custom_colors_frame)
        colors_display_frame.pack(fill='x', padx=10, pady=10)
        theme_manager.apply_theme_to_widget(colors_display_frame, "frame")

        current_theme = theme_manager.get_current_theme()
        color_samples = [
            ("primary", language_manager.get_text("primary_color", "اللون الأساسي")),
            ("success", language_manager.get_text("success_color", "لون النجاح")),
            ("warning", language_manager.get_text("warning_color", "لون التحذير")),
            ("error", language_manager.get_text("error_color", "لون الخطأ"))
        ]

        for i, (color_key, color_name) in enumerate(color_samples):
            color_frame = tk.Frame(colors_display_frame)
            color_frame.grid(row=i//2, column=i%2, sticky='w', padx=10, pady=5)
            theme_manager.apply_theme_to_widget(color_frame, "frame")

            # عينة اللون
            color_sample = tk.Label(color_frame, text="  ", width=3,
                                   bg=current_theme.get(color_key, '#000000'),
                                   relief='solid', bd=1)
            color_sample.pack(side='left', padx=(0, 5))

            # اسم اللون
            color_label = tk.Label(color_frame, text=color_name)
            color_label.pack(side='left')
            theme_manager.apply_theme_to_widget(color_label, "label")

    def on_theme_change(self, event=None):
        """عند تغيير الثيم"""
        new_theme = self.theme_var.get()
        if new_theme in theme_manager.get_theme_names():
            # معاينة فورية
            self.preview_theme()

    def on_language_change(self, event=None):
        """عند تغيير اللغة"""
        # يمكن إضافة معاينة فورية هنا
        pass

    def preview_theme(self):
        """معاينة الثيم"""
        new_theme = self.theme_var.get()
        if theme_manager.set_theme(new_theme):
            # تحديث ألوان النافذة الحالية
            self.colors = theme_manager.get_current_theme()
            theme_manager.apply_theme_to_widget(self.dialog, "main_window")

            # إشعار
            messagebox.showinfo(language_manager.get_text("info"),
                              language_manager.get_text("theme_preview", "تم تطبيق الثيم للمعاينة"))

    def preview_language(self):
        """معاينة اللغة"""
        # الحصول على كود اللغة من الاسم
        language_names = language_manager.get_language_names()
        selected_name = self.language_var.get()

        for code, name in language_names.items():
            if name == selected_name:
                if language_manager.set_language(code):
                    # تحديث عنوان النافذة
                    self.dialog.title(language_manager.get_text("advanced_settings"))

                    # إشعار
                    messagebox.showinfo(language_manager.get_text("info"),
                                      language_manager.get_text("language_preview", "تم تطبيق اللغة للمعاينة"))
                break

    def create_control_buttons(self):
        """أزرار التحكم"""
        button_frame = tk.Frame(self.dialog, bg=self.colors['bg'])
        button_frame.pack(fill='x', pady=10, padx=10)

        # زر حفظ
        save_btn = tk.Button(button_frame, text="💾 حفظ الإعدادات",
                            font=('Segoe UI', 10, 'bold'),
                            bg=self.colors['success'], fg='white',
                            padx=20, pady=8,
                            command=self.save_settings)
        save_btn.pack(side='left', padx=5)

        # زر استعادة الافتراضي
        reset_btn = tk.Button(button_frame, text="🔄 استعادة الافتراضي",
                             font=('Segoe UI', 10),
                             bg='#ffc107', fg='black',
                             padx=20, pady=8,
                             command=self.reset_to_default)
        reset_btn.pack(side='left', padx=5)

        # زر إلغاء
        cancel_btn = tk.Button(button_frame, text="❌ إلغاء",
                              font=('Segoe UI', 10),
                              bg='#6c757d', fg='white',
                              padx=20, pady=8,
                              command=self.cancel)
        cancel_btn.pack(side='right', padx=5)

        # زر تطبيق
        apply_btn = tk.Button(button_frame, text="✅ تطبيق",
                             font=('Segoe UI', 10),
                             bg=self.colors['accent'], fg='white',
                             padx=20, pady=8,
                             command=self.apply_settings)
        apply_btn.pack(side='right', padx=5)

    def browse_default_folder(self):
        """تصفح المجلد الافتراضي"""
        folder = filedialog.askdirectory(initialdir=self.default_save_path_var.get())
        if folder:
            self.default_save_path_var.set(folder)

    def get_settings(self):
        """الحصول على الإعدادات الحالية"""
        return {
            'max_connections': self.max_connections_var.get(),
            'auto_retry': self.auto_retry_var.get(),
            'bandwidth_limit': self.bandwidth_limit_var.get() if self.bandwidth_limit_var.get() > 0 else None,
            'download_notifications': self.download_notifications_var.get(),
            'default_save_path': self.default_save_path_var.get(),
            'max_concurrent': self.max_concurrent_var.get(),
            'chunk_size': self.chunk_size_var.get(),
            'timeout': self.timeout_var.get(),
            'verify_ssl': self.verify_ssl_var.get(),
            'auto_start': self.auto_start_var.get()
        }

    def save_settings(self):
        """حفظ الإعدادات"""
        self.result = self.get_settings()
        messagebox.showinfo("نجح", "تم حفظ الإعدادات بنجاح!")
        self.dialog.destroy()

    def apply_settings(self):
        """تطبيق الإعدادات بدون إغلاق النافذة"""
        self.result = self.get_settings()
        messagebox.showinfo("نجح", "تم تطبيق الإعدادات!")

    def reset_to_default(self):
        """استعادة الإعدادات الافتراضية"""
        if messagebox.askyesno("تأكيد", "هل تريد استعادة الإعدادات الافتراضية؟"):
            self.max_connections_var.set(4)
            self.auto_retry_var.set(True)
            self.bandwidth_limit_var.set(0)
            self.download_notifications_var.set(True)
            self.default_save_path_var.set(os.path.expanduser("~/Downloads"))
            self.max_concurrent_var.set(3)
            self.chunk_size_var.set(8192)
            self.timeout_var.set(30)
            self.verify_ssl_var.set(True)
            self.auto_start_var.set(True)

            messagebox.showinfo("تم", "تم استعادة الإعدادات الافتراضية")

    def cancel(self):
        """إلغاء"""
        self.result = None
        self.dialog.destroy()

    def center_window(self):
        """توسيط النافذة"""
        self.dialog.update_idletasks()
        x = (self.dialog.winfo_screenwidth() // 2) - (self.dialog.winfo_width() // 2)
        y = (self.dialog.winfo_screenheight() // 2) - (self.dialog.winfo_height() // 2)
        self.dialog.geometry(f"+{x}+{y}")

    def show(self):
        """عرض النافذة وانتظار النتيجة"""
        self.dialog.wait_window()
        return self.result