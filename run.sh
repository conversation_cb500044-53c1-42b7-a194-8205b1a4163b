#!/bin/bash

echo "========================================"
echo "    مدير التحميل - Download Manager"
echo "========================================"
echo ""
echo "جاري تشغيل البرنامج..."
echo "Starting the application..."
echo ""

# التحقق من وجود Python
if ! command -v python3 &> /dev/null; then
    if ! command -v python &> /dev/null; then
        echo "خطأ: Python غير مثبت على النظام"
        echo "Error: Python is not installed"
        exit 1
    else
        PYTHON_CMD="python"
    fi
else
    PYTHON_CMD="python3"
fi

# التحقق من وجود pip
if ! command -v pip3 &> /dev/null; then
    if ! command -v pip &> /dev/null; then
        echo "خطأ: pip غير مثبت على النظام"
        echo "Error: pip is not installed"
        exit 1
    else
        PIP_CMD="pip"
    fi
else
    PIP_CMD="pip3"
fi

# تثبيت المتطلبات إذا لزم الأمر
echo "تحقق من المتطلبات..."
echo "Checking requirements..."
$PIP_CMD install -r requirements.txt > /dev/null 2>&1

# تشغيل البرنامج
echo "تشغيل البرنامج..."
echo "Running the application..."
$PYTHON_CMD main.py

# في حالة إغلاق البرنامج
echo ""
echo "تم إغلاق البرنامج"
echo "Application closed"
