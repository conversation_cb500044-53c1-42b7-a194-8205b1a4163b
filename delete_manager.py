"""
مدير الحذف المتقدم - Advanced Delete Manager
إدارة عمليات الحذف مع إمكانية الاستعادة والحذف الآمن
"""

import os
import json
import shutil
import time
from datetime import datetime, timedelta
from pathlib import Path

class DeleteManager:
    """مدير الحذف المتقدم"""
    
    def __init__(self):
        self.trash_folder = self.get_trash_folder()
        self.deleted_items = []
        self.auto_cleanup_days = 30  # حذف نهائي بعد 30 يوم
        self.max_trash_size = 1024 * 1024 * 1024  # 1 GB حد أقصى لسلة المحذوفات
        
        # إنشاء مجلد سلة المحذوفات
        self.ensure_trash_folder()
        
        # تحميل قائمة المحذوفات
        self.load_deleted_items()
        
        # تنظيف تلقائي
        self.auto_cleanup()
    
    def get_trash_folder(self):
        """الحصول على مجلد سلة المحذوفات"""
        # استخدام مجلد مخصص في مجلد البرنامج
        app_folder = Path.cwd()
        trash_folder = app_folder / ".trash"
        return trash_folder
    
    def ensure_trash_folder(self):
        """التأكد من وجود مجلد سلة المحذوفات"""
        try:
            self.trash_folder.mkdir(exist_ok=True)
            
            # إنشاء مجلدات فرعية
            (self.trash_folder / "files").mkdir(exist_ok=True)
            (self.trash_folder / "downloads").mkdir(exist_ok=True)
            (self.trash_folder / "metadata").mkdir(exist_ok=True)
            
            return True
        except Exception as e:
            print(f"خطأ في إنشاء مجلد سلة المحذوفات: {e}")
            return False
    
    def delete_download(self, download_id, download_info, delete_file=False):
        """حذف تحميل مع إمكانية حذف الملف"""
        try:
            deleted_item = {
                'id': download_id,
                'type': 'download',
                'download_info': download_info,
                'deleted_at': datetime.now().isoformat(),
                'file_deleted': delete_file,
                'original_file_path': None,
                'trash_file_path': None,
                'can_restore': True
            }
            
            # إذا كان المطلوب حذف الملف أيضاً
            if delete_file and download_info.get('file_path'):
                file_path = download_info['file_path']
                if os.path.exists(file_path):
                    # نقل الملف إلى سلة المحذوفات
                    trash_file_path = self.move_file_to_trash(file_path)
                    if trash_file_path:
                        deleted_item['original_file_path'] = file_path
                        deleted_item['trash_file_path'] = str(trash_file_path)
                    else:
                        deleted_item['can_restore'] = False
            
            # حفظ معلومات التحميل المحذوف
            metadata_file = self.trash_folder / "metadata" / f"{download_id}.json"
            with open(metadata_file, 'w', encoding='utf-8') as f:
                json.dump(deleted_item, f, ensure_ascii=False, indent=2)
            
            # إضافة إلى قائمة المحذوفات
            self.deleted_items.append(deleted_item)
            self.save_deleted_items()
            
            return True
            
        except Exception as e:
            print(f"خطأ في حذف التحميل: {e}")
            return False
    
    def move_file_to_trash(self, file_path):
        """نقل ملف إلى سلة المحذوفات"""
        try:
            file_path = Path(file_path)
            if not file_path.exists():
                return None
            
            # إنشاء اسم فريد في سلة المحذوفات
            timestamp = int(time.time())
            trash_filename = f"{timestamp}_{file_path.name}"
            trash_file_path = self.trash_folder / "files" / trash_filename
            
            # نقل الملف
            shutil.move(str(file_path), str(trash_file_path))
            
            return trash_file_path
            
        except Exception as e:
            print(f"خطأ في نقل الملف إلى سلة المحذوفات: {e}")
            return None
    
    def restore_download(self, download_id):
        """استعادة تحميل محذوف"""
        try:
            # البحث عن العنصر المحذوف
            deleted_item = None
            for item in self.deleted_items:
                if item['id'] == download_id:
                    deleted_item = item
                    break
            
            if not deleted_item:
                return False, "العنصر غير موجود في سلة المحذوفات"
            
            if not deleted_item['can_restore']:
                return False, "لا يمكن استعادة هذا العنصر"
            
            # استعادة الملف إذا كان محذوفاً
            if deleted_item['file_deleted'] and deleted_item['trash_file_path']:
                trash_file_path = Path(deleted_item['trash_file_path'])
                original_file_path = deleted_item['original_file_path']
                
                if trash_file_path.exists() and original_file_path:
                    # التأكد من وجود المجلد الأصلي
                    original_dir = Path(original_file_path).parent
                    original_dir.mkdir(parents=True, exist_ok=True)
                    
                    # استعادة الملف
                    shutil.move(str(trash_file_path), original_file_path)
            
            # إزالة من قائمة المحذوفات
            self.deleted_items.remove(deleted_item)
            
            # حذف ملف البيانات الوصفية
            metadata_file = self.trash_folder / "metadata" / f"{download_id}.json"
            if metadata_file.exists():
                metadata_file.unlink()
            
            self.save_deleted_items()
            
            return True, "تم استعادة العنصر بنجاح"
            
        except Exception as e:
            return False, f"خطأ في استعادة العنصر: {e}"
    
    def permanent_delete(self, download_id):
        """حذف نهائي لتحميل"""
        try:
            # البحث عن العنصر المحذوف
            deleted_item = None
            for item in self.deleted_items:
                if item['id'] == download_id:
                    deleted_item = item
                    break
            
            if not deleted_item:
                return False, "العنصر غير موجود"
            
            # حذف الملف نهائياً إذا كان موجوداً
            if deleted_item['trash_file_path']:
                trash_file_path = Path(deleted_item['trash_file_path'])
                if trash_file_path.exists():
                    trash_file_path.unlink()
            
            # حذف ملف البيانات الوصفية
            metadata_file = self.trash_folder / "metadata" / f"{download_id}.json"
            if metadata_file.exists():
                metadata_file.unlink()
            
            # إزالة من قائمة المحذوفات
            self.deleted_items.remove(deleted_item)
            self.save_deleted_items()
            
            return True, "تم الحذف النهائي بنجاح"
            
        except Exception as e:
            return False, f"خطأ في الحذف النهائي: {e}"
    
    def empty_trash(self):
        """إفراغ سلة المحذوفات بالكامل"""
        try:
            deleted_count = 0
            
            # حذف جميع الملفات
            files_folder = self.trash_folder / "files"
            if files_folder.exists():
                for file_path in files_folder.iterdir():
                    if file_path.is_file():
                        file_path.unlink()
                        deleted_count += 1
            
            # حذف جميع ملفات البيانات الوصفية
            metadata_folder = self.trash_folder / "metadata"
            if metadata_folder.exists():
                for metadata_file in metadata_folder.iterdir():
                    if metadata_file.is_file():
                        metadata_file.unlink()
            
            # مسح قائمة المحذوفات
            self.deleted_items.clear()
            self.save_deleted_items()
            
            return True, f"تم حذف {deleted_count} عنصر نهائياً"
            
        except Exception as e:
            return False, f"خطأ في إفراغ سلة المحذوفات: {e}"
    
    def get_deleted_items(self):
        """الحصول على قائمة العناصر المحذوفة"""
        return self.deleted_items.copy()
    
    def get_trash_size(self):
        """الحصول على حجم سلة المحذوفات"""
        try:
            total_size = 0
            files_folder = self.trash_folder / "files"
            
            if files_folder.exists():
                for file_path in files_folder.iterdir():
                    if file_path.is_file():
                        total_size += file_path.stat().st_size
            
            return total_size
            
        except Exception as e:
            print(f"خطأ في حساب حجم سلة المحذوفات: {e}")
            return 0
    
    def auto_cleanup(self):
        """تنظيف تلقائي للعناصر القديمة"""
        try:
            current_time = datetime.now()
            cutoff_time = current_time - timedelta(days=self.auto_cleanup_days)
            
            items_to_remove = []
            
            for item in self.deleted_items:
                deleted_at = datetime.fromisoformat(item['deleted_at'])
                if deleted_at < cutoff_time:
                    # حذف نهائي للعناصر القديمة
                    self.permanent_delete(item['id'])
                    items_to_remove.append(item)
            
            # فحص حجم سلة المحذوفات
            trash_size = self.get_trash_size()
            if trash_size > self.max_trash_size:
                # حذف أقدم العناصر إذا تجاوز الحد الأقصى
                self.cleanup_by_size()
            
        except Exception as e:
            print(f"خطأ في التنظيف التلقائي: {e}")
    
    def cleanup_by_size(self):
        """تنظيف حسب الحجم"""
        try:
            # ترتيب العناصر حسب تاريخ الحذف (الأقدم أولاً)
            sorted_items = sorted(self.deleted_items, 
                                key=lambda x: datetime.fromisoformat(x['deleted_at']))
            
            current_size = self.get_trash_size()
            target_size = self.max_trash_size * 0.8  # تقليل إلى 80% من الحد الأقصى
            
            for item in sorted_items:
                if current_size <= target_size:
                    break
                
                # حذف العنصر وحساب الحجم المحرر
                if item['trash_file_path']:
                    file_path = Path(item['trash_file_path'])
                    if file_path.exists():
                        file_size = file_path.stat().st_size
                        self.permanent_delete(item['id'])
                        current_size -= file_size
            
        except Exception as e:
            print(f"خطأ في التنظيف حسب الحجم: {e}")
    
    def save_deleted_items(self):
        """حفظ قائمة العناصر المحذوفة"""
        try:
            deleted_items_file = self.trash_folder / "deleted_items.json"
            with open(deleted_items_file, 'w', encoding='utf-8') as f:
                json.dump(self.deleted_items, f, ensure_ascii=False, indent=2)
            
            return True
        except Exception as e:
            print(f"خطأ في حفظ قائمة المحذوفات: {e}")
            return False
    
    def load_deleted_items(self):
        """تحميل قائمة العناصر المحذوفة"""
        try:
            deleted_items_file = self.trash_folder / "deleted_items.json"
            if deleted_items_file.exists():
                with open(deleted_items_file, 'r', encoding='utf-8') as f:
                    self.deleted_items = json.load(f)
            
            return True
        except Exception as e:
            print(f"خطأ في تحميل قائمة المحذوفات: {e}")
            self.deleted_items = []
            return False
    
    def get_statistics(self):
        """الحصول على إحصائيات سلة المحذوفات"""
        try:
            total_items = len(self.deleted_items)
            total_size = self.get_trash_size()
            
            # تصنيف حسب النوع
            downloads_count = len([item for item in self.deleted_items if item['type'] == 'download'])
            files_count = len([item for item in self.deleted_items if item.get('file_deleted', False)])
            
            # أقدم وأحدث عنصر
            oldest_item = None
            newest_item = None
            
            if self.deleted_items:
                sorted_items = sorted(self.deleted_items, 
                                    key=lambda x: datetime.fromisoformat(x['deleted_at']))
                oldest_item = sorted_items[0]['deleted_at']
                newest_item = sorted_items[-1]['deleted_at']
            
            return {
                'total_items': total_items,
                'total_size': total_size,
                'total_size_formatted': self.format_size(total_size),
                'downloads_count': downloads_count,
                'files_count': files_count,
                'oldest_item': oldest_item,
                'newest_item': newest_item,
                'auto_cleanup_days': self.auto_cleanup_days,
                'max_trash_size': self.max_trash_size,
                'max_trash_size_formatted': self.format_size(self.max_trash_size)
            }
            
        except Exception as e:
            print(f"خطأ في حساب إحصائيات سلة المحذوفات: {e}")
            return {}
    
    def format_size(self, size_bytes):
        """تنسيق حجم الملف"""
        if size_bytes == 0:
            return "0 B"
        
        size_names = ["B", "KB", "MB", "GB", "TB"]
        i = 0
        while size_bytes >= 1024 and i < len(size_names) - 1:
            size_bytes /= 1024.0
            i += 1
        
        return f"{size_bytes:.2f} {size_names[i]}"
    
    def search_deleted_items(self, query):
        """البحث في العناصر المحذوفة"""
        try:
            query = query.lower()
            results = []
            
            for item in self.deleted_items:
                # البحث في اسم الملف
                download_info = item.get('download_info', {})
                filename = download_info.get('filename', '').lower()
                url = download_info.get('url', '').lower()
                
                if query in filename or query in url:
                    results.append(item)
            
            return results
            
        except Exception as e:
            print(f"خطأ في البحث: {e}")
            return []

# إنشاء مثيل عام لمدير الحذف
delete_manager = DeleteManager()
