"""
لوحة الإحصائيات والتحليلات المتقدمة - Advanced Analytics Dashboard
نافذة شاملة لعرض الإحصائيات والرسوم البيانية والتحليلات
"""

import tkinter as tk
from tkinter import ttk, messagebox
from datetime import datetime, timedelta
import threading
import time
from theme_manager import theme_manager
from language_manager import language_manager

try:
    import matplotlib.pyplot as plt
    import matplotlib.dates as mdates
    from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
    from matplotlib.figure import Figure
    MATPLOTLIB_AVAILABLE = True
except ImportError:
    MATPLOTLIB_AVAILABLE = False

class AnalyticsDashboard:
    """لوحة الإحصائيات والتحليلات المتقدمة"""

    def __init__(self, parent, analytics_system, smart_accelerator):
        self.parent = parent
        self.analytics_system = analytics_system
        self.smart_accelerator = smart_accelerator

        # إنشاء النافذة
        self.window = tk.Toplevel(parent)
        self.window.title(language_manager.get_text("analytics_dashboard", "لوحة الإحصائيات"))
        self.window.geometry("1200x800")
        self.window.resizable(True, True)
        self.window.transient(parent)

        # الحصول على الألوان من الثيم
        self.colors = theme_manager.get_current_theme()

        # تطبيق الثيم على النافذة
        theme_manager.apply_theme_to_widget(self.window, "main_window")

        # متغيرات التحديث
        self.auto_refresh = True
        self.refresh_interval = 5  # ثوان
        self.last_update = datetime.now()

        # إعداد الواجهة
        self.setup_ui()

        # بدء التحديث التلقائي
        self.start_auto_refresh()

        # توسيط النافذة
        self.center_window()

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # الإطار الرئيسي
        main_frame = tk.Frame(self.window)
        main_frame.pack(fill='both', expand=True, padx=10, pady=10)
        theme_manager.apply_theme_to_widget(main_frame, "frame")

        # شريط الأدوات العلوي
        self.create_toolbar(main_frame)

        # دفتر التبويبات
        self.notebook = ttk.Notebook(main_frame)
        self.notebook.pack(fill='both', expand=True, pady=(10, 0))

        # تبويب الإحصائيات العامة
        self.create_overview_tab()

        # تبويب الأداء
        self.create_performance_tab()

        # تبويب الرسوم البيانية
        if MATPLOTLIB_AVAILABLE:
            self.create_charts_tab()

        # تبويب التسريع الذكي
        self.create_accelerator_tab()

        # تبويب الشبكة
        self.create_network_tab()

        # تبويب التقارير
        self.create_reports_tab()

    def create_toolbar(self, parent):
        """إنشاء شريط الأدوات"""
        toolbar_frame = tk.Frame(parent)
        toolbar_frame.pack(fill='x', pady=(0, 10))
        theme_manager.apply_theme_to_widget(toolbar_frame, "toolbar")

        # عنوان
        title_label = tk.Label(toolbar_frame,
                              text="📊 " + language_manager.get_text("analytics_dashboard", "لوحة الإحصائيات المتقدمة"),
                              font=('Segoe UI', 14, 'bold'))
        title_label.pack(side='left', padx=10, pady=10)
        theme_manager.apply_theme_to_widget(title_label, "label")

        # أزرار التحكم
        controls_frame = tk.Frame(toolbar_frame)
        controls_frame.pack(side='right', padx=10, pady=5)
        theme_manager.apply_theme_to_widget(controls_frame, "frame")

        # زر تحديث
        refresh_btn = tk.Button(controls_frame,
                               text="🔄 " + language_manager.get_text("refresh", "تحديث"),
                               command=self.manual_refresh)
        refresh_btn.pack(side='left', padx=5)
        theme_manager.apply_theme_to_widget(refresh_btn, "button_primary")

        # زر تصدير
        export_btn = tk.Button(controls_frame,
                              text="📤 " + language_manager.get_text("export", "تصدير"),
                              command=self.export_data)
        export_btn.pack(side='left', padx=5)
        theme_manager.apply_theme_to_widget(export_btn, "button_secondary")

        # مفتاح التحديث التلقائي
        self.auto_refresh_var = tk.BooleanVar(value=True)
        auto_refresh_check = tk.Checkbutton(controls_frame,
                                           text=language_manager.get_text("auto_refresh", "تحديث تلقائي"),
                                           variable=self.auto_refresh_var,
                                           command=self.toggle_auto_refresh)
        auto_refresh_check.pack(side='left', padx=10)
        theme_manager.apply_theme_to_widget(auto_refresh_check, "label")

    def create_overview_tab(self):
        """تبويب الإحصائيات العامة"""
        overview_frame = ttk.Frame(self.notebook)
        self.notebook.add(overview_frame, text=language_manager.get_text("overview", "نظرة عامة"))

        # إطار التمرير
        canvas = tk.Canvas(overview_frame)
        scrollbar = ttk.Scrollbar(overview_frame, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        # إحصائيات الجلسة
        self.create_session_stats_panel(scrollable_frame)

        # إحصائيات عامة
        self.create_general_stats_panel(scrollable_frame)

        # إحصائيات الأداء
        self.create_performance_stats_panel(scrollable_frame)

        # ترتيب العناصر
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

    def create_session_stats_panel(self, parent):
        """لوحة إحصائيات الجلسة"""
        session_frame = tk.LabelFrame(parent,
                                     text=" " + language_manager.get_text("session_statistics", "إحصائيات الجلسة") + " ",
                                     font=('Segoe UI', 11, 'bold'))
        session_frame.pack(fill='x', padx=10, pady=10)
        theme_manager.apply_theme_to_widget(session_frame, "labelframe")

        # إطار الإحصائيات
        stats_container = tk.Frame(session_frame)
        stats_container.pack(fill='x', padx=15, pady=15)
        theme_manager.apply_theme_to_widget(stats_container, "frame")

        # الصف الأول
        row1_frame = tk.Frame(stats_container)
        row1_frame.pack(fill='x', pady=5)
        theme_manager.apply_theme_to_widget(row1_frame, "frame")

        self.session_duration_label = self.create_stat_widget(row1_frame, "⏱️", "مدة الجلسة", "00:00:00", 0)
        self.downloads_started_label = self.create_stat_widget(row1_frame, "🚀", "تحميلات بدأت", "0", 1)
        self.downloads_completed_label = self.create_stat_widget(row1_frame, "✅", "تحميلات مكتملة", "0", 2)

        # الصف الثاني
        row2_frame = tk.Frame(stats_container)
        row2_frame.pack(fill='x', pady=5)
        theme_manager.apply_theme_to_widget(row2_frame, "frame")

        self.downloads_failed_label = self.create_stat_widget(row2_frame, "❌", "تحميلات فاشلة", "0", 0)
        self.success_rate_label = self.create_stat_widget(row2_frame, "📈", "معدل النجاح", "0%", 1)
        self.total_downloaded_label = self.create_stat_widget(row2_frame, "💾", "إجمالي المحمل", "0 B", 2)

        # الصف الثالث
        row3_frame = tk.Frame(stats_container)
        row3_frame.pack(fill='x', pady=5)
        theme_manager.apply_theme_to_widget(row3_frame, "frame")

        self.peak_speed_label = self.create_stat_widget(row3_frame, "⚡", "أقصى سرعة", "0 B/s", 0)
        self.avg_speed_label = self.create_stat_widget(row3_frame, "📊", "متوسط السرعة", "0 B/s", 1)
        self.active_downloads_label = self.create_stat_widget(row3_frame, "🔄", "تحميلات نشطة", "0", 2)

    def create_stat_widget(self, parent, icon, label, value, column):
        """إنشاء عنصر إحصائية"""
        stat_frame = tk.Frame(parent)
        stat_frame.grid(row=0, column=column, padx=10, pady=5, sticky='ew')
        theme_manager.apply_theme_to_widget(stat_frame, "panel")
        stat_frame.configure(relief='solid', bd=1)

        parent.columnconfigure(column, weight=1)

        # الأيقونة
        icon_label = tk.Label(stat_frame, text=icon, font=('Segoe UI', 16))
        icon_label.pack(pady=(10, 5))
        theme_manager.apply_theme_to_widget(icon_label, "label")

        # القيمة
        value_label = tk.Label(stat_frame, text=value, font=('Segoe UI', 12, 'bold'))
        value_label.pack()
        theme_manager.apply_theme_to_widget(value_label, "label")

        # التسمية
        label_widget = tk.Label(stat_frame, text=label, font=('Segoe UI', 9))
        label_widget.pack(pady=(0, 10))
        theme_manager.apply_theme_to_widget(label_widget, "label_secondary")

        return value_label

    def create_general_stats_panel(self, parent):
        """لوحة الإحصائيات العامة"""
        general_frame = tk.LabelFrame(parent,
                                     text=" " + language_manager.get_text("general_statistics", "إحصائيات عامة") + " ",
                                     font=('Segoe UI', 11, 'bold'))
        general_frame.pack(fill='x', padx=10, pady=10)
        theme_manager.apply_theme_to_widget(general_frame, "labelframe")

        # جدول الإحصائيات
        stats_tree = ttk.Treeview(general_frame, columns=('Value',), show='tree headings', height=8)
        stats_tree.pack(fill='x', padx=15, pady=15)

        stats_tree.heading('#0', text='الإحصائية')
        stats_tree.heading('Value', text='القيمة')

        stats_tree.column('#0', width=200)
        stats_tree.column('Value', width=150)

        # إضافة البيانات
        self.general_stats_tree = stats_tree
        self.update_general_stats()

    def create_performance_tab(self):
        """تبويب الأداء"""
        performance_frame = ttk.Frame(self.notebook)
        self.notebook.add(performance_frame, text=language_manager.get_text("performance", "الأداء"))

        # مقاييس الأداء
        metrics_frame = tk.LabelFrame(performance_frame,
                                     text=" " + language_manager.get_text("performance_metrics", "مقاييس الأداء") + " ",
                                     font=('Segoe UI', 11, 'bold'))
        metrics_frame.pack(fill='x', padx=10, pady=10)
        theme_manager.apply_theme_to_widget(metrics_frame, "labelframe")

        # جدول مقاييس الأداء
        self.performance_tree = ttk.Treeview(metrics_frame,
                                           columns=('Current', 'Average', 'Best'),
                                           show='tree headings', height=10)
        self.performance_tree.pack(fill='both', expand=True, padx=15, pady=15)

        self.performance_tree.heading('#0', text='المقياس')
        self.performance_tree.heading('Current', text='الحالي')
        self.performance_tree.heading('Average', text='المتوسط')
        self.performance_tree.heading('Best', text='الأفضل')

        # تحديث بيانات الأداء
        self.update_performance_data()

    def create_charts_tab(self):
        """تبويب الرسوم البيانية"""
        if not MATPLOTLIB_AVAILABLE:
            return

        charts_frame = ttk.Frame(self.notebook)
        self.notebook.add(charts_frame, text=language_manager.get_text("charts", "الرسوم البيانية"))

        # إطار الرسوم البيانية
        charts_container = tk.Frame(charts_frame)
        charts_container.pack(fill='both', expand=True, padx=10, pady=10)

        # رسم السرعة
        self.create_speed_chart(charts_container)

        # رسم التحميلات اليومية
        self.create_daily_downloads_chart(charts_container)

    def create_speed_chart(self, parent):
        """إنشاء رسم السرعة"""
        speed_frame = tk.LabelFrame(parent, text=" رسم السرعة ", font=('Segoe UI', 10, 'bold'))
        speed_frame.pack(fill='both', expand=True, pady=(0, 10))

        # إنشاء الرسم
        fig = Figure(figsize=(8, 4), dpi=100)
        self.speed_plot = fig.add_subplot(111)

        # تطبيق ألوان الثيم
        fig.patch.set_facecolor(self.colors['bg_panel'])
        self.speed_plot.set_facecolor(self.colors['bg_panel'])

        # إعداد الرسم
        self.speed_plot.set_title('سرعة التحميل عبر الوقت', color=self.colors['text_primary'])
        self.speed_plot.set_xlabel('الوقت', color=self.colors['text_primary'])
        self.speed_plot.set_ylabel('السرعة (MB/s)', color=self.colors['text_primary'])

        # تطبيق ألوان المحاور
        self.speed_plot.tick_params(colors=self.colors['text_secondary'])
        self.speed_plot.spines['bottom'].set_color(self.colors['border'])
        self.speed_plot.spines['top'].set_color(self.colors['border'])
        self.speed_plot.spines['right'].set_color(self.colors['border'])
        self.speed_plot.spines['left'].set_color(self.colors['border'])

        # إضافة الرسم إلى الواجهة
        canvas = FigureCanvasTkAgg(fig, speed_frame)
        canvas.draw()
        canvas.get_tk_widget().pack(fill='both', expand=True, padx=10, pady=10)

        self.speed_canvas = canvas
        self.speed_figure = fig

    def create_daily_downloads_chart(self, parent):
        """إنشاء رسم التحميلات اليومية"""
        daily_frame = tk.LabelFrame(parent, text=" التحميلات اليومية ", font=('Segoe UI', 10, 'bold'))
        daily_frame.pack(fill='both', expand=True)

        # إنشاء الرسم
        fig = Figure(figsize=(8, 4), dpi=100)
        self.daily_plot = fig.add_subplot(111)

        # تطبيق ألوان الثيم
        fig.patch.set_facecolor(self.colors['bg_panel'])
        self.daily_plot.set_facecolor(self.colors['bg_panel'])

        # إعداد الرسم
        self.daily_plot.set_title('التحميلات اليومية', color=self.colors['text_primary'])
        self.daily_plot.set_xlabel('التاريخ', color=self.colors['text_primary'])
        self.daily_plot.set_ylabel('عدد التحميلات', color=self.colors['text_primary'])

        # تطبيق ألوان المحاور
        self.daily_plot.tick_params(colors=self.colors['text_secondary'])
        for spine in self.daily_plot.spines.values():
            spine.set_color(self.colors['border'])

        # إضافة الرسم إلى الواجهة
        canvas = FigureCanvasTkAgg(fig, daily_frame)
        canvas.draw()
        canvas.get_tk_widget().pack(fill='both', expand=True, padx=10, pady=10)

        self.daily_canvas = canvas
        self.daily_figure = fig

    def create_accelerator_tab(self):
        """تبويب التسريع الذكي"""
        accelerator_frame = ttk.Frame(self.notebook)
        self.notebook.add(accelerator_frame, text=language_manager.get_text("smart_accelerator", "التسريع الذكي"))

        # حالة التسريع
        status_frame = tk.LabelFrame(accelerator_frame,
                                    text=" " + language_manager.get_text("accelerator_status", "حالة التسريع") + " ",
                                    font=('Segoe UI', 11, 'bold'))
        status_frame.pack(fill='x', padx=10, pady=10)
        theme_manager.apply_theme_to_widget(status_frame, "labelframe")

        # معلومات التسريع
        self.accelerator_info_text = tk.Text(status_frame, height=15, wrap='word')
        self.accelerator_info_text.pack(fill='both', expand=True, padx=15, pady=15)
        theme_manager.apply_theme_to_widget(self.accelerator_info_text, "entry")

        # تحديث معلومات التسريع
        self.update_accelerator_info()

    def create_network_tab(self):
        """تبويب الشبكة"""
        network_frame = ttk.Frame(self.notebook)
        self.notebook.add(network_frame, text=language_manager.get_text("network", "الشبكة"))

        # حالة الشبكة
        network_status_frame = tk.LabelFrame(network_frame,
                                           text=" " + language_manager.get_text("network_status", "حالة الشبكة") + " ",
                                           font=('Segoe UI', 11, 'bold'))
        network_status_frame.pack(fill='x', padx=10, pady=10)
        theme_manager.apply_theme_to_widget(network_status_frame, "labelframe")

        # معلومات الشبكة
        self.network_info_text = tk.Text(network_status_frame, height=12, wrap='word')
        self.network_info_text.pack(fill='both', expand=True, padx=15, pady=15)
        theme_manager.apply_theme_to_widget(self.network_info_text, "entry")

        # تحديث معلومات الشبكة
        self.update_network_info()

    def create_reports_tab(self):
        """تبويب التقارير"""
        reports_frame = ttk.Frame(self.notebook)
        self.notebook.add(reports_frame, text=language_manager.get_text("reports", "التقارير"))

        # أزرار التقارير
        buttons_frame = tk.Frame(reports_frame)
        buttons_frame.pack(fill='x', padx=10, pady=10)
        theme_manager.apply_theme_to_widget(buttons_frame, "frame")

        # زر تقرير يومي
        daily_report_btn = tk.Button(buttons_frame,
                                    text="📊 " + language_manager.get_text("daily_report", "تقرير يومي"),
                                    command=lambda: self.generate_report('daily'))
        daily_report_btn.pack(side='left', padx=5)
        theme_manager.apply_theme_to_widget(daily_report_btn, "button_primary")

        # زر تقرير أسبوعي
        weekly_report_btn = tk.Button(buttons_frame,
                                     text="📈 " + language_manager.get_text("weekly_report", "تقرير أسبوعي"),
                                     command=lambda: self.generate_report('weekly'))
        weekly_report_btn.pack(side='left', padx=5)
        theme_manager.apply_theme_to_widget(weekly_report_btn, "button_secondary")

        # زر تقرير شهري
        monthly_report_btn = tk.Button(buttons_frame,
                                      text="📅 " + language_manager.get_text("monthly_report", "تقرير شهري"),
                                      command=lambda: self.generate_report('monthly'))
        monthly_report_btn.pack(side='left', padx=5)
        theme_manager.apply_theme_to_widget(monthly_report_btn, "button_secondary")

        # منطقة عرض التقرير
        report_frame = tk.LabelFrame(reports_frame,
                                    text=" " + language_manager.get_text("report_content", "محتوى التقرير") + " ",
                                    font=('Segoe UI', 11, 'bold'))
        report_frame.pack(fill='both', expand=True, padx=10, pady=10)
        theme_manager.apply_theme_to_widget(report_frame, "labelframe")

        # نص التقرير
        self.report_text = tk.Text(report_frame, wrap='word')
        self.report_text.pack(fill='both', expand=True, padx=15, pady=15)
        theme_manager.apply_theme_to_widget(self.report_text, "entry")

        # شريط تمرير للتقرير
        report_scrollbar = ttk.Scrollbar(report_frame, orient="vertical", command=self.report_text.yview)
        report_scrollbar.pack(side="right", fill="y")
        self.report_text.configure(yscrollcommand=report_scrollbar.set)

    def start_auto_refresh(self):
        """بدء التحديث التلقائي"""
        def refresh_loop():
            while self.auto_refresh:
                try:
                    if self.auto_refresh_var.get():
                        self.update_all_data()
                    time.sleep(self.refresh_interval)
                except Exception as e:
                    print(f"خطأ في التحديث التلقائي: {e}")
                    time.sleep(10)

        refresh_thread = threading.Thread(target=refresh_loop, daemon=True)
        refresh_thread.start()

    def update_all_data(self):
        """تحديث جميع البيانات"""
        try:
            # تحديث إحصائيات الجلسة
            self.update_session_stats()

            # تحديث الإحصائيات العامة
            self.update_general_stats()

            # تحديث بيانات الأداء
            self.update_performance_data()

            # تحديث الرسوم البيانية
            if MATPLOTLIB_AVAILABLE:
                self.update_charts()

            # تحديث معلومات التسريع
            self.update_accelerator_info()

            # تحديث معلومات الشبكة
            self.update_network_info()

            # تحديث وقت آخر تحديث
            self.last_update = datetime.now()

        except Exception as e:
            print(f"خطأ في تحديث البيانات: {e}")

    def update_session_stats(self):
        """تحديث إحصائيات الجلسة"""
        try:
            stats = self.analytics_system.get_session_statistics()

            # تحديث التسميات
            duration = int(stats['session_duration'])
            hours = duration // 3600
            minutes = (duration % 3600) // 60
            seconds = duration % 60
            duration_str = f"{hours:02d}:{minutes:02d}:{seconds:02d}"

            self.session_duration_label.config(text=duration_str)
            self.downloads_started_label.config(text=str(stats['downloads_started']))
            self.downloads_completed_label.config(text=str(stats['downloads_completed']))
            self.downloads_failed_label.config(text=str(stats['downloads_failed']))
            self.success_rate_label.config(text=f"{stats['success_rate']:.1f}%")
            self.total_downloaded_label.config(text=stats['total_bytes_formatted'])
            self.peak_speed_label.config(text=stats['peak_speed_formatted'])
            self.avg_speed_label.config(text=stats['average_speed_formatted'])
            self.active_downloads_label.config(text=str(stats['active_downloads']))

        except Exception as e:
            print(f"خطأ في تحديث إحصائيات الجلسة: {e}")

    def update_general_stats(self):
        """تحديث الإحصائيات العامة"""
        try:
            # مسح البيانات الحالية
            for item in self.general_stats_tree.get_children():
                self.general_stats_tree.delete(item)

            # الحصول على البيانات
            daily_stats = self.analytics_system.get_daily_statistics(7)

            # حساب الإحصائيات
            total_downloads = sum(day['downloads_started'] for day in daily_stats)
            total_completed = sum(day['downloads_completed'] for day in daily_stats)
            total_failed = sum(day['downloads_failed'] for day in daily_stats)
            total_bytes = sum(day['total_bytes'] for day in daily_stats)

            # إضافة البيانات
            self.general_stats_tree.insert('', 'end', text='إجمالي التحميلات (7 أيام)', values=(str(total_downloads),))
            self.general_stats_tree.insert('', 'end', text='التحميلات المكتملة', values=(str(total_completed),))
            self.general_stats_tree.insert('', 'end', text='التحميلات الفاشلة', values=(str(total_failed),))
            self.general_stats_tree.insert('', 'end', text='إجمالي البيانات المحملة', values=(self.format_size(total_bytes),))

            if total_downloads > 0:
                success_rate = (total_completed / total_downloads) * 100
                self.general_stats_tree.insert('', 'end', text='معدل النجاح الإجمالي', values=(f"{success_rate:.1f}%",))

        except Exception as e:
            print(f"خطأ في تحديث الإحصائيات العامة: {e}")

    def update_performance_data(self):
        """تحديث بيانات الأداء"""
        try:
            # مسح البيانات الحالية
            for item in self.performance_tree.get_children():
                self.performance_tree.delete(item)

            # الحصول على مقاييس الأداء
            performance = self.analytics_system.get_performance_metrics()
            speed_analytics = self.analytics_system.get_speed_analytics()

            # إضافة البيانات
            self.performance_tree.insert('', 'end', text='معدل النجاح',
                                       values=(f"{performance['success_rate']:.1f}%",
                                              f"{performance['success_rate']:.1f}%",
                                              "100%"))

            self.performance_tree.insert('', 'end', text='معدل الفشل',
                                       values=(f"{performance['failure_rate']:.1f}%",
                                              f"{performance['failure_rate']:.1f}%",
                                              "0%"))

            self.performance_tree.insert('', 'end', text='السرعة الحالية',
                                       values=(speed_analytics['current_speed_formatted'],
                                              speed_analytics['average_speed_formatted'],
                                              speed_analytics['max_speed_formatted']))

            self.performance_tree.insert('', 'end', text='متوسط وقت التحميل',
                                       values=(performance['average_download_time_formatted'],
                                              performance['average_download_time_formatted'],
                                              "N/A"))

            self.performance_tree.insert('', 'end', text='نقاط الكفاءة',
                                       values=(f"{performance['efficiency_score']:.1f}",
                                              f"{performance['efficiency_score']:.1f}",
                                              "100"))

        except Exception as e:
            print(f"خطأ في تحديث بيانات الأداء: {e}")

    def update_charts(self):
        """تحديث الرسوم البيانية"""
        if not MATPLOTLIB_AVAILABLE:
            return

        try:
            # تحديث رسم السرعة
            self.update_speed_chart()

            # تحديث رسم التحميلات اليومية
            self.update_daily_chart()

        except Exception as e:
            print(f"خطأ في تحديث الرسوم البيانية: {e}")

    def update_speed_chart(self):
        """تحديث رسم السرعة"""
        try:
            # الحصول على بيانات السرعة
            speed_data = list(self.analytics_system.real_time_data['speed_history'])

            if not speed_data:
                return

            # استخراج البيانات
            times = [item['timestamp'] for item in speed_data[-50:]]  # آخر 50 نقطة
            speeds = [item['speed'] / (1024*1024) for item in speed_data[-50:]]  # تحويل إلى MB/s

            # مسح الرسم
            self.speed_plot.clear()

            # رسم البيانات
            self.speed_plot.plot(times, speeds, color=self.colors['primary'], linewidth=2)
            self.speed_plot.fill_between(times, speeds, alpha=0.3, color=self.colors['primary'])

            # إعداد الرسم
            self.speed_plot.set_title('سرعة التحميل عبر الوقت', color=self.colors['text_primary'])
            self.speed_plot.set_xlabel('الوقت', color=self.colors['text_primary'])
            self.speed_plot.set_ylabel('السرعة (MB/s)', color=self.colors['text_primary'])

            # تنسيق المحاور
            self.speed_plot.tick_params(colors=self.colors['text_secondary'])
            for spine in self.speed_plot.spines.values():
                spine.set_color(self.colors['border'])

            # تحديث الرسم
            self.speed_canvas.draw()

        except Exception as e:
            print(f"خطأ في تحديث رسم السرعة: {e}")

    def update_daily_chart(self):
        """تحديث رسم التحميلات اليومية"""
        try:
            # الحصول على البيانات اليومية
            daily_data = self.analytics_system.get_daily_statistics(7)

            if not daily_data:
                return

            # استخراج البيانات
            dates = [datetime.strptime(day['date'], '%Y-%m-%d') for day in daily_data]
            downloads_started = [day['downloads_started'] for day in daily_data]
            downloads_completed = [day['downloads_completed'] for day in daily_data]
            downloads_failed = [day['downloads_failed'] for day in daily_data]

            # مسح الرسم
            self.daily_plot.clear()

            # رسم البيانات
            width = 0.25
            x_pos = range(len(dates))

            self.daily_plot.bar([x - width for x in x_pos], downloads_started,
                              width, label='بدأت', color=self.colors['info'])
            self.daily_plot.bar(x_pos, downloads_completed,
                              width, label='مكتملة', color=self.colors['success'])
            self.daily_plot.bar([x + width for x in x_pos], downloads_failed,
                              width, label='فاشلة', color=self.colors['error'])

            # إعداد الرسم
            self.daily_plot.set_title('التحميلات اليومية', color=self.colors['text_primary'])
            self.daily_plot.set_xlabel('التاريخ', color=self.colors['text_primary'])
            self.daily_plot.set_ylabel('عدد التحميلات', color=self.colors['text_primary'])

            # تنسيق المحاور
            self.daily_plot.set_xticks(x_pos)
            self.daily_plot.set_xticklabels([d.strftime('%m-%d') for d in dates])
            self.daily_plot.tick_params(colors=self.colors['text_secondary'])

            for spine in self.daily_plot.spines.values():
                spine.set_color(self.colors['border'])

            # إضافة وسيلة الإيضاح
            self.daily_plot.legend()

            # تحديث الرسم
            self.daily_canvas.draw()

        except Exception as e:
            print(f"خطأ في تحديث رسم التحميلات اليومية: {e}")

    def update_accelerator_info(self):
        """تحديث معلومات التسريع"""
        try:
            status = self.smart_accelerator.get_acceleration_status()

            info_text = f"""
حالة التسريع الذكي:
{'=' * 50}

الحالة: {'مفعل' if status['enabled'] else 'معطل'}
وضع التعلم: {'مفعل' if status['learning_mode'] else 'معطل'}
مستوى التحسين: {status['optimization_level']}

الإعدادات المثلى الحالية:
- عدد الاتصالات: {status['optimal_connections']}
- حجم الجزء: {self.format_size(status['optimal_chunk_size'])}

إحصائيات التعلم:
- عينات الأداء: {status['performance_samples']}
- الخوادم المحللة: {status['servers_analyzed']}
- جودة الشبكة: {status['network_quality']}

خوارزميات التحسين النشطة:
- التكيف مع عدد الاتصالات: ✅
- تحجيم الأجزاء الديناميكي: ✅
- توازن حمولة الخادم: ✅
- التكيف مع حالة الشبكة: ✅
- التحسين التنبؤي: ✅

آخر تحديث: {datetime.now().strftime('%H:%M:%S')}
            """

            self.accelerator_info_text.delete('1.0', tk.END)
            self.accelerator_info_text.insert('1.0', info_text)

        except Exception as e:
            print(f"خطأ في تحديث معلومات التسريع: {e}")

    def update_network_info(self):
        """تحديث معلومات الشبكة"""
        try:
            # الحصول على حالة الشبكة من المراقب
            from smart_accelerator import network_monitor
            network_status = network_monitor.get_network_status()

            info_text = f"""
حالة الشبكة:
{'=' * 50}

جودة الاتصال: {network_status['connection_quality']}
متوسط زمن الاستجابة: {network_status['average_ping']:.2f} ms إذا كان متوفراً
متوسط عرض النطاق: {self.format_speed(network_status['average_bandwidth']) if network_status['average_bandwidth'] else 'غير متوفر'}

عينات البيانات:
- عينات زمن الاستجابة: {network_status['ping_samples']}
- عينات عرض النطاق: {network_status['bandwidth_samples']}

حالة الاتصال:
- الحالة: {'متصل' if network_status['connection_quality'] != 'disconnected' else 'غير متصل'}
- الاستقرار: {'مستقر' if network_status['connection_quality'] in ['excellent', 'good'] else 'غير مستقر'}

توصيات التحسين:
"""

            # إضافة توصيات حسب جودة الشبكة
            quality = network_status['connection_quality']
            if quality == 'excellent':
                info_text += "- يمكن استخدام أقصى عدد اتصالات\n- السرعة مثلى\n"
            elif quality == 'good':
                info_text += "- استخدام عدد اتصالات متوسط\n- أداء جيد متوقع\n"
            elif quality == 'fair':
                info_text += "- تقليل عدد الاتصالات\n- قد تحدث انقطاعات\n"
            elif quality == 'poor':
                info_text += "- استخدام اتصال واحد أو اثنين\n- توقع سرعات منخفضة\n"
            else:
                info_text += "- فحص الاتصال بالإنترنت\n- إعادة المحاولة لاحقاً\n"

            info_text += f"\nآخر تحديث: {datetime.now().strftime('%H:%M:%S')}"

            self.network_info_text.delete('1.0', tk.END)
            self.network_info_text.insert('1.0', info_text)

        except Exception as e:
            print(f"خطأ في تحديث معلومات الشبكة: {e}")

    def generate_report(self, report_type):
        """إنشاء تقرير"""
        try:
            report = self.analytics_system.generate_report(report_type)

            report_text = f"""
تقرير {report_type.upper()}
{'=' * 60}
تاريخ الإنشاء: {report['generated_at'].strftime('%Y-%m-%d %H:%M:%S')}

إحصائيات الجلسة:
- مدة الجلسة: {self.format_time(report['session_stats']['session_duration'])}
- التحميلات المبدوءة: {report['session_stats']['downloads_started']}
- التحميلات المكتملة: {report['session_stats']['downloads_completed']}
- التحميلات الفاشلة: {report['session_stats']['downloads_failed']}
- معدل النجاح: {report['session_stats']['success_rate']:.1f}%
- إجمالي البيانات: {report['session_stats']['total_bytes_formatted']}
- أقصى سرعة: {report['session_stats']['peak_speed_formatted']}
- متوسط السرعة: {report['session_stats']['average_speed_formatted']}

تحليل السرعة:
- السرعة الحالية: {report['speed_analytics']['current_speed_formatted']}
- متوسط السرعة: {report['speed_analytics']['average_speed_formatted']}
- أقصى سرعة: {report['speed_analytics']['max_speed_formatted']}
- أدنى سرعة: {report['speed_analytics']['min_speed_formatted']}
- اتجاه السرعة: {report['speed_analytics']['speed_trend']}

مقاييس الأداء:
- معدل النجاح: {report['performance_metrics']['success_rate']:.1f}%
- معدل الفشل: {report['performance_metrics']['failure_rate']:.1f}%
- متوسط وقت التحميل: {report['performance_metrics']['average_download_time_formatted']}
- نقاط الكفاءة: {report['performance_metrics']['efficiency_score']:.1f}/100
            """

            # إضافة البيانات التاريخية حسب نوع التقرير
            if f'{report_type}_data' in report:
                report_text += f"\n\nالبيانات ال{report_type}:\n"
                report_text += "-" * 40 + "\n"

                for day_data in report[f'{report_type}_data']:
                    report_text += f"التاريخ: {day_data['date']}\n"
                    report_text += f"  - بدأت: {day_data['downloads_started']}\n"
                    report_text += f"  - مكتملة: {day_data['downloads_completed']}\n"
                    report_text += f"  - فاشلة: {day_data['downloads_failed']}\n"
                    report_text += f"  - البيانات: {self.format_size(day_data['total_bytes'])}\n\n"

            # عرض التقرير
            self.report_text.delete('1.0', tk.END)
            self.report_text.insert('1.0', report_text)

        except Exception as e:
            error_msg = f"خطأ في إنشاء التقرير: {str(e)}"
            self.report_text.delete('1.0', tk.END)
            self.report_text.insert('1.0', error_msg)

    def manual_refresh(self):
        """تحديث يدوي"""
        self.update_all_data()
        messagebox.showinfo(language_manager.get_text("info"),
                          language_manager.get_text("data_refreshed", "تم تحديث البيانات"))

    def toggle_auto_refresh(self):
        """تبديل التحديث التلقائي"""
        self.auto_refresh = self.auto_refresh_var.get()

    def export_data(self):
        """تصدير البيانات"""
        try:
            filename = self.analytics_system.export_data('json')
            if filename:
                messagebox.showinfo(language_manager.get_text("success"),
                                  f"تم تصدير البيانات إلى: {filename}")
            else:
                messagebox.showerror(language_manager.get_text("error"),
                                   "فشل في تصدير البيانات")
        except Exception as e:
            messagebox.showerror(language_manager.get_text("error"),
                               f"خطأ في التصدير: {str(e)}")

    def format_size(self, size_bytes):
        """تنسيق حجم الملف"""
        if size_bytes == 0:
            return "0 B"

        size_names = ["B", "KB", "MB", "GB", "TB"]
        i = 0
        while size_bytes >= 1024 and i < len(size_names) - 1:
            size_bytes /= 1024.0
            i += 1

        return f"{size_bytes:.2f} {size_names[i]}"

    def format_speed(self, speed_bps):
        """تنسيق السرعة"""
        return self.format_size(speed_bps) + "/s"

    def format_time(self, seconds):
        """تنسيق الوقت"""
        if seconds < 60:
            return f"{int(seconds)}s"
        elif seconds < 3600:
            return f"{int(seconds / 60)}m {int(seconds % 60)}s"
        else:
            hours = int(seconds / 3600)
            minutes = int((seconds % 3600) / 60)
            return f"{hours}h {minutes}m"

    def center_window(self):
        """توسيط النافذة"""
        self.window.update_idletasks()
        x = (self.window.winfo_screenwidth() // 2) - (self.window.winfo_width() // 2)
        y = (self.window.winfo_screenheight() // 2) - (self.window.winfo_height() // 2)
        self.window.geometry(f"+{x}+{y}")

    def show(self):
        """عرض النافذة"""
        self.window.deiconify()
        self.window.lift()
        self.window.focus_force()

    def close(self):
        """إغلاق النافذة"""
        self.auto_refresh = False
        self.window.destroy()