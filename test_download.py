#!/usr/bin/env python3
"""
ملف اختبار لبرنامج مدير التحميل
Test file for Download Manager
"""

import os
import sys
from downloader import Downloader
from utils import URLUtils, FileUtils, ValidationUtils

def test_url_validation():
    """اختبار التحقق من صحة الروابط"""
    print("=== اختبار التحقق من صحة الروابط ===")
    
    test_urls = [
        "https://www.google.com",
        "http://httpbin.org/get",
        "invalid-url",
        "ftp://example.com/file.txt",
        "https://httpbin.org/status/404"
    ]
    
    for url in test_urls:
        is_valid = URLUtils.is_valid_url(url)
        print(f"الرابط: {url}")
        print(f"صحيح: {'نعم' if is_valid else 'لا'}")
        
        if is_valid:
            accessible, message = ValidationUtils.validate_url_accessibility(url)
            print(f"متاح: {'نعم' if accessible else 'لا'} - {message}")
        
        print("-" * 50)

def test_filename_extraction():
    """اختبار استخراج أسماء الملفات"""
    print("\n=== اختبار استخراج أسماء الملفات ===")
    
    test_urls = [
        "https://example.com/file.pdf",
        "https://example.com/path/to/document.docx",
        "https://example.com/download?file=image.jpg",
        "https://example.com/",
        "https://httpbin.org/json"
    ]
    
    for url in test_urls:
        filename = URLUtils.get_filename_from_url(url)
        print(f"الرابط: {url}")
        print(f"اسم الملف: {filename}")
        print("-" * 50)

def test_file_utils():
    """اختبار أدوات الملفات"""
    print("\n=== اختبار أدوات الملفات ===")
    
    # اختبار تحويل الأحجام
    sizes = [0, 1024, 1048576, 1073741824, 1099511627776]
    
    for size in sizes:
        formatted = FileUtils.get_file_size_formatted(size)
        print(f"الحجم: {size} بايت = {formatted}")
    
    print("-" * 50)
    
    # اختبار تنظيف أسماء الملفات
    filenames = [
        "normal_file.txt",
        "file with spaces.pdf",
        "file<with>invalid:chars.doc",
        "file/with\\slashes.txt",
        "file|with?special*chars.zip"
    ]
    
    for filename in filenames:
        clean = FileUtils.sanitize_filename(filename)
        print(f"الأصلي: {filename}")
        print(f"المنظف: {clean}")
        print("-" * 30)

def test_download_path():
    """اختبار مسار التحميل"""
    print("\n=== اختبار مسار التحميل ===")
    
    test_paths = [
        os.path.expanduser("~/Downloads"),
        os.path.expanduser("~/Desktop"),
        "C:\\temp",
        "/tmp",
        "invalid/path/that/does/not/exist"
    ]
    
    for path in test_paths:
        valid, message = ValidationUtils.validate_download_path(path)
        print(f"المسار: {path}")
        print(f"صحيح: {'نعم' if valid else 'لا'} - {message}")
        print("-" * 50)

def test_simple_download():
    """اختبار تحميل بسيط"""
    print("\n=== اختبار تحميل بسيط ===")
    
    # رابط ملف صغير للاختبار
    test_url = "https://httpbin.org/json"
    download_path = os.path.expanduser("~/Downloads")
    
    print(f"اختبار تحميل من: {test_url}")
    print(f"إلى المجلد: {download_path}")
    
    # إنشاء مثيل من المحمل
    downloader = Downloader()
    
    # دالة لعرض التقدم
    def show_progress(progress_data):
        percentage = progress_data.get('percentage', 0)
        downloaded = progress_data.get('downloaded_formatted', '0 B')
        total = progress_data.get('total_formatted', '0 B')
        speed = progress_data.get('speed', '0 B/s')
        
        print(f"\rالتقدم: {percentage:.1f}% ({downloaded}/{total}) - السرعة: {speed}", end='')
    
    # دالة عند الانتهاء
    def download_complete(file_path):
        print(f"\nتم التحميل بنجاح: {file_path}")
    
    # دالة عند حدوث خطأ
    def download_error(error_msg):
        print(f"\nخطأ في التحميل: {error_msg}")
    
    # تعيين دوال الاستدعاء
    downloader.set_callbacks(
        progress_callback=show_progress,
        completion_callback=download_complete,
        error_callback=download_error
    )
    
    # بدء التحميل
    if downloader.start_download(test_url, download_path, "test_file.json"):
        print("تم بدء التحميل...")
        
        # انتظار انتهاء التحميل
        import time
        while downloader.is_downloading:
            time.sleep(0.1)
    else:
        print("فشل في بدء التحميل")

def main():
    """الدالة الرئيسية للاختبار"""
    print("برنامج اختبار مدير التحميل")
    print("=" * 50)
    
    try:
        test_url_validation()
        test_filename_extraction()
        test_file_utils()
        test_download_path()
        
        # اختبار التحميل فقط إذا كان المستخدم يريد ذلك
        response = input("\nهل تريد اختبار التحميل الفعلي؟ (y/n): ")
        if response.lower() in ['y', 'yes', 'نعم']:
            test_simple_download()
        
        print("\n" + "=" * 50)
        print("انتهى الاختبار بنجاح!")
        
    except KeyboardInterrupt:
        print("\nتم إيقاف الاختبار بواسطة المستخدم")
    except Exception as e:
        print(f"\nخطأ في الاختبار: {e}")

if __name__ == "__main__":
    main()
