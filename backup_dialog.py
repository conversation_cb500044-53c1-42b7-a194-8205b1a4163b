"""
نافذة مدير النسخ الاحتياطية - Backup Manager Dialog
إدارة شاملة للنسخ الاحتياطية والاستعادة
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
from datetime import datetime
from theme_manager import theme_manager
from language_manager import language_manager

class BackupManagerDialog:
    """نافذة مدير النسخ الاحتياطية"""
    
    def __init__(self, parent, backup_system):
        self.parent = parent
        self.backup_system = backup_system
        
        # إنشاء النافذة
        self.dialog = tk.Toplevel(parent)
        self.dialog.title(language_manager.get_text("backup_manager", "مدير النسخ الاحتياطية"))
        self.dialog.geometry("900x700")
        self.dialog.resizable(True, True)
        self.dialog.transient(parent)
        self.dialog.grab_set()
        
        # الحصول على الثيم الحالي
        self.colors = theme_manager.get_current_theme()
        
        # تطبيق الثيم على النافذة
        theme_manager.apply_theme_to_widget(self.dialog, "main_window")
        
        # إعداد الواجهة
        self.setup_ui()
        
        # تحميل البيانات
        self.refresh_backups()
        
        # توسيط النافذة
        self.center_window()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # الإطار الرئيسي
        main_frame = tk.Frame(self.dialog)
        main_frame.pack(fill='both', expand=True, padx=10, pady=10)
        theme_manager.apply_theme_to_widget(main_frame, "frame")
        
        # شريط الأدوات العلوي
        self.create_toolbar(main_frame)
        
        # دفتر التبويبات
        self.notebook = ttk.Notebook(main_frame)
        self.notebook.pack(fill='both', expand=True, pady=(10, 0))
        
        # تبويب النسخ الاحتياطية
        self.create_backups_tab()
        
        # تبويب الإعدادات
        self.create_settings_tab()
        
        # تبويب الإحصائيات
        self.create_statistics_tab()
        
        # أزرار التحكم
        self.create_control_buttons(main_frame)
    
    def create_toolbar(self, parent):
        """إنشاء شريط الأدوات"""
        toolbar_frame = tk.Frame(parent)
        toolbar_frame.pack(fill='x', pady=(0, 10))
        theme_manager.apply_theme_to_widget(toolbar_frame, "toolbar")
        
        # عنوان
        title_label = tk.Label(toolbar_frame, 
                              text="💾 " + language_manager.get_text("backup_manager", "مدير النسخ الاحتياطية"),
                              font=('Segoe UI', 12, 'bold'))
        title_label.pack(side='left', padx=10, pady=10)
        theme_manager.apply_theme_to_widget(title_label, "label")
        
        # أزرار سريعة
        quick_buttons_frame = tk.Frame(toolbar_frame)
        quick_buttons_frame.pack(side='right', padx=10, pady=5)
        theme_manager.apply_theme_to_widget(quick_buttons_frame, "frame")
        
        # زر نسخة احتياطية سريعة
        quick_backup_btn = tk.Button(quick_buttons_frame, 
                                    text="⚡ " + language_manager.get_text("quick_backup", "نسخة سريعة"),
                                    command=self.create_quick_backup)
        quick_backup_btn.pack(side='left', padx=5)
        theme_manager.apply_theme_to_widget(quick_backup_btn, "button_primary")
        
        # زر تحديث
        refresh_btn = tk.Button(quick_buttons_frame, 
                               text="🔄 " + language_manager.get_text("refresh", "تحديث"),
                               command=self.refresh_backups)
        refresh_btn.pack(side='left', padx=5)
        theme_manager.apply_theme_to_widget(refresh_btn, "button_secondary")
    
    def create_backups_tab(self):
        """تبويب النسخ الاحتياطية"""
        backups_frame = ttk.Frame(self.notebook)
        self.notebook.add(backups_frame, text=language_manager.get_text("backups", "النسخ الاحتياطية"))
        
        # إطار قائمة النسخ الاحتياطية
        list_frame = tk.LabelFrame(backups_frame, 
                                  text=" " + language_manager.get_text("available_backups", "النسخ المتاحة") + " ",
                                  font=('Segoe UI', 10, 'bold'))
        list_frame.pack(fill='both', expand=True, padx=10, pady=10)
        theme_manager.apply_theme_to_widget(list_frame, "labelframe")
        
        # جدول النسخ الاحتياطية
        columns = ('الاسم', 'التاريخ', 'الحجم', 'الملفات', 'النوع')
        self.backups_tree = ttk.Treeview(list_frame, columns=columns, show='headings', height=15)
        
        # تكوين الأعمدة
        column_widths = {
            'الاسم': 200,
            'التاريخ': 150,
            'الحجم': 100,
            'الملفات': 80,
            'النوع': 100
        }
        
        for col in columns:
            self.backups_tree.heading(col, text=col)
            self.backups_tree.column(col, width=column_widths.get(col, 100), minwidth=50)
        
        # أشرطة التمرير
        v_scrollbar = ttk.Scrollbar(list_frame, orient='vertical', command=self.backups_tree.yview)
        h_scrollbar = ttk.Scrollbar(list_frame, orient='horizontal', command=self.backups_tree.xview)
        
        self.backups_tree.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)
        
        # ترتيب العناصر
        self.backups_tree.grid(row=0, column=0, sticky='nsew', padx=10, pady=10)
        v_scrollbar.grid(row=0, column=1, sticky='ns')
        h_scrollbar.grid(row=1, column=0, sticky='ew')
        
        list_frame.columnconfigure(0, weight=1)
        list_frame.rowconfigure(0, weight=1)
        
        # ربط الأحداث
        self.backups_tree.bind('<Double-1>', self.on_backup_double_click)
        self.backups_tree.bind('<Button-3>', self.show_backup_context_menu)
        
        # أزرار إدارة النسخ
        buttons_frame = tk.Frame(backups_frame)
        buttons_frame.pack(fill='x', padx=10, pady=10)
        theme_manager.apply_theme_to_widget(buttons_frame, "frame")
        
        # زر إنشاء نسخة احتياطية
        create_btn = tk.Button(buttons_frame, 
                              text="➕ " + language_manager.get_text("create_backup", "إنشاء نسخة"),
                              command=self.show_create_backup_dialog)
        create_btn.pack(side='left', padx=5)
        theme_manager.apply_theme_to_widget(create_btn, "button_success")
        
        # زر استعادة
        restore_btn = tk.Button(buttons_frame, 
                               text="♻️ " + language_manager.get_text("restore", "استعادة"),
                               command=self.restore_selected_backup)
        restore_btn.pack(side='left', padx=5)
        theme_manager.apply_theme_to_widget(restore_btn, "button_primary")
        
        # زر حذف
        delete_btn = tk.Button(buttons_frame, 
                              text="🗑️ " + language_manager.get_text("delete", "حذف"),
                              command=self.delete_selected_backup)
        delete_btn.pack(side='left', padx=5)
        theme_manager.apply_theme_to_widget(delete_btn, "button_error")
        
        # زر تصدير
        export_btn = tk.Button(buttons_frame, 
                              text="📤 " + language_manager.get_text("export", "تصدير"),
                              command=self.export_selected_backup)
        export_btn.pack(side='left', padx=5)
        theme_manager.apply_theme_to_widget(export_btn, "button_secondary")
        
        # زر استيراد
        import_btn = tk.Button(buttons_frame, 
                              text="📥 " + language_manager.get_text("import", "استيراد"),
                              command=self.import_backup)
        import_btn.pack(side='left', padx=5)
        theme_manager.apply_theme_to_widget(import_btn, "button_secondary")
    
    def create_settings_tab(self):
        """تبويب الإعدادات"""
        settings_frame = ttk.Frame(self.notebook)
        self.notebook.add(settings_frame, text=language_manager.get_text("settings", "الإعدادات"))
        
        # إعدادات النسخ الاحتياطي التلقائي
        auto_frame = tk.LabelFrame(settings_frame, 
                                  text=" " + language_manager.get_text("auto_backup_settings", "إعدادات النسخ التلقائي") + " ",
                                  font=('Segoe UI', 10, 'bold'))
        auto_frame.pack(fill='x', padx=10, pady=10)
        theme_manager.apply_theme_to_widget(auto_frame, "labelframe")
        
        # تفعيل النسخ التلقائي
        self.auto_backup_var = tk.BooleanVar(value=self.backup_system.auto_backup_enabled)
        auto_check = tk.Checkbutton(auto_frame, 
                                   text=language_manager.get_text("enable_auto_backup", "تفعيل النسخ التلقائي"),
                                   variable=self.auto_backup_var,
                                   command=self.update_auto_backup_setting)
        auto_check.pack(anchor='w', padx=15, pady=10)
        theme_manager.apply_theme_to_widget(auto_check, "label")
        
        # فترة النسخ التلقائي
        interval_frame = tk.Frame(auto_frame)
        interval_frame.pack(fill='x', padx=15, pady=5)
        theme_manager.apply_theme_to_widget(interval_frame, "frame")
        
        tk.Label(interval_frame, text=language_manager.get_text("backup_interval", "فترة النسخ (ساعات)") + ":").pack(side='left')
        theme_manager.apply_theme_to_widget(interval_frame.winfo_children()[-1], "label")
        
        self.interval_var = tk.IntVar(value=self.backup_system.backup_interval_hours)
        interval_spin = tk.Spinbox(interval_frame, from_=1, to=168, textvariable=self.interval_var, width=10)
        interval_spin.pack(side='left', padx=10)
        
        # الحد الأقصى للنسخ
        max_frame = tk.Frame(auto_frame)
        max_frame.pack(fill='x', padx=15, pady=5)
        theme_manager.apply_theme_to_widget(max_frame, "frame")
        
        tk.Label(max_frame, text=language_manager.get_text("max_backups", "الحد الأقصى للنسخ") + ":").pack(side='left')
        theme_manager.apply_theme_to_widget(max_frame.winfo_children()[-1], "label")
        
        self.max_backups_var = tk.IntVar(value=self.backup_system.max_backups)
        max_spin = tk.Spinbox(max_frame, from_=1, to=50, textvariable=self.max_backups_var, width=10)
        max_spin.pack(side='left', padx=10)
        
        # ضغط الملفات
        self.compression_var = tk.BooleanVar(value=self.backup_system.compression_enabled)
        compression_check = tk.Checkbutton(auto_frame, 
                                          text=language_manager.get_text("enable_compression", "تفعيل ضغط الملفات"),
                                          variable=self.compression_var)
        compression_check.pack(anchor='w', padx=15, pady=10)
        theme_manager.apply_theme_to_widget(compression_check, "label")
        
        # زر حفظ الإعدادات
        save_settings_btn = tk.Button(auto_frame, 
                                     text="💾 " + language_manager.get_text("save_settings", "حفظ الإعدادات"),
                                     command=self.save_backup_settings)
        save_settings_btn.pack(pady=15)
        theme_manager.apply_theme_to_widget(save_settings_btn, "button_primary")
    
    def create_statistics_tab(self):
        """تبويب الإحصائيات"""
        stats_frame = ttk.Frame(self.notebook)
        self.notebook.add(stats_frame, text=language_manager.get_text("statistics", "إحصائيات"))
        
        # إحصائيات النسخ الاحتياطية
        stats_info_frame = tk.LabelFrame(stats_frame, 
                                        text=" " + language_manager.get_text("backup_statistics", "إحصائيات النسخ") + " ",
                                        font=('Segoe UI', 10, 'bold'))
        stats_info_frame.pack(fill='both', expand=True, padx=10, pady=10)
        theme_manager.apply_theme_to_widget(stats_info_frame, "labelframe")
        
        # نص الإحصائيات
        self.stats_text = tk.Text(stats_info_frame, height=20, wrap='word')
        self.stats_text.pack(fill='both', expand=True, padx=15, pady=15)
        theme_manager.apply_theme_to_widget(self.stats_text, "entry")
        
        # تحديث الإحصائيات
        self.update_statistics()
    
    def create_control_buttons(self, parent):
        """إنشاء أزرار التحكم"""
        buttons_frame = tk.Frame(parent)
        buttons_frame.pack(fill='x', pady=(10, 0))
        theme_manager.apply_theme_to_widget(buttons_frame, "frame")
        
        # زر إغلاق
        close_btn = tk.Button(buttons_frame, 
                             text="❌ " + language_manager.get_text("close", "إغلاق"),
                             command=self.close_dialog)
        close_btn.pack(side='right', padx=5)
        theme_manager.apply_theme_to_widget(close_btn, "button_secondary")
    
    def refresh_backups(self):
        """تحديث قائمة النسخ الاحتياطية"""
        # مسح القائمة الحالية
        for item in self.backups_tree.get_children():
            self.backups_tree.delete(item)
        
        # تحميل النسخ الاحتياطية
        backups = self.backup_system.get_available_backups()
        
        for backup in backups:
            # تنسيق التاريخ
            created_at = datetime.fromisoformat(backup['created_at'])
            date_str = created_at.strftime('%Y-%m-%d %H:%M')
            
            # تحديد نوع النسخة
            backup_type = "تلقائي" if backup['backup_name'].startswith('auto_') else "يدوي"
            
            # إضافة إلى الجدول
            self.backups_tree.insert('', 'end', values=(
                backup['backup_name'],
                date_str,
                backup['file_size_formatted'],
                backup['files_count'],
                backup_type
            ), tags=[backup['file_path']])
    
    def create_quick_backup(self):
        """إنشاء نسخة احتياطية سريعة"""
        try:
            backup_path, backup_info = self.backup_system.create_backup()
            
            if backup_path:
                messagebox.showinfo(language_manager.get_text("success"), 
                                  f"تم إنشاء النسخة الاحتياطية بنجاح!\n{backup_path}")
                self.refresh_backups()
            else:
                messagebox.showerror(language_manager.get_text("error"), 
                                   "فشل في إنشاء النسخة الاحتياطية")
        except Exception as e:
            messagebox.showerror(language_manager.get_text("error"), 
                               f"خطأ في إنشاء النسخة الاحتياطية:\n{str(e)}")
    
    def show_create_backup_dialog(self):
        """عرض نافذة إنشاء نسخة احتياطية مخصصة"""
        # يمكن إضافة نافذة مخصصة هنا
        self.create_quick_backup()
    
    def restore_selected_backup(self):
        """استعادة النسخة الاحتياطية المحددة"""
        try:
            selected_item = self.backups_tree.selection()[0]
            tags = self.backups_tree.item(selected_item, 'tags')
            
            if tags:
                backup_path = tags[0]
                
                # تأكيد الاستعادة
                if messagebox.askyesno(language_manager.get_text("warning"), 
                                     "هل تريد استعادة هذه النسخة الاحتياطية؟\nسيتم إنشاء نسخة احتياطية من الحالة الحالية أولاً."):
                    
                    success, message = self.backup_system.restore_backup(backup_path)
                    
                    if success:
                        messagebox.showinfo(language_manager.get_text("success"), message)
                    else:
                        messagebox.showerror(language_manager.get_text("error"), message)
                        
        except IndexError:
            messagebox.showwarning(language_manager.get_text("warning"), 
                                 "يرجى تحديد نسخة احتياطية للاستعادة")
    
    def delete_selected_backup(self):
        """حذف النسخة الاحتياطية المحددة"""
        try:
            selected_item = self.backups_tree.selection()[0]
            tags = self.backups_tree.item(selected_item, 'tags')
            
            if tags:
                backup_path = tags[0]
                
                if messagebox.askyesno(language_manager.get_text("warning"), 
                                     "هل تريد حذف هذه النسخة الاحتياطية نهائياً؟"):
                    
                    if self.backup_system.delete_backup(backup_path):
                        messagebox.showinfo(language_manager.get_text("success"), 
                                          "تم حذف النسخة الاحتياطية")
                        self.refresh_backups()
                    else:
                        messagebox.showerror(language_manager.get_text("error"), 
                                           "فشل في حذف النسخة الاحتياطية")
                        
        except IndexError:
            messagebox.showwarning(language_manager.get_text("warning"), 
                                 "يرجى تحديد نسخة احتياطية للحذف")
    
    def export_selected_backup(self):
        """تصدير النسخة الاحتياطية المحددة"""
        try:
            selected_item = self.backups_tree.selection()[0]
            tags = self.backups_tree.item(selected_item, 'tags')
            
            if tags:
                backup_path = tags[0]
                
                # اختيار مجلد التصدير
                destination = filedialog.askdirectory(title="اختر مجلد التصدير")
                
                if destination:
                    success, message = self.backup_system.export_backup_to_location(backup_path, destination)
                    
                    if success:
                        messagebox.showinfo(language_manager.get_text("success"), message)
                    else:
                        messagebox.showerror(language_manager.get_text("error"), message)
                        
        except IndexError:
            messagebox.showwarning(language_manager.get_text("warning"), 
                                 "يرجى تحديد نسخة احتياطية للتصدير")
    
    def import_backup(self):
        """استيراد نسخة احتياطية"""
        backup_file = filedialog.askopenfilename(
            title="اختر ملف النسخة الاحتياطية",
            filetypes=[("Backup files", "*.zip"), ("All files", "*.*")]
        )
        
        if backup_file:
            success, message = self.backup_system.import_backup_from_location(backup_file)
            
            if success:
                messagebox.showinfo(language_manager.get_text("success"), message)
                self.refresh_backups()
            else:
                messagebox.showerror(language_manager.get_text("error"), message)
    
    def update_auto_backup_setting(self):
        """تحديث إعداد النسخ التلقائي"""
        self.backup_system.auto_backup_enabled = self.auto_backup_var.get()
    
    def save_backup_settings(self):
        """حفظ إعدادات النسخ الاحتياطي"""
        try:
            self.backup_system.auto_backup_enabled = self.auto_backup_var.get()
            self.backup_system.backup_interval_hours = self.interval_var.get()
            self.backup_system.max_backups = self.max_backups_var.get()
            self.backup_system.compression_enabled = self.compression_var.get()
            
            messagebox.showinfo(language_manager.get_text("success"), 
                              "تم حفظ الإعدادات بنجاح!")
        except Exception as e:
            messagebox.showerror(language_manager.get_text("error"), 
                               f"خطأ في حفظ الإعدادات:\n{str(e)}")
    
    def update_statistics(self):
        """تحديث الإحصائيات"""
        try:
            stats = self.backup_system.get_backup_statistics()
            
            stats_text = f"""
إحصائيات النسخ الاحتياطية:
{'=' * 50}

إجمالي النسخ الاحتياطية: {stats.get('total_backups', 0)}
الحجم الإجمالي: {stats.get('total_size_formatted', '0 B')}

إعدادات النسخ التلقائي:
- النسخ التلقائي: {'مفعل' if stats.get('auto_backup_enabled', False) else 'معطل'}
- فترة النسخ: كل {stats.get('backup_interval_hours', 24)} ساعة
- الحد الأقصى للنسخ: {stats.get('max_backups', 10)}

أحدث نسخة احتياطية:
"""
            
            newest = stats.get('newest_backup')
            if newest:
                created_at = datetime.fromisoformat(newest['created_at'])
                stats_text += f"- الاسم: {newest['backup_name']}\n"
                stats_text += f"- التاريخ: {created_at.strftime('%Y-%m-%d %H:%M:%S')}\n"
                stats_text += f"- الحجم: {newest['file_size_formatted']}\n"
                stats_text += f"- عدد الملفات: {newest['files_count']}\n"
            else:
                stats_text += "- لا توجد نسخ احتياطية\n"
            
            stats_text += "\nأقدم نسخة احتياطية:\n"
            
            oldest = stats.get('oldest_backup')
            if oldest:
                created_at = datetime.fromisoformat(oldest['created_at'])
                stats_text += f"- الاسم: {oldest['backup_name']}\n"
                stats_text += f"- التاريخ: {created_at.strftime('%Y-%m-%d %H:%M:%S')}\n"
                stats_text += f"- الحجم: {oldest['file_size_formatted']}\n"
                stats_text += f"- عدد الملفات: {oldest['files_count']}\n"
            else:
                stats_text += "- لا توجد نسخ احتياطية\n"
            
            self.stats_text.delete('1.0', tk.END)
            self.stats_text.insert('1.0', stats_text)
            
        except Exception as e:
            error_text = f"خطأ في تحميل الإحصائيات:\n{str(e)}"
            self.stats_text.delete('1.0', tk.END)
            self.stats_text.insert('1.0', error_text)
    
    def on_backup_double_click(self, event):
        """عند النقر المزدوج على نسخة احتياطية"""
        self.show_backup_details()
    
    def show_backup_context_menu(self, event):
        """عرض قائمة السياق للنسخة الاحتياطية"""
        # يمكن إضافة قائمة سياق هنا
        pass
    
    def show_backup_details(self):
        """عرض تفاصيل النسخة الاحتياطية"""
        try:
            selected_item = self.backups_tree.selection()[0]
            tags = self.backups_tree.item(selected_item, 'tags')
            
            if tags:
                backup_path = tags[0]
                
                # التحقق من سلامة النسخة الاحتياطية
                is_valid, message = self.backup_system.verify_backup(backup_path)
                
                details = f"مسار الملف: {backup_path}\n"
                details += f"حالة النسخة: {'سليمة' if is_valid else 'تالفة'}\n"
                details += f"التفاصيل: {message}\n"
                
                messagebox.showinfo("تفاصيل النسخة الاحتياطية", details)
                
        except IndexError:
            messagebox.showwarning(language_manager.get_text("warning"), 
                                 "يرجى تحديد نسخة احتياطية")
    
    def center_window(self):
        """توسيط النافذة"""
        self.dialog.update_idletasks()
        x = (self.dialog.winfo_screenwidth() // 2) - (self.dialog.winfo_width() // 2)
        y = (self.dialog.winfo_screenheight() // 2) - (self.dialog.winfo_height() // 2)
        self.dialog.geometry(f"+{x}+{y}")
    
    def close_dialog(self):
        """إغلاق النافذة"""
        self.dialog.destroy()
    
    def show(self):
        """عرض النافذة"""
        self.dialog.wait_window()
