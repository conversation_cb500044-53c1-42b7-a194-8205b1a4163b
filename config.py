"""
ملف الإعدادات لبرنامج مدير التحميل
Configuration file for Download Manager
"""

import os
from pathlib import Path

class Config:
    """فئة الإعدادات الأساسية"""
    
    # إعدادات التحميل الافتراضية
    DEFAULT_DOWNLOAD_FOLDER = os.path.expanduser("~/Downloads")
    DEFAULT_CHUNK_SIZE = 8192  # 8KB
    DEFAULT_TIMEOUT = 30  # ثانية
    MAX_RETRIES = 3
    
    # إعدادات الواجهة
    WINDOW_WIDTH = 800
    WINDOW_HEIGHT = 600
    WINDOW_TITLE = "مدير التحميل - Download Manager"
    
    # إعدادات السجل
    HISTORY_FILE = "download_history.json"
    MAX_HISTORY_ITEMS = 100
    
    # إعدادات الألوان والخطوط
    COLORS = {
        'primary': '#2196F3',
        'success': '#4CAF50',
        'warning': '#FF9800',
        'error': '#F44336',
        'background': '#f0f0f0',
        'text': '#333333'
    }
    
    FONTS = {
        'title': ('Arial', 16, 'bold'),
        'normal': ('Arial', 10),
        'small': ('Arial', 8)
    }
    
    # إعدادات الشبكة
    HEADERS = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
    }
    
    # أنواع الملفات المدعومة
    SUPPORTED_EXTENSIONS = [
        '.zip', '.rar', '.7z', '.tar', '.gz',  # ملفات مضغوطة
        '.exe', '.msi', '.deb', '.rpm',        # ملفات تثبيت
        '.pdf', '.doc', '.docx', '.txt',       # مستندات
        '.mp4', '.avi', '.mkv', '.mov',        # فيديو
        '.mp3', '.wav', '.flac', '.aac',       # صوت
        '.jpg', '.png', '.gif', '.bmp',        # صور
        '.iso', '.img',                        # ملفات نظام
    ]
    
    # رسائل الحالة
    STATUS_MESSAGES = {
        'ready': 'جاهز للتحميل',
        'downloading': 'جاري التحميل...',
        'paused': 'متوقف مؤقتاً',
        'completed': 'تم التحميل بنجاح!',
        'error': 'حدث خطأ في التحميل',
        'stopped': 'تم إيقاف التحميل',
        'connecting': 'جاري الاتصال...',
        'validating': 'جاري التحقق من الرابط...'
    }
    
    # رسائل الأخطاء
    ERROR_MESSAGES = {
        'invalid_url': 'رابط غير صحيح',
        'network_error': 'خطأ في الشبكة',
        'file_not_found': 'الملف غير موجود',
        'permission_denied': 'ليس لديك صلاحية للكتابة في هذا المجلد',
        'disk_full': 'مساحة القرص ممتلئة',
        'connection_timeout': 'انتهت مهلة الاتصال',
        'server_error': 'خطأ في الخادم'
    }
    
    @classmethod
    def get_download_folder(cls):
        """الحصول على مجلد التحميل الافتراضي"""
        folder = Path(cls.DEFAULT_DOWNLOAD_FOLDER)
        folder.mkdir(parents=True, exist_ok=True)
        return str(folder)
    
    @classmethod
    def is_supported_file(cls, filename):
        """التحقق من دعم نوع الملف"""
        if not filename:
            return True  # السماح بالملفات بدون امتداد
        
        extension = Path(filename).suffix.lower()
        return extension in cls.SUPPORTED_EXTENSIONS or len(cls.SUPPORTED_EXTENSIONS) == 0
    
    @classmethod
    def get_safe_filename(cls, filename):
        """تنظيف اسم الملف من الأحرف غير المسموحة"""
        if not filename:
            return "downloaded_file"
        
        # إزالة الأحرف غير المسموحة
        invalid_chars = '<>:"/\\|?*'
        for char in invalid_chars:
            filename = filename.replace(char, '_')
        
        # إزالة المسافات الزائدة
        filename = filename.strip()
        
        # التأكد من عدم تجاوز الطول المسموح
        if len(filename) > 255:
            name, ext = os.path.splitext(filename)
            filename = name[:255-len(ext)] + ext
        
        return filename or "downloaded_file"

# إعدادات إضافية للمطورين
class DevConfig(Config):
    """إعدادات التطوير"""
    DEBUG = True
    LOG_LEVEL = 'DEBUG'
    ENABLE_LOGGING = True

# إعدادات الإنتاج
class ProdConfig(Config):
    """إعدادات الإنتاج"""
    DEBUG = False
    LOG_LEVEL = 'INFO'
    ENABLE_LOGGING = False

# تحديد الإعدادات المستخدمة
current_config = Config
