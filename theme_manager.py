"""
مدير الثيمات والألوان - Theme Manager
إدارة الألوان والثيمات مع دعم البرتقالي الداكن
"""

class ThemeManager:
    """مدير الثيمات والألوان"""
    
    def __init__(self):
        self.current_theme = "dark_orange"
        self.themes = self.get_available_themes()
    
    def get_available_themes(self):
        """الحصول على الثيمات المتاحة"""
        return {
            "dark_orange": {
                # ألوان أساسية
                'bg_main': '#1a1a1a',           # خلفية رئيسية داكنة
                'bg_toolbar': '#2d2d2d',        # شريط الأدوات
                'bg_panel': '#242424',          # لوحات
                'bg_sidebar': '#1f1f1f',        # الشريط الجانبي
                'bg_input': '#333333',          # حقول الإدخال
                
                # ألوان البرتقالي الداكن
                'primary': '#ff6b35',           # برتقالي داكن رئيسي
                'primary_hover': '#e55a2b',     # برتقالي عند التمرير
                'primary_light': '#ff8c5a',     # برتقالي فاتح
                'primary_dark': '#cc4a1a',      # برتقالي أغمق
                
                # ألوان ثانوية
                'secondary': '#ff9500',         # برتقالي ذهبي
                'secondary_hover': '#e6860a',   # برتقالي ذهبي داكن
                
                # ألوان الحالة
                'success': '#28a745',           # أخضر للنجاح
                'success_light': '#d4edda',     # أخضر فاتح
                'warning': '#ffc107',           # أصفر للتحذير
                'warning_light': '#fff3cd',     # أصفر فاتح
                'error': '#dc3545',             # أحمر للخطأ
                'error_light': '#f8d7da',       # أحمر فاتح
                'info': '#17a2b8',              # أزرق للمعلومات
                'info_light': '#d1ecf1',        # أزرق فاتح
                
                # ألوان النص
                'text_primary': '#ffffff',      # نص أساسي أبيض
                'text_secondary': '#cccccc',    # نص ثانوي رمادي فاتح
                'text_muted': '#999999',        # نص خافت
                'text_inverse': '#000000',      # نص معكوس أسود
                
                # ألوان الحدود
                'border': '#444444',            # حدود عادية
                'border_light': '#555555',      # حدود فاتحة
                'border_focus': '#ff6b35',      # حدود عند التركيز
                
                # ألوان خاصة
                'accent': '#ff6b35',            # لون مميز
                'accent_hover': '#e55a2b',      # لون مميز عند التمرير
                'highlight': '#ff8c5a',         # لون التمييز
                'shadow': '#000000',            # ظل
                
                # ألوان حالات التحميل
                'download_pending': '#ffc107',      # انتظار - أصفر
                'download_active': '#ff6b35',       # نشط - برتقالي
                'download_paused': '#6c757d',       # متوقف - رمادي
                'download_completed': '#28a745',    # مكتمل - أخضر
                'download_failed': '#dc3545',       # فشل - أحمر
                'download_cancelled': '#6c757d',    # ملغي - رمادي داكن
            },
            
            "light_orange": {
                # ثيم فاتح مع البرتقالي
                'bg_main': '#f8f9fa',
                'bg_toolbar': '#e9ecef',
                'bg_panel': '#ffffff',
                'bg_sidebar': '#f1f3f4',
                'bg_input': '#ffffff',
                
                'primary': '#ff6b35',
                'primary_hover': '#e55a2b',
                'primary_light': '#ff8c5a',
                'primary_dark': '#cc4a1a',
                
                'secondary': '#ff9500',
                'secondary_hover': '#e6860a',
                
                'success': '#28a745',
                'success_light': '#d4edda',
                'warning': '#ffc107',
                'warning_light': '#fff3cd',
                'error': '#dc3545',
                'error_light': '#f8d7da',
                'info': '#17a2b8',
                'info_light': '#d1ecf1',
                
                'text_primary': '#212529',
                'text_secondary': '#6c757d',
                'text_muted': '#adb5bd',
                'text_inverse': '#ffffff',
                
                'border': '#dee2e6',
                'border_light': '#e9ecef',
                'border_focus': '#ff6b35',
                
                'accent': '#ff6b35',
                'accent_hover': '#e55a2b',
                'highlight': '#ff8c5a',
                'shadow': '#00000020',
                
                'download_pending': '#ffc107',
                'download_active': '#ff6b35',
                'download_paused': '#6c757d',
                'download_completed': '#28a745',
                'download_failed': '#dc3545',
                'download_cancelled': '#6c757d',
            },
            
            "classic_idm": {
                # ثيم كلاسيكي مشابه لـ IDM الأصلي
                'bg_main': '#f0f0f0',
                'bg_toolbar': '#e8e8e8',
                'bg_panel': '#ffffff',
                'bg_sidebar': '#f5f5f5',
                'bg_input': '#ffffff',
                
                'primary': '#0078d4',
                'primary_hover': '#106ebe',
                'primary_light': '#40a9ff',
                'primary_dark': '#005a9e',
                
                'secondary': '#5c2d91',
                'secondary_hover': '#4c1a7b',
                
                'success': '#107c10',
                'success_light': '#dff6dd',
                'warning': '#ff8c00',
                'warning_light': '#fff4ce',
                'error': '#d13438',
                'error_light': '#fde7e9',
                'info': '#0078d4',
                'info_light': '#deecf9',
                
                'text_primary': '#323130',
                'text_secondary': '#605e5c',
                'text_muted': '#a19f9d',
                'text_inverse': '#ffffff',
                
                'border': '#d0d0d0',
                'border_light': '#e1dfdd',
                'border_focus': '#0078d4',
                
                'accent': '#0078d4',
                'accent_hover': '#106ebe',
                'highlight': '#40a9ff',
                'shadow': '#00000015',
                
                'download_pending': '#ff8c00',
                'download_active': '#0078d4',
                'download_paused': '#605e5c',
                'download_completed': '#107c10',
                'download_failed': '#d13438',
                'download_cancelled': '#a19f9d',
            }
        }
    
    def get_current_theme(self):
        """الحصول على الثيم الحالي"""
        return self.themes.get(self.current_theme, self.themes["dark_orange"])
    
    def set_theme(self, theme_name):
        """تعيين ثيم جديد"""
        if theme_name in self.themes:
            self.current_theme = theme_name
            return True
        return False
    
    def get_theme_names(self):
        """الحصول على أسماء الثيمات المتاحة"""
        return list(self.themes.keys())
    
    def get_download_status_color(self, status):
        """الحصول على لون حالة التحميل"""
        theme = self.get_current_theme()
        status_lower = status.lower()
        
        if 'pending' in status_lower or 'انتظار' in status_lower:
            return theme['download_pending']
        elif 'downloading' in status_lower or 'جاري' in status_lower:
            return theme['download_active']
        elif 'paused' in status_lower or 'متوقف' in status_lower:
            return theme['download_paused']
        elif 'completed' in status_lower or 'مكتمل' in status_lower:
            return theme['download_completed']
        elif 'failed' in status_lower or 'فشل' in status_lower:
            return theme['download_failed']
        elif 'cancelled' in status_lower or 'ملغي' in status_lower:
            return theme['download_cancelled']
        else:
            return theme['text_secondary']
    
    def apply_theme_to_widget(self, widget, widget_type="default"):
        """تطبيق الثيم على عنصر واجهة"""
        theme = self.get_current_theme()
        
        try:
            if widget_type == "main_window":
                widget.configure(bg=theme['bg_main'])
            elif widget_type == "toolbar":
                widget.configure(bg=theme['bg_toolbar'])
            elif widget_type == "panel":
                widget.configure(bg=theme['bg_panel'])
            elif widget_type == "sidebar":
                widget.configure(bg=theme['bg_sidebar'])
            elif widget_type == "button_primary":
                widget.configure(
                    bg=theme['primary'],
                    fg=theme['text_inverse'],
                    activebackground=theme['primary_hover'],
                    relief='flat',
                    bd=0
                )
            elif widget_type == "button_secondary":
                widget.configure(
                    bg=theme['secondary'],
                    fg=theme['text_inverse'],
                    activebackground=theme['secondary_hover'],
                    relief='flat',
                    bd=0
                )
            elif widget_type == "button_success":
                widget.configure(
                    bg=theme['success'],
                    fg=theme['text_inverse'],
                    relief='flat',
                    bd=0
                )
            elif widget_type == "button_warning":
                widget.configure(
                    bg=theme['warning'],
                    fg=theme['text_inverse'],
                    relief='flat',
                    bd=0
                )
            elif widget_type == "button_error":
                widget.configure(
                    bg=theme['error'],
                    fg=theme['text_inverse'],
                    relief='flat',
                    bd=0
                )
            elif widget_type == "entry":
                widget.configure(
                    bg=theme['bg_input'],
                    fg=theme['text_primary'],
                    insertbackground=theme['text_primary'],
                    selectbackground=theme['primary'],
                    selectforeground=theme['text_inverse'],
                    relief='solid',
                    bd=1,
                    highlightthickness=1,
                    highlightcolor=theme['border_focus']
                )
            elif widget_type == "label":
                widget.configure(
                    bg=theme['bg_panel'],
                    fg=theme['text_primary']
                )
            elif widget_type == "label_secondary":
                widget.configure(
                    bg=theme['bg_panel'],
                    fg=theme['text_secondary']
                )
            elif widget_type == "frame":
                widget.configure(bg=theme['bg_panel'])
            elif widget_type == "labelframe":
                widget.configure(
                    bg=theme['bg_panel'],
                    fg=theme['text_primary']
                )
        except Exception as e:
            print(f"خطأ في تطبيق الثيم: {e}")
    
    def get_gradient_colors(self, start_color, end_color, steps=10):
        """إنشاء تدرج لوني"""
        # تحويل الألوان من hex إلى RGB
        start_rgb = tuple(int(start_color[i:i+2], 16) for i in (1, 3, 5))
        end_rgb = tuple(int(end_color[i:i+2], 16) for i in (1, 3, 5))
        
        # حساب التدرج
        gradient = []
        for i in range(steps):
            ratio = i / (steps - 1)
            r = int(start_rgb[0] + (end_rgb[0] - start_rgb[0]) * ratio)
            g = int(start_rgb[1] + (end_rgb[1] - start_rgb[1]) * ratio)
            b = int(start_rgb[2] + (end_rgb[2] - start_rgb[2]) * ratio)
            gradient.append(f"#{r:02x}{g:02x}{b:02x}")
        
        return gradient
    
    def save_theme_preference(self, filename="theme_settings.json"):
        """حفظ تفضيلات الثيم"""
        try:
            import json
            from datetime import datetime
            settings = {
                'current_theme': self.current_theme,
                'custom_colors': {},  # يمكن إضافة ألوان مخصصة لاحقاً
                'saved_at': str(datetime.now())
            }
            
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(settings, f, ensure_ascii=False, indent=2)
            
            return True
        except Exception as e:
            print(f"خطأ في حفظ تفضيلات الثيم: {e}")
            return False
    
    def load_theme_preference(self, filename="theme_settings.json"):
        """تحميل تفضيلات الثيم"""
        try:
            import json
            import os
            
            if os.path.exists(filename):
                with open(filename, 'r', encoding='utf-8') as f:
                    settings = json.load(f)
                
                theme_name = settings.get('current_theme', 'dark_orange')
                if theme_name in self.themes:
                    self.current_theme = theme_name
                    return True
            
            return False
        except Exception as e:
            print(f"خطأ في تحميل تفضيلات الثيم: {e}")
            return False

# إنشاء مثيل عام للثيم
theme_manager = ThemeManager()
