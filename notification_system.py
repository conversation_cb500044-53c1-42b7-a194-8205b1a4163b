"""
نظام الإشعارات المتقدم - Advanced Notification System
إشعارات نظام التشغيل مع أصوات وتأثيرات بصرية
"""

import tkinter as tk
from tkinter import messagebox
import threading
import time
import os
import sys
from datetime import datetime
from language_manager import language_manager
from theme_manager import theme_manager

try:
    # محاولة استيراد مكتبة الإشعارات
    import plyer
    PLYER_AVAILABLE = True
except ImportError:
    PLYER_AVAILABLE = False

try:
    # محاولة استيراد مكتبة الأصوات
    import pygame
    PYGAME_AVAILABLE = True
except ImportError:
    PYGAME_AVAILABLE = False

class NotificationSystem:
    """نظام الإشعارات المتقدم"""
    
    def __init__(self):
        self.enabled = True
        self.sound_enabled = True
        self.system_notifications = True
        self.popup_notifications = True
        self.notification_history = []
        self.max_history = 100
        
        # إعداد الأصوات
        self.setup_sounds()
        
        # إعداد الإشعارات المخصصة
        self.custom_notifications = []
        
    def setup_sounds(self):
        """إعداد نظام الأصوات"""
        if PYGAME_AVAILABLE:
            try:
                pygame.mixer.init()
                self.sounds = {
                    'success': self.create_success_sound(),
                    'error': self.create_error_sound(),
                    'warning': self.create_warning_sound(),
                    'info': self.create_info_sound()
                }
            except:
                self.sound_enabled = False
                self.sounds = {}
        else:
            self.sound_enabled = False
            self.sounds = {}
    
    def create_success_sound(self):
        """إنشاء صوت النجاح"""
        # يمكن إضافة ملف صوتي هنا أو إنشاء نغمة برمجياً
        return None
    
    def create_error_sound(self):
        """إنشاء صوت الخطأ"""
        return None
    
    def create_warning_sound(self):
        """إنشاء صوت التحذير"""
        return None
    
    def create_info_sound(self):
        """إنشاء صوت المعلومات"""
        return None
    
    def show_notification(self, title, message, notification_type="info", duration=5000, action_callback=None):
        """عرض إشعار شامل"""
        if not self.enabled:
            return
        
        # إضافة إلى السجل
        self.add_to_history(title, message, notification_type)
        
        # تشغيل الصوت
        if self.sound_enabled:
            self.play_sound(notification_type)
        
        # إشعار النظام
        if self.system_notifications and PLYER_AVAILABLE:
            self.show_system_notification(title, message, notification_type)
        
        # إشعار منبثق مخصص
        if self.popup_notifications:
            self.show_custom_popup(title, message, notification_type, duration, action_callback)
    
    def show_system_notification(self, title, message, notification_type):
        """عرض إشعار نظام التشغيل"""
        try:
            # تحديد الأيقونة حسب النوع
            icon_path = self.get_icon_path(notification_type)
            
            plyer.notification.notify(
                title=title,
                message=message,
                app_name=language_manager.get_text("app_title"),
                app_icon=icon_path,
                timeout=5
            )
        except Exception as e:
            print(f"خطأ في إشعار النظام: {e}")
    
    def show_custom_popup(self, title, message, notification_type, duration, action_callback):
        """عرض إشعار منبثق مخصص"""
        def show_popup():
            popup = CustomNotificationPopup(title, message, notification_type, duration, action_callback)
            popup.show()
        
        # تشغيل في خيط منفصل لتجنب التجميد
        threading.Thread(target=show_popup, daemon=True).start()
    
    def play_sound(self, notification_type):
        """تشغيل الصوت"""
        if not self.sound_enabled or notification_type not in self.sounds:
            return
        
        try:
            sound = self.sounds[notification_type]
            if sound:
                sound.play()
        except Exception as e:
            print(f"خطأ في تشغيل الصوت: {e}")
    
    def get_icon_path(self, notification_type):
        """الحصول على مسار الأيقونة"""
        icons = {
            'success': '✅',
            'error': '❌',
            'warning': '⚠️',
            'info': 'ℹ️'
        }
        return icons.get(notification_type, 'ℹ️')
    
    def add_to_history(self, title, message, notification_type):
        """إضافة إلى سجل الإشعارات"""
        notification = {
            'timestamp': datetime.now(),
            'title': title,
            'message': message,
            'type': notification_type,
            'read': False
        }
        
        self.notification_history.insert(0, notification)
        
        # الحفاظ على الحد الأقصى للسجل
        if len(self.notification_history) > self.max_history:
            self.notification_history = self.notification_history[:self.max_history]
    
    def get_unread_count(self):
        """الحصول على عدد الإشعارات غير المقروءة"""
        return len([n for n in self.notification_history if not n['read']])
    
    def mark_as_read(self, index):
        """تمييز إشعار كمقروء"""
        if 0 <= index < len(self.notification_history):
            self.notification_history[index]['read'] = True
    
    def mark_all_as_read(self):
        """تمييز جميع الإشعارات كمقروءة"""
        for notification in self.notification_history:
            notification['read'] = True
    
    def clear_history(self):
        """مسح سجل الإشعارات"""
        self.notification_history.clear()
    
    def download_completed_notification(self, filename, file_size, download_time):
        """إشعار اكتمال التحميل"""
        title = language_manager.get_text("download_completed")
        
        # تنسيق الرسالة
        size_formatted = self.format_size(file_size)
        time_formatted = self.format_time(download_time)
        
        message = f"{language_manager.get_text('filename')}: {filename}\n"
        message += f"{language_manager.get_text('file_size')}: {size_formatted}\n"
        message += f"{language_manager.get_text('time_taken', 'الوقت المستغرق')}: {time_formatted}"
        
        self.show_notification(title, message, "success", duration=7000)
    
    def download_failed_notification(self, filename, error_message):
        """إشعار فشل التحميل"""
        title = language_manager.get_text("download_failed")
        message = f"{language_manager.get_text('filename')}: {filename}\n"
        message += f"{language_manager.get_text('error')}: {error_message}"
        
        self.show_notification(title, message, "error", duration=10000)
    
    def download_paused_notification(self, filename):
        """إشعار إيقاف التحميل"""
        title = language_manager.get_text("download_paused")
        message = f"{language_manager.get_text('filename')}: {filename}"
        
        self.show_notification(title, message, "warning", duration=3000)
    
    def download_resumed_notification(self, filename):
        """إشعار استئناف التحميل"""
        title = language_manager.get_text("download_resumed", "تم استئناف التحميل")
        message = f"{language_manager.get_text('filename')}: {filename}"
        
        self.show_notification(title, message, "info", duration=3000)
    
    def low_disk_space_notification(self, available_space):
        """إشعار انخفاض مساحة القرص"""
        title = language_manager.get_text("low_disk_space", "مساحة قرص منخفضة")
        space_formatted = self.format_size(available_space)
        message = f"{language_manager.get_text('available_space', 'المساحة المتاحة')}: {space_formatted}"
        
        self.show_notification(title, message, "warning", duration=8000)
    
    def network_error_notification(self, error_details):
        """إشعار خطأ الشبكة"""
        title = language_manager.get_text("network_error")
        message = f"{language_manager.get_text('error_details', 'تفاصيل الخطأ')}: {error_details}"
        
        self.show_notification(title, message, "error", duration=6000)
    
    def schedule_notification(self, download_name, schedule_time):
        """إشعار التحميل المجدول"""
        title = language_manager.get_text("scheduled_download", "تحميل مجدول")
        message = f"{language_manager.get_text('download')}: {download_name}\n"
        message += f"{language_manager.get_text('scheduled_time', 'الوقت المجدول')}: {schedule_time}"
        
        self.show_notification(title, message, "info", duration=5000)
    
    def format_size(self, size_bytes):
        """تنسيق حجم الملف"""
        if size_bytes == 0:
            return "0 B"
        
        size_names = ["B", "KB", "MB", "GB", "TB"]
        i = 0
        while size_bytes >= 1024 and i < len(size_names) - 1:
            size_bytes /= 1024.0
            i += 1
        
        return f"{size_bytes:.2f} {size_names[i]}"
    
    def format_time(self, seconds):
        """تنسيق الوقت"""
        if seconds < 60:
            return f"{int(seconds)} {language_manager.get_text('seconds', 'ثانية')}"
        elif seconds < 3600:
            return f"{int(seconds / 60)} {language_manager.get_text('minutes', 'دقيقة')}"
        else:
            hours = int(seconds / 3600)
            minutes = int((seconds % 3600) / 60)
            return f"{hours}:{minutes:02d} {language_manager.get_text('hours', 'ساعة')}"

class CustomNotificationPopup:
    """نافذة إشعار منبثقة مخصصة"""
    
    def __init__(self, title, message, notification_type, duration, action_callback=None):
        self.title = title
        self.message = message
        self.notification_type = notification_type
        self.duration = duration
        self.action_callback = action_callback
        
        # الحصول على الألوان من الثيم
        self.colors = theme_manager.get_current_theme()
        
    def show(self):
        """عرض النافذة المنبثقة"""
        # إنشاء نافذة منبثقة
        self.popup = tk.Toplevel()
        self.popup.title("")
        self.popup.geometry("350x120")
        self.popup.resizable(False, False)
        self.popup.overrideredirect(True)  # إزالة شريط العنوان
        
        # تطبيق الثيم
        bg_color = self.get_notification_color()
        self.popup.configure(bg=bg_color)
        
        # إنشاء المحتوى
        self.create_content()
        
        # وضع النافذة في الزاوية
        self.position_popup()
        
        # إغلاق تلقائي
        if self.duration > 0:
            self.popup.after(self.duration, self.close_popup)
        
        # تأثير الظهور
        self.animate_show()
    
    def create_content(self):
        """إنشاء محتوى النافذة"""
        # إطار رئيسي
        main_frame = tk.Frame(self.popup, bg=self.popup['bg'], padx=15, pady=10)
        main_frame.pack(fill='both', expand=True)
        
        # أيقونة ونوع الإشعار
        icon_frame = tk.Frame(main_frame, bg=self.popup['bg'])
        icon_frame.pack(side='left', padx=(0, 10))
        
        icon = self.get_notification_icon()
        icon_label = tk.Label(icon_frame, text=icon, font=('Segoe UI', 16), 
                             bg=self.popup['bg'], fg=self.colors['text_primary'])
        icon_label.pack()
        
        # النص
        text_frame = tk.Frame(main_frame, bg=self.popup['bg'])
        text_frame.pack(side='left', fill='both', expand=True)
        
        # العنوان
        title_label = tk.Label(text_frame, text=self.title, 
                              font=('Segoe UI', 10, 'bold'),
                              bg=self.popup['bg'], fg=self.colors['text_primary'],
                              anchor='w')
        title_label.pack(fill='x')
        
        # الرسالة
        message_label = tk.Label(text_frame, text=self.message, 
                                font=('Segoe UI', 9),
                                bg=self.popup['bg'], fg=self.colors['text_secondary'],
                                anchor='w', justify='left', wraplength=250)
        message_label.pack(fill='x', pady=(5, 0))
        
        # زر الإجراء (إذا كان متوفراً)
        if self.action_callback:
            action_btn = tk.Button(text_frame, text=language_manager.get_text("view", "عرض"),
                                  font=('Segoe UI', 8),
                                  bg=self.colors['primary'], fg=self.colors['text_inverse'],
                                  relief='flat', padx=10, pady=2,
                                  command=self.on_action_click)
            action_btn.pack(anchor='e', pady=(5, 0))
        
        # زر الإغلاق
        close_btn = tk.Label(main_frame, text="✕", font=('Segoe UI', 12, 'bold'),
                            bg=self.popup['bg'], fg=self.colors['text_secondary'],
                            cursor='hand2')
        close_btn.pack(side='right', anchor='ne')
        close_btn.bind('<Button-1>', lambda e: self.close_popup())
    
    def get_notification_color(self):
        """الحصول على لون الإشعار"""
        color_map = {
            'success': self.colors.get('success_light', self.colors['success']),
            'error': self.colors.get('error_light', self.colors['error']),
            'warning': self.colors.get('warning_light', self.colors['warning']),
            'info': self.colors.get('info_light', self.colors['info'])
        }
        return color_map.get(self.notification_type, self.colors['bg_panel'])
    
    def get_notification_icon(self):
        """الحصول على أيقونة الإشعار"""
        icons = {
            'success': '✅',
            'error': '❌',
            'warning': '⚠️',
            'info': 'ℹ️'
        }
        return icons.get(self.notification_type, 'ℹ️')
    
    def position_popup(self):
        """وضع النافذة في الزاوية"""
        # الحصول على أبعاد الشاشة
        screen_width = self.popup.winfo_screenwidth()
        screen_height = self.popup.winfo_screenheight()
        
        # وضع في الزاوية اليمنى السفلى
        x = screen_width - 370
        y = screen_height - 150
        
        self.popup.geometry(f"+{x}+{y}")
    
    def animate_show(self):
        """تأثير الظهور"""
        # يمكن إضافة تأثيرات حركة هنا
        self.popup.attributes('-alpha', 0.0)
        self.popup.deiconify()
        
        # تأثير التلاشي
        for i in range(1, 11):
            self.popup.attributes('-alpha', i / 10)
            self.popup.update()
            time.sleep(0.02)
    
    def animate_hide(self):
        """تأثير الاختفاء"""
        # تأثير التلاشي العكسي
        for i in range(10, 0, -1):
            self.popup.attributes('-alpha', i / 10)
            self.popup.update()
            time.sleep(0.02)
    
    def on_action_click(self):
        """عند النقر على زر الإجراء"""
        if self.action_callback:
            self.action_callback()
        self.close_popup()
    
    def close_popup(self):
        """إغلاق النافذة"""
        try:
            self.animate_hide()
            self.popup.destroy()
        except:
            pass

# إنشاء مثيل عام لنظام الإشعارات
notification_system = NotificationSystem()
