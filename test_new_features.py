#!/usr/bin/env python3
"""
اختبار المميزات الجديدة - New Features Test
اختبار الثيمات واللغات ونظام الحذف المتقدم
"""

import sys
import os
from datetime import datetime

def test_theme_manager():
    """اختبار مدير الثيمات"""
    print("🎨 اختبار مدير الثيمات...")
    
    try:
        from theme_manager import theme_manager
        
        # اختبار الحصول على الثيمات المتاحة
        themes = theme_manager.get_theme_names()
        print(f"✅ الثيمات المتاحة: {themes}")
        
        # اختبار تغيير الثيم
        if theme_manager.set_theme("dark_orange"):
            print("✅ تم تعيين ثيم البرتقالي الداكن")
        
        # اختبار الحصول على الثيم الحالي
        current_theme = theme_manager.get_current_theme()
        print(f"✅ الثيم الحالي يحتوي على {len(current_theme)} لون")
        
        # اختبار ألوان حالات التحميل
        status_color = theme_manager.get_download_status_color("downloading")
        print(f"✅ لون حالة التحميل: {status_color}")
        
        # اختبار حفظ وتحميل التفضيلات
        if theme_manager.save_theme_preference():
            print("✅ تم حفظ تفضيلات الثيم")
        
        if theme_manager.load_theme_preference():
            print("✅ تم تحميل تفضيلات الثيم")
        
        print("✅ مدير الثيمات يعمل بنجاح!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في مدير الثيمات: {e}")
        return False

def test_language_manager():
    """اختبار مدير اللغات"""
    print("\n🌐 اختبار مدير اللغات...")
    
    try:
        from language_manager import language_manager
        
        # اختبار الحصول على اللغات المتاحة
        languages = language_manager.get_language_names()
        print(f"✅ اللغات المتاحة: {languages}")
        
        # اختبار تغيير اللغة
        if language_manager.set_language("en"):
            print("✅ تم تعيين اللغة الإنجليزية")
        
        # اختبار الحصول على النصوص
        app_title = language_manager.get_text("app_title")
        print(f"✅ عنوان التطبيق: {app_title}")
        
        # اختبار اتجاه النص
        direction = language_manager.get_language_direction()
        print(f"✅ اتجاه النص: {direction}")
        
        # اختبار تنسيق الحجم
        formatted_size = language_manager.format_file_size(1024 * 1024)
        print(f"✅ تنسيق الحجم: {formatted_size}")
        
        # اختبار تنسيق السرعة
        formatted_speed = language_manager.format_speed(1024 * 1024)
        print(f"✅ تنسيق السرعة: {formatted_speed}")
        
        # اختبار تنسيق الوقت
        formatted_time = language_manager.format_time(3661)
        print(f"✅ تنسيق الوقت: {formatted_time}")
        
        # العودة للعربية
        language_manager.set_language("ar")
        
        # اختبار حفظ وتحميل التفضيلات
        if language_manager.save_language_preference():
            print("✅ تم حفظ تفضيلات اللغة")
        
        if language_manager.load_language_preference():
            print("✅ تم تحميل تفضيلات اللغة")
        
        print("✅ مدير اللغات يعمل بنجاح!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في مدير اللغات: {e}")
        return False

def test_delete_manager():
    """اختبار مدير الحذف"""
    print("\n🗑️ اختبار مدير الحذف...")
    
    try:
        from delete_manager import delete_manager
        
        # اختبار إنشاء مجلد سلة المحذوفات
        if delete_manager.ensure_trash_folder():
            print("✅ تم إنشاء مجلد سلة المحذوفات")
        
        # اختبار حذف تحميل وهمي
        test_download_info = {
            'filename': 'test_file.txt',
            'url': 'https://example.com/test.txt',
            'file_path': None
        }
        
        if delete_manager.delete_download('test_123', test_download_info, delete_file=False):
            print("✅ تم حذف التحميل الوهمي")
        
        # اختبار الحصول على العناصر المحذوفة
        deleted_items = delete_manager.get_deleted_items()
        print(f"✅ العناصر المحذوفة: {len(deleted_items)}")
        
        # اختبار البحث
        search_results = delete_manager.search_deleted_items('test')
        print(f"✅ نتائج البحث: {len(search_results)}")
        
        # اختبار الإحصائيات
        stats = delete_manager.get_statistics()
        print(f"✅ إحصائيات سلة المحذوفات: {stats.get('total_items', 0)} عنصر")
        
        # اختبار حجم سلة المحذوفات
        trash_size = delete_manager.get_trash_size()
        print(f"✅ حجم سلة المحذوفات: {delete_manager.format_size(trash_size)}")
        
        # اختبار استعادة العنصر
        if deleted_items:
            success, message = delete_manager.restore_download('test_123')
            if success:
                print("✅ تم استعادة العنصر الوهمي")
            else:
                print(f"⚠️ لم يتم استعادة العنصر: {message}")
        
        print("✅ مدير الحذف يعمل بنجاح!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في مدير الحذف: {e}")
        return False

def test_advanced_dialogs():
    """اختبار النوافذ المتقدمة الجديدة"""
    print("\n🖼️ اختبار النوافذ المتقدمة الجديدة...")
    
    try:
        # اختبار استيراد النوافذ المحدثة
        from advanced_dialogs import AdvancedDownloadDialog, AdvancedSettingsDialog
        print("✅ تم استيراد النوافذ المتقدمة المحدثة")
        
        # اختبار استيراد نافذة سلة المحذوفات
        from trash_dialog import TrashManagerDialog
        print("✅ تم استيراد نافذة مدير سلة المحذوفات")
        
        print("✅ النوافذ المتقدمة الجديدة جاهزة!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في النوافذ المتقدمة: {e}")
        return False

def test_integration():
    """اختبار التكامل بين المكونات"""
    print("\n🔗 اختبار التكامل...")
    
    try:
        from theme_manager import theme_manager
        from language_manager import language_manager
        from delete_manager import delete_manager
        
        # اختبار تغيير الثيم واللغة معاً
        original_theme = theme_manager.current_theme
        original_language = language_manager.current_language
        
        # تغيير للثيم الفاتح والإنجليزية
        theme_manager.set_theme("light_orange")
        language_manager.set_language("en")
        
        # اختبار الحصول على النصوص بالثيم الجديد
        app_title = language_manager.get_text("app_title")
        theme_colors = theme_manager.get_current_theme()
        
        print(f"✅ عنوان التطبيق بالإنجليزية: {app_title}")
        print(f"✅ الثيم الفاتح يحتوي على {len(theme_colors)} لون")
        
        # العودة للإعدادات الأصلية
        theme_manager.set_theme(original_theme)
        language_manager.set_language(original_language)
        
        print("✅ التكامل بين المكونات يعمل بنجاح!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في التكامل: {e}")
        return False

def test_main_app_integration():
    """اختبار التكامل مع التطبيق الرئيسي"""
    print("\n🏠 اختبار التكامل مع التطبيق الرئيسي...")
    
    try:
        # اختبار استيراد الملف الرئيسي المحدث
        import main
        print("✅ تم استيراد الملف الرئيسي المحدث")
        
        # التحقق من وجود المدراء الجدد
        if hasattr(main, 'theme_manager'):
            print("✅ مدير الثيمات متكامل")
        
        if hasattr(main, 'language_manager'):
            print("✅ مدير اللغات متكامل")
        
        if hasattr(main, 'delete_manager'):
            print("✅ مدير الحذف متكامل")
        
        print("✅ التكامل مع التطبيق الرئيسي ناجح!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في التكامل مع التطبيق الرئيسي: {e}")
        return False

def main():
    """الدالة الرئيسية للاختبار"""
    print("🧪 بدء اختبار المميزات الجديدة")
    print("=" * 60)
    
    tests = [
        ("مدير الثيمات", test_theme_manager),
        ("مدير اللغات", test_language_manager),
        ("مدير الحذف", test_delete_manager),
        ("النوافذ المتقدمة الجديدة", test_advanced_dialogs),
        ("التكامل بين المكونات", test_integration),
        ("التكامل مع التطبيق الرئيسي", test_main_app_integration)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"❌ خطأ في اختبار {test_name}: {e}")
    
    print("\n" + "=" * 60)
    print(f"📊 نتائج الاختبار: {passed}/{total} نجح")
    
    if passed == total:
        print("🎉 جميع المميزات الجديدة تعمل بنجاح!")
        print("\n🚀 المميزات الجديدة المتاحة:")
        print("   • ثيمات متعددة مع البرتقالي الداكن")
        print("   • دعم اللغة الإنجليزية والعربية")
        print("   • نظام حذف متقدم مع سلة محذوفات")
        print("   • قوائم سياق للحذف والاستعادة")
        print("   • واجهات محسنة مع دعم الثيمات")
        print("   • إعدادات متقدمة للمظهر واللغة")
    else:
        print("⚠️ بعض المميزات تحتاج إلى مراجعة.")
    
    print("\n🎨 لتجربة المميزات الجديدة:")
    print("   python main.py")
    print("   ثم استخدم أزرار الثيم واللغة وسلة المحذوفات")

if __name__ == "__main__":
    main()
