#!/usr/bin/env python3
"""
ملف التشغيل السريع لبرنامج مدير التحميل
Quick start file for Download Manager
"""

import sys
import os
import subprocess

def check_python_version():
    """التحقق من إصدار Python"""
    if sys.version_info < (3, 7):
        print("خطأ: يتطلب Python 3.7 أو أحدث")
        print("Error: Python 3.7 or newer is required")
        return False
    return True

def install_requirements():
    """تثبيت المتطلبات"""
    try:
        print("تحقق من المتطلبات...")
        print("Checking requirements...")
        
        subprocess.check_call([
            sys.executable, "-m", "pip", "install", "-r", "requirements.txt"
        ], stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
        
        return True
    except subprocess.CalledProcessError:
        print("خطأ في تثبيت المتطلبات")
        print("Error installing requirements")
        return False

def main():
    """الدالة الرئيسية"""
    print("=" * 50)
    print("    مدير التحميل - Download Manager")
    print("=" * 50)
    print()
    
    # التحقق من إصدار Python
    if not check_python_version():
        input("اضغط Enter للخروج...")
        return
    
    # تثبيت المتطلبات
    if not install_requirements():
        input("اضغط Enter للخروج...")
        return
    
    # تشغيل البرنامج
    try:
        print("تشغيل البرنامج المحسن...")
        print("Starting enhanced application...")
        print()

        # محاولة تشغيل الإصدار المحسن أولاً
        try:
            from main_idm import main as run_idm_app
            print("🎨 تشغيل الإصدار المحسن بتصميم IDM...")
            run_idm_app()
        except ImportError:
            # في حالة عدم توفر الإصدار المحسن، تشغيل الإصدار الأساسي
            print("⚠️ الإصدار المحسن غير متوفر، تشغيل الإصدار الأساسي...")
            from main import main as run_app
            run_app()
        
    except ImportError as e:
        print(f"خطأ في استيراد الوحدات: {e}")
        print(f"Import error: {e}")
        input("اضغط Enter للخروج...")
    except KeyboardInterrupt:
        print("\nتم إيقاف البرنامج بواسطة المستخدم")
        print("Application stopped by user")
    except Exception as e:
        print(f"خطأ غير متوقع: {e}")
        print(f"Unexpected error: {e}")
        input("اضغط Enter للخروج...")

if __name__ == "__main__":
    main()
