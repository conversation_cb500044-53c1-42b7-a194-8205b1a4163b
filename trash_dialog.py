"""
نافذة مدير سلة المحذوفات - Trash Manager Dialog
إدارة العناصر المحذوفة مع إمكانية الاستعادة والحذف النهائي
"""

import tkinter as tk
from tkinter import ttk, messagebox
from datetime import datetime
from theme_manager import theme_manager
from language_manager import language_manager

class TrashManagerDialog:
    """نافذة مدير سلة المحذوفات"""
    
    def __init__(self, parent, delete_manager):
        self.parent = parent
        self.delete_manager = delete_manager
        
        # إنشاء النافذة
        self.dialog = tk.Toplevel(parent)
        self.dialog.title(language_manager.get_text("trash_manager", "مدير سلة المحذوفات"))
        self.dialog.geometry("800x600")
        self.dialog.resizable(True, True)
        self.dialog.transient(parent)
        self.dialog.grab_set()
        
        # الحصول على الثيم الحالي
        self.colors = theme_manager.get_current_theme()
        
        # تطبيق الثيم على النافذة
        theme_manager.apply_theme_to_widget(self.dialog, "main_window")
        
        # إعداد الواجهة
        self.setup_ui()
        
        # تحميل البيانات
        self.refresh_data()
        
        # توسيط النافذة
        self.center_window()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # الإطار الرئيسي
        main_frame = tk.Frame(self.dialog)
        main_frame.pack(fill='both', expand=True, padx=10, pady=10)
        theme_manager.apply_theme_to_widget(main_frame, "frame")
        
        # شريط الأدوات العلوي
        self.create_toolbar(main_frame)
        
        # لوحة الإحصائيات
        self.create_statistics_panel(main_frame)
        
        # جدول العناصر المحذوفة
        self.create_items_table(main_frame)
        
        # أزرار التحكم
        self.create_control_buttons(main_frame)
    
    def create_toolbar(self, parent):
        """إنشاء شريط الأدوات"""
        toolbar_frame = tk.Frame(parent)
        toolbar_frame.pack(fill='x', pady=(0, 10))
        theme_manager.apply_theme_to_widget(toolbar_frame, "toolbar")
        
        # عنوان
        title_label = tk.Label(toolbar_frame, 
                              text="🗑️ " + language_manager.get_text("trash_manager", "مدير سلة المحذوفات"),
                              font=('Segoe UI', 12, 'bold'))
        title_label.pack(side='left', padx=10, pady=10)
        theme_manager.apply_theme_to_widget(title_label, "label")
        
        # أزرار شريط الأدوات
        buttons_frame = tk.Frame(toolbar_frame)
        buttons_frame.pack(side='right', padx=10, pady=5)
        theme_manager.apply_theme_to_widget(buttons_frame, "frame")
        
        # زر تحديث
        refresh_btn = tk.Button(buttons_frame, 
                               text="🔄 " + language_manager.get_text("refresh", "تحديث"),
                               command=self.refresh_data)
        refresh_btn.pack(side='left', padx=5)
        theme_manager.apply_theme_to_widget(refresh_btn, "button_secondary")
        
        # زر البحث
        search_btn = tk.Button(buttons_frame, 
                              text="🔍 " + language_manager.get_text("search", "بحث"),
                              command=self.show_search_dialog)
        search_btn.pack(side='left', padx=5)
        theme_manager.apply_theme_to_widget(search_btn, "button_secondary")
    
    def create_statistics_panel(self, parent):
        """إنشاء لوحة الإحصائيات"""
        stats_frame = tk.LabelFrame(parent, 
                                   text=" " + language_manager.get_text("statistics", "إحصائيات") + " ",
                                   font=('Segoe UI', 10, 'bold'))
        stats_frame.pack(fill='x', pady=(0, 10))
        theme_manager.apply_theme_to_widget(stats_frame, "labelframe")
        
        # إطار الإحصائيات
        stats_container = tk.Frame(stats_frame)
        stats_container.pack(fill='x', padx=10, pady=10)
        theme_manager.apply_theme_to_widget(stats_container, "frame")
        
        # الإحصائيات
        self.total_items_label = tk.Label(stats_container, text="إجمالي العناصر: 0")
        self.total_items_label.grid(row=0, column=0, sticky='w', padx=10, pady=2)
        theme_manager.apply_theme_to_widget(self.total_items_label, "label")
        
        self.total_size_label = tk.Label(stats_container, text="الحجم الإجمالي: 0 B")
        self.total_size_label.grid(row=0, column=1, sticky='w', padx=10, pady=2)
        theme_manager.apply_theme_to_widget(self.total_size_label, "label")
        
        self.downloads_count_label = tk.Label(stats_container, text="التحميلات: 0")
        self.downloads_count_label.grid(row=1, column=0, sticky='w', padx=10, pady=2)
        theme_manager.apply_theme_to_widget(self.downloads_count_label, "label")
        
        self.files_count_label = tk.Label(stats_container, text="الملفات: 0")
        self.files_count_label.grid(row=1, column=1, sticky='w', padx=10, pady=2)
        theme_manager.apply_theme_to_widget(self.files_count_label, "label")
    
    def create_items_table(self, parent):
        """إنشاء جدول العناصر المحذوفة"""
        table_frame = tk.LabelFrame(parent, 
                                   text=" " + language_manager.get_text("deleted_items", "العناصر المحذوفة") + " ",
                                   font=('Segoe UI', 10, 'bold'))
        table_frame.pack(fill='both', expand=True, pady=(0, 10))
        theme_manager.apply_theme_to_widget(table_frame, "labelframe")
        
        # إطار الجدول
        tree_frame = tk.Frame(table_frame)
        tree_frame.pack(fill='both', expand=True, padx=10, pady=10)
        tree_frame.columnconfigure(0, weight=1)
        tree_frame.rowconfigure(0, weight=1)
        
        # الجدول
        columns = ('النوع', 'اسم الملف', 'تاريخ الحذف', 'الحجم', 'يمكن الاستعادة')
        self.items_tree = ttk.Treeview(tree_frame, columns=columns, show='headings', height=15)
        
        # تكوين الأعمدة
        column_widths = {
            'النوع': 80,
            'اسم الملف': 250,
            'تاريخ الحذف': 150,
            'الحجم': 100,
            'يمكن الاستعادة': 120
        }
        
        for col in columns:
            self.items_tree.heading(col, text=col)
            self.items_tree.column(col, width=column_widths.get(col, 100), minwidth=50)
        
        # أشرطة التمرير
        v_scrollbar = ttk.Scrollbar(tree_frame, orient='vertical', command=self.items_tree.yview)
        h_scrollbar = ttk.Scrollbar(tree_frame, orient='horizontal', command=self.items_tree.xview)
        
        self.items_tree.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)
        
        # ترتيب العناصر
        self.items_tree.grid(row=0, column=0, sticky='nsew')
        v_scrollbar.grid(row=0, column=1, sticky='ns')
        h_scrollbar.grid(row=1, column=0, sticky='ew')
        
        # ربط الأحداث
        self.items_tree.bind('<Double-1>', self.on_item_double_click)
        self.items_tree.bind('<Button-3>', self.show_context_menu)
        
        # قائمة السياق
        self.setup_context_menu()
    
    def create_control_buttons(self, parent):
        """إنشاء أزرار التحكم"""
        buttons_frame = tk.Frame(parent)
        buttons_frame.pack(fill='x', pady=(10, 0))
        theme_manager.apply_theme_to_widget(buttons_frame, "frame")
        
        # زر استعادة
        restore_btn = tk.Button(buttons_frame, 
                               text="♻️ " + language_manager.get_text("restore", "استعادة"),
                               command=self.restore_selected_item)
        restore_btn.pack(side='left', padx=5)
        theme_manager.apply_theme_to_widget(restore_btn, "button_success")
        
        # زر حذف نهائي
        permanent_delete_btn = tk.Button(buttons_frame, 
                                        text="🗑️ " + language_manager.get_text("permanent_delete", "حذف نهائي"),
                                        command=self.permanent_delete_selected_item)
        permanent_delete_btn.pack(side='left', padx=5)
        theme_manager.apply_theme_to_widget(permanent_delete_btn, "button_error")
        
        # زر إفراغ سلة المحذوفات
        empty_trash_btn = tk.Button(buttons_frame, 
                                   text="🧹 " + language_manager.get_text("empty_trash", "إفراغ سلة المحذوفات"),
                                   command=self.empty_trash)
        empty_trash_btn.pack(side='left', padx=5)
        theme_manager.apply_theme_to_widget(empty_trash_btn, "button_warning")
        
        # زر إغلاق
        close_btn = tk.Button(buttons_frame, 
                             text="❌ " + language_manager.get_text("close", "إغلاق"),
                             command=self.close_dialog)
        close_btn.pack(side='right', padx=5)
        theme_manager.apply_theme_to_widget(close_btn, "button_secondary")
    
    def setup_context_menu(self):
        """إعداد قائمة السياق"""
        self.context_menu = tk.Menu(self.dialog, tearoff=0)
        
        self.context_menu.add_command(
            label=language_manager.get_text("restore", "استعادة"),
            command=self.restore_selected_item
        )
        
        self.context_menu.add_command(
            label=language_manager.get_text("permanent_delete", "حذف نهائي"),
            command=self.permanent_delete_selected_item
        )
        
        self.context_menu.add_separator()
        
        self.context_menu.add_command(
            label=language_manager.get_text("properties", "خصائص"),
            command=self.show_item_properties
        )
    
    def show_context_menu(self, event):
        """عرض قائمة السياق"""
        try:
            item = self.items_tree.selection()[0]
            if item:
                self.context_menu.post(event.x_root, event.y_root)
        except IndexError:
            pass
    
    def refresh_data(self):
        """تحديث البيانات"""
        # مسح الجدول
        for item in self.items_tree.get_children():
            self.items_tree.delete(item)
        
        # تحميل العناصر المحذوفة
        deleted_items = self.delete_manager.get_deleted_items()
        
        for item in deleted_items:
            # تنسيق البيانات
            item_type = "📥" if item['type'] == 'download' else "📄"
            filename = item.get('download_info', {}).get('filename', 'غير محدد')
            deleted_at = self.format_datetime(item['deleted_at'])
            size = "غير محدد"
            can_restore = "✅ نعم" if item['can_restore'] else "❌ لا"
            
            # إضافة إلى الجدول
            self.items_tree.insert('', 'end', values=(
                item_type, filename, deleted_at, size, can_restore
            ), tags=[item['id']])
        
        # تحديث الإحصائيات
        self.update_statistics()
    
    def update_statistics(self):
        """تحديث الإحصائيات"""
        stats = self.delete_manager.get_statistics()
        
        self.total_items_label.config(text=f"إجمالي العناصر: {stats.get('total_items', 0)}")
        self.total_size_label.config(text=f"الحجم الإجمالي: {stats.get('total_size_formatted', '0 B')}")
        self.downloads_count_label.config(text=f"التحميلات: {stats.get('downloads_count', 0)}")
        self.files_count_label.config(text=f"الملفات: {stats.get('files_count', 0)}")
    
    def restore_selected_item(self):
        """استعادة العنصر المحدد"""
        try:
            selected_item = self.items_tree.selection()[0]
            if selected_item:
                tags = self.items_tree.item(selected_item, 'tags')
                if tags:
                    download_id = tags[0]
                    
                    success, message = self.delete_manager.restore_download(download_id)
                    
                    if success:
                        messagebox.showinfo(language_manager.get_text("success", "نجح"), message)
                        self.refresh_data()
                    else:
                        messagebox.showerror(language_manager.get_text("error", "خطأ"), message)
        except IndexError:
            messagebox.showwarning(language_manager.get_text("warning", "تحذير"), 
                                 "يرجى تحديد عنصر للاستعادة")
    
    def permanent_delete_selected_item(self):
        """حذف العنصر المحدد نهائياً"""
        try:
            selected_item = self.items_tree.selection()[0]
            if selected_item:
                if messagebox.askyesno(language_manager.get_text("warning", "تحذير"), 
                                     "هل تريد حذف هذا العنصر نهائياً؟ لا يمكن التراجع عن هذا الإجراء."):
                    
                    tags = self.items_tree.item(selected_item, 'tags')
                    if tags:
                        download_id = tags[0]
                        
                        success, message = self.delete_manager.permanent_delete(download_id)
                        
                        if success:
                            messagebox.showinfo(language_manager.get_text("success", "نجح"), message)
                            self.refresh_data()
                        else:
                            messagebox.showerror(language_manager.get_text("error", "خطأ"), message)
        except IndexError:
            messagebox.showwarning(language_manager.get_text("warning", "تحذير"), 
                                 "يرجى تحديد عنصر للحذف")
    
    def empty_trash(self):
        """إفراغ سلة المحذوفات"""
        if messagebox.askyesno(language_manager.get_text("warning", "تحذير"), 
                             "هل تريد إفراغ سلة المحذوفات بالكامل؟ سيتم حذف جميع العناصر نهائياً."):
            
            success, message = self.delete_manager.empty_trash()
            
            if success:
                messagebox.showinfo(language_manager.get_text("success", "نجح"), message)
                self.refresh_data()
            else:
                messagebox.showerror(language_manager.get_text("error", "خطأ"), message)
    
    def show_search_dialog(self):
        """عرض نافذة البحث"""
        search_query = tk.simpledialog.askstring("بحث", "ادخل كلمة البحث:")
        if search_query:
            self.search_items(search_query)
    
    def search_items(self, query):
        """البحث في العناصر"""
        results = self.delete_manager.search_deleted_items(query)
        
        # مسح الجدول
        for item in self.items_tree.get_children():
            self.items_tree.delete(item)
        
        # عرض النتائج
        for item in results:
            item_type = "📥" if item['type'] == 'download' else "📄"
            filename = item.get('download_info', {}).get('filename', 'غير محدد')
            deleted_at = self.format_datetime(item['deleted_at'])
            size = "غير محدد"
            can_restore = "✅ نعم" if item['can_restore'] else "❌ لا"
            
            self.items_tree.insert('', 'end', values=(
                item_type, filename, deleted_at, size, can_restore
            ), tags=[item['id']])
    
    def on_item_double_click(self, event):
        """عند النقر المزدوج على عنصر"""
        self.show_item_properties()
    
    def show_item_properties(self):
        """عرض خصائص العنصر"""
        try:
            selected_item = self.items_tree.selection()[0]
            if selected_item:
                tags = self.items_tree.item(selected_item, 'tags')
                if tags:
                    download_id = tags[0]
                    
                    # البحث عن العنصر
                    for item in self.delete_manager.get_deleted_items():
                        if item['id'] == download_id:
                            self.show_properties_dialog(item)
                            break
        except IndexError:
            pass
    
    def show_properties_dialog(self, item):
        """عرض نافذة خصائص العنصر"""
        props_dialog = tk.Toplevel(self.dialog)
        props_dialog.title("خصائص العنصر")
        props_dialog.geometry("400x300")
        props_dialog.transient(self.dialog)
        props_dialog.grab_set()
        
        # محتوى النافذة
        content = f"""
معرف العنصر: {item['id']}
النوع: {item['type']}
تاريخ الحذف: {self.format_datetime(item['deleted_at'])}
يمكن الاستعادة: {'نعم' if item['can_restore'] else 'لا'}
حذف الملف: {'نعم' if item['file_deleted'] else 'لا'}

معلومات التحميل:
اسم الملف: {item.get('download_info', {}).get('filename', 'غير محدد')}
الرابط: {item.get('download_info', {}).get('url', 'غير محدد')}
        """
        
        text_widget = tk.Text(props_dialog, wrap='word', padx=10, pady=10)
        text_widget.pack(fill='both', expand=True)
        text_widget.insert('1.0', content)
        text_widget.config(state='disabled')
        
        # زر إغلاق
        close_btn = tk.Button(props_dialog, text="إغلاق", command=props_dialog.destroy)
        close_btn.pack(pady=10)
    
    def format_datetime(self, datetime_str):
        """تنسيق التاريخ والوقت"""
        try:
            dt = datetime.fromisoformat(datetime_str)
            return dt.strftime("%Y-%m-%d %H:%M")
        except:
            return datetime_str
    
    def center_window(self):
        """توسيط النافذة"""
        self.dialog.update_idletasks()
        x = (self.dialog.winfo_screenwidth() // 2) - (self.dialog.winfo_width() // 2)
        y = (self.dialog.winfo_screenheight() // 2) - (self.dialog.winfo_height() // 2)
        self.dialog.geometry(f"+{x}+{y}")
    
    def close_dialog(self):
        """إغلاق النافذة"""
        self.dialog.destroy()
    
    def show(self):
        """عرض النافذة"""
        self.dialog.wait_window()
