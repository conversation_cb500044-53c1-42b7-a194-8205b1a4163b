"""
مدير التحميلات المتعددة - Multi Download Manager
يدير عدة تحميلات في نفس الوقت مع جدولة وأولويات
"""

import threading
import queue
import time
import json
import os
from datetime import datetime
from enum import Enum
from downloader import AdvancedDownloader

class DownloadStatus(Enum):
    """حالات التحميل"""
    PENDING = "في الانتظار"
    DOWNLOADING = "جاري التحميل"
    PAUSED = "متوقف مؤقتاً"
    COMPLETED = "مكتمل"
    FAILED = "فشل"
    CANCELLED = "ملغي"

class DownloadPriority(Enum):
    """أولويات التحميل"""
    LOW = 1
    NORMAL = 2
    HIGH = 3
    URGENT = 4

class DownloadItem:
    """عنصر تحميل واحد"""
    
    def __init__(self, url, save_path, filename=None, priority=DownloadPriority.NORMAL):
        self.id = self.generate_id()
        self.url = url
        self.save_path = save_path
        self.filename = filename
        self.priority = priority
        self.status = DownloadStatus.PENDING
        self.downloader = AdvancedDownloader()
        self.created_at = datetime.now()
        self.started_at = None
        self.completed_at = None
        self.error_message = None
        self.progress_data = {}
        self.retry_count = 0
        self.max_retries = 3
    
    def generate_id(self):
        """توليد معرف فريد"""
        import hashlib
        timestamp = str(int(time.time() * 1000))
        return hashlib.md5(timestamp.encode()).hexdigest()[:8]
    
    def to_dict(self):
        """تحويل إلى قاموس للحفظ"""
        return {
            'id': self.id,
            'url': self.url,
            'save_path': self.save_path,
            'filename': self.filename,
            'priority': self.priority.value,
            'status': self.status.value,
            'created_at': self.created_at.isoformat(),
            'started_at': self.started_at.isoformat() if self.started_at else None,
            'completed_at': self.completed_at.isoformat() if self.completed_at else None,
            'error_message': self.error_message,
            'progress_data': self.progress_data,
            'retry_count': self.retry_count
        }

class MultiDownloadManager:
    """مدير التحميلات المتعددة"""
    
    def __init__(self, max_concurrent=3, auto_start=True):
        self.max_concurrent = max_concurrent
        self.auto_start = auto_start
        self.downloads = {}  # معرف -> DownloadItem
        self.download_queue = queue.PriorityQueue()
        self.active_downloads = {}
        self.completed_downloads = []
        self.failed_downloads = []
        
        # إعدادات
        self.auto_retry = True
        self.retry_delay = 5  # ثواني
        self.save_state_file = "downloads_state.json"
        
        # خيوط العمل
        self.manager_thread = None
        self.is_running = False
        
        # دوال الاستدعاء
        self.progress_callback = None
        self.status_change_callback = None
        self.completion_callback = None
        self.error_callback = None
        
        # إحصائيات
        self.total_downloaded = 0
        self.session_start_time = time.time()
        
        # تحميل الحالة المحفوظة
        self.load_state()
        
        if self.auto_start:
            self.start_manager()
    
    def set_callbacks(self, progress_callback=None, status_change_callback=None, 
                     completion_callback=None, error_callback=None):
        """تعيين دوال الاستدعاء"""
        self.progress_callback = progress_callback
        self.status_change_callback = status_change_callback
        self.completion_callback = completion_callback
        self.error_callback = error_callback
    
    def add_download(self, url, save_path, filename=None, priority=DownloadPriority.NORMAL):
        """إضافة تحميل جديد"""
        download_item = DownloadItem(url, save_path, filename, priority)
        self.downloads[download_item.id] = download_item
        
        # إضافة إلى طابور التحميل
        self.download_queue.put((-priority.value, time.time(), download_item.id))
        
        # إشعار بالتغيير
        if self.status_change_callback:
            self.status_change_callback(download_item.id, DownloadStatus.PENDING)
        
        self.save_state()
        return download_item.id
    
    def start_manager(self):
        """بدء مدير التحميلات"""
        if self.is_running:
            return
        
        self.is_running = True
        self.manager_thread = threading.Thread(target=self._manager_loop, daemon=True)
        self.manager_thread.start()
    
    def stop_manager(self):
        """إيقاف مدير التحميلات"""
        self.is_running = False
        
        # إيقاف جميع التحميلات النشطة
        for download_id in list(self.active_downloads.keys()):
            self.pause_download(download_id)
    
    def _manager_loop(self):
        """حلقة إدارة التحميلات الرئيسية"""
        while self.is_running:
            try:
                # التحقق من إمكانية بدء تحميلات جديدة
                if len(self.active_downloads) < self.max_concurrent and not self.download_queue.empty():
                    try:
                        priority, timestamp, download_id = self.download_queue.get_nowait()
                        if download_id in self.downloads:
                            self._start_download(download_id)
                    except queue.Empty:
                        pass
                
                # التحقق من التحميلات النشطة
                self._check_active_downloads()
                
                # حفظ الحالة دورياً
                self.save_state()
                
                time.sleep(1)  # فترة انتظار قصيرة
                
            except Exception as e:
                if self.error_callback:
                    self.error_callback(f"خطأ في مدير التحميلات: {str(e)}")
                time.sleep(5)
    
    def _start_download(self, download_id):
        """بدء تحميل واحد"""
        if download_id not in self.downloads:
            return
        
        download_item = self.downloads[download_id]
        download_item.status = DownloadStatus.DOWNLOADING
        download_item.started_at = datetime.now()
        
        # إعداد دوال الاستدعاء للتحميل
        download_item.downloader.set_callbacks(
            progress_callback=lambda data, did=download_id: self._on_progress(did, data),
            completion_callback=lambda path, did=download_id: self._on_completion(did, path),
            error_callback=lambda error, did=download_id: self._on_error(did, error)
        )
        
        # بدء التحميل
        success = download_item.downloader.start_download(
            download_item.url,
            download_item.save_path,
            download_item.filename
        )
        
        if success:
            self.active_downloads[download_id] = download_item
            if self.status_change_callback:
                self.status_change_callback(download_id, DownloadStatus.DOWNLOADING)
        else:
            download_item.status = DownloadStatus.FAILED
            download_item.error_message = "فشل في بدء التحميل"
            if self.status_change_callback:
                self.status_change_callback(download_id, DownloadStatus.FAILED)
    
    def _on_progress(self, download_id, progress_data):
        """معالج تحديث التقدم"""
        if download_id in self.downloads:
            self.downloads[download_id].progress_data = progress_data
            if self.progress_callback:
                self.progress_callback(download_id, progress_data)
    
    def _on_completion(self, download_id, file_path):
        """معالج اكتمال التحميل"""
        if download_id in self.downloads:
            download_item = self.downloads[download_id]
            download_item.status = DownloadStatus.COMPLETED
            download_item.completed_at = datetime.now()
            
            # إزالة من التحميلات النشطة
            if download_id in self.active_downloads:
                del self.active_downloads[download_id]
            
            # إضافة إلى المكتملة
            self.completed_downloads.append(download_item)
            
            # تحديث الإحصائيات
            if 'total' in download_item.progress_data:
                self.total_downloaded += download_item.progress_data['total']
            
            if self.status_change_callback:
                self.status_change_callback(download_id, DownloadStatus.COMPLETED)
            
            if self.completion_callback:
                self.completion_callback(download_id, file_path)
    
    def _on_error(self, download_id, error_message):
        """معالج أخطاء التحميل"""
        if download_id in self.downloads:
            download_item = self.downloads[download_id]
            download_item.error_message = error_message
            download_item.retry_count += 1
            
            # إزالة من التحميلات النشطة
            if download_id in self.active_downloads:
                del self.active_downloads[download_id]
            
            # إعادة المحاولة أو الفشل النهائي
            if self.auto_retry and download_item.retry_count < download_item.max_retries:
                download_item.status = DownloadStatus.PENDING
                # إعادة إضافة إلى الطابور مع تأخير
                threading.Timer(self.retry_delay, lambda: self.download_queue.put(
                    (-download_item.priority.value, time.time(), download_id)
                )).start()
                
                if self.status_change_callback:
                    self.status_change_callback(download_id, DownloadStatus.PENDING)
            else:
                download_item.status = DownloadStatus.FAILED
                self.failed_downloads.append(download_item)
                
                if self.status_change_callback:
                    self.status_change_callback(download_id, DownloadStatus.FAILED)
                
                if self.error_callback:
                    self.error_callback(f"فشل التحميل {download_id}: {error_message}")
    
    def _check_active_downloads(self):
        """فحص التحميلات النشطة"""
        for download_id in list(self.active_downloads.keys()):
            download_item = self.active_downloads[download_id]
            if not download_item.downloader.is_downloading:
                # التحميل توقف لسبب ما
                if download_item.status == DownloadStatus.DOWNLOADING:
                    download_item.status = DownloadStatus.FAILED
                    download_item.error_message = "التحميل توقف بشكل غير متوقع"
                    self._on_error(download_id, download_item.error_message)
    
    def pause_download(self, download_id):
        """إيقاف تحميل مؤقتاً"""
        if download_id in self.active_downloads:
            download_item = self.active_downloads[download_id]
            download_item.downloader.pause_download()
            download_item.status = DownloadStatus.PAUSED
            
            if self.status_change_callback:
                self.status_change_callback(download_id, DownloadStatus.PAUSED)
            
            return True
        return False
    
    def resume_download(self, download_id):
        """استئناف تحميل"""
        if download_id in self.active_downloads:
            download_item = self.active_downloads[download_id]
            download_item.downloader.resume_download()
            download_item.status = DownloadStatus.DOWNLOADING
            
            if self.status_change_callback:
                self.status_change_callback(download_id, DownloadStatus.DOWNLOADING)
            
            return True
        elif download_id in self.downloads and self.downloads[download_id].status == DownloadStatus.PAUSED:
            # إعادة إضافة إلى الطابور
            download_item = self.downloads[download_id]
            self.download_queue.put((-download_item.priority.value, time.time(), download_id))
            download_item.status = DownloadStatus.PENDING
            
            if self.status_change_callback:
                self.status_change_callback(download_id, DownloadStatus.PENDING)
            
            return True
        return False
    
    def cancel_download(self, download_id):
        """إلغاء تحميل"""
        if download_id in self.active_downloads:
            download_item = self.active_downloads[download_id]
            download_item.downloader.stop_download()
            download_item.status = DownloadStatus.CANCELLED
            del self.active_downloads[download_id]
            
            if self.status_change_callback:
                self.status_change_callback(download_id, DownloadStatus.CANCELLED)
            
            return True
        elif download_id in self.downloads:
            self.downloads[download_id].status = DownloadStatus.CANCELLED
            
            if self.status_change_callback:
                self.status_change_callback(download_id, DownloadStatus.CANCELLED)
            
            return True
        return False
    
    def get_download_info(self, download_id):
        """الحصول على معلومات التحميل"""
        if download_id in self.downloads:
            return self.downloads[download_id].to_dict()
        return None
    
    def get_all_downloads(self):
        """الحصول على جميع التحميلات"""
        return [item.to_dict() for item in self.downloads.values()]
    
    def get_downloads_by_status(self, status):
        """الحصول على التحميلات حسب الحالة"""
        return [item.to_dict() for item in self.downloads.values() if item.status == status]
    
    def get_statistics(self):
        """الحصول على إحصائيات شاملة"""
        session_time = time.time() - self.session_start_time
        
        stats = {
            'total_downloads': len(self.downloads),
            'active_downloads': len(self.active_downloads),
            'completed_downloads': len(self.completed_downloads),
            'failed_downloads': len(self.failed_downloads),
            'pending_downloads': self.download_queue.qsize(),
            'total_downloaded_bytes': self.total_downloaded,
            'total_downloaded_formatted': self._format_size(self.total_downloaded),
            'session_time': session_time,
            'session_time_formatted': self._format_time(session_time),
            'average_speed': self.total_downloaded / session_time if session_time > 0 else 0,
            'max_concurrent': self.max_concurrent,
            'auto_retry': self.auto_retry
        }
        
        return stats
    
    def save_state(self):
        """حفظ حالة جميع التحميلات"""
        try:
            state = {
                'downloads': [item.to_dict() for item in self.downloads.values()],
                'settings': {
                    'max_concurrent': self.max_concurrent,
                    'auto_retry': self.auto_retry,
                    'retry_delay': self.retry_delay
                },
                'statistics': self.get_statistics(),
                'saved_at': datetime.now().isoformat()
            }
            
            with open(self.save_state_file, 'w', encoding='utf-8') as f:
                json.dump(state, f, ensure_ascii=False, indent=2)
                
        except Exception as e:
            if self.error_callback:
                self.error_callback(f"فشل في حفظ الحالة: {str(e)}")
    
    def load_state(self):
        """تحميل الحالة المحفوظة"""
        try:
            if os.path.exists(self.save_state_file):
                with open(self.save_state_file, 'r', encoding='utf-8') as f:
                    state = json.load(f)
                
                # استرجاع التحميلات
                for download_data in state.get('downloads', []):
                    if download_data['status'] not in [DownloadStatus.COMPLETED.value, DownloadStatus.CANCELLED.value]:
                        # إعادة إنشاء عنصر التحميل
                        download_item = DownloadItem(
                            download_data['url'],
                            download_data['save_path'],
                            download_data['filename'],
                            DownloadPriority(download_data['priority'])
                        )
                        download_item.id = download_data['id']
                        download_item.status = DownloadStatus(download_data['status'])
                        download_item.retry_count = download_data.get('retry_count', 0)
                        
                        self.downloads[download_item.id] = download_item
                        
                        # إضافة إلى الطابور إذا كان في الانتظار
                        if download_item.status == DownloadStatus.PENDING:
                            self.download_queue.put((-download_item.priority.value, time.time(), download_item.id))
                
                # استرجاع الإعدادات
                settings = state.get('settings', {})
                self.max_concurrent = settings.get('max_concurrent', self.max_concurrent)
                self.auto_retry = settings.get('auto_retry', self.auto_retry)
                self.retry_delay = settings.get('retry_delay', self.retry_delay)
                
        except Exception as e:
            if self.error_callback:
                self.error_callback(f"فشل في تحميل الحالة: {str(e)}")
    
    def _format_size(self, size_bytes):
        """تنسيق حجم الملف"""
        if size_bytes == 0:
            return "0 B"
        size_names = ["B", "KB", "MB", "GB", "TB"]
        i = 0
        while size_bytes >= 1024 and i < len(size_names) - 1:
            size_bytes /= 1024.0
            i += 1
        return f"{size_bytes:.2f} {size_names[i]}"
    
    def _format_time(self, seconds):
        """تنسيق الوقت"""
        if seconds < 60:
            return f"{int(seconds)} ثانية"
        elif seconds < 3600:
            return f"{int(seconds / 60)} دقيقة"
        else:
            return f"{int(seconds / 3600)} ساعة"
