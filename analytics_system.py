"""
نظام الإحصائيات والتحليلات المتقدم - Advanced Analytics System
تتبع وتحليل أداء التحميلات مع رسوم بيانية وتقارير
"""

import json
import os
from datetime import datetime, timedelta
from collections import defaultdict, deque
import threading
import time
from language_manager import language_manager

try:
    import matplotlib.pyplot as plt
    import matplotlib.dates as mdates
    from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
    MATPLOTLIB_AVAILABLE = True
except ImportError:
    MATPLOTLIB_AVAILABLE = False

class AnalyticsSystem:
    """نظام الإحصائيات والتحليلات المتقدم"""
    
    def __init__(self):
        self.data_file = "analytics_data.json"
        self.session_start = datetime.now()
        
        # بيانات الجلسة الحالية
        self.session_data = {
            'downloads_started': 0,
            'downloads_completed': 0,
            'downloads_failed': 0,
            'total_bytes_downloaded': 0,
            'total_time_spent': 0,
            'peak_speed': 0,
            'average_speed': 0,
            'active_time': 0
        }
        
        # بيانات تاريخية
        self.historical_data = self.load_historical_data()
        
        # بيانات الوقت الفعلي
        self.real_time_data = {
            'speed_history': deque(maxlen=100),  # آخر 100 قراءة سرعة
            'download_progress': {},  # تقدم التحميلات النشطة
            'system_resources': deque(maxlen=50),  # استخدام الموارد
            'network_usage': deque(maxlen=60)  # استخدام الشبكة
        }
        
        # إعدادات التتبع
        self.tracking_enabled = True
        self.detailed_logging = True
        self.auto_save_interval = 300  # 5 دقائق
        
        # بدء التتبع
        self.start_tracking()
    
    def load_historical_data(self):
        """تحميل البيانات التاريخية"""
        try:
            if os.path.exists(self.data_file):
                with open(self.data_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            return {
                'daily_stats': {},
                'weekly_stats': {},
                'monthly_stats': {},
                'download_history': [],
                'performance_metrics': [],
                'user_behavior': {}
            }
        except Exception as e:
            print(f"خطأ في تحميل البيانات التاريخية: {e}")
            return {}
    
    def save_historical_data(self):
        """حفظ البيانات التاريخية"""
        try:
            with open(self.data_file, 'w', encoding='utf-8') as f:
                json.dump(self.historical_data, f, ensure_ascii=False, indent=2, default=str)
        except Exception as e:
            print(f"خطأ في حفظ البيانات التاريخية: {e}")
    
    def start_tracking(self):
        """بدء التتبع التلقائي"""
        if not self.tracking_enabled:
            return
        
        def tracking_loop():
            while self.tracking_enabled:
                try:
                    # تحديث بيانات الوقت الفعلي
                    self.update_real_time_data()
                    
                    # حفظ دوري
                    if int(time.time()) % self.auto_save_interval == 0:
                        self.save_session_data()
                    
                    time.sleep(1)  # تحديث كل ثانية
                except Exception as e:
                    print(f"خطأ في حلقة التتبع: {e}")
                    time.sleep(5)
        
        tracking_thread = threading.Thread(target=tracking_loop, daemon=True)
        tracking_thread.start()
    
    def update_real_time_data(self):
        """تحديث بيانات الوقت الفعلي"""
        try:
            # تحديث استخدام الموارد
            self.update_system_resources()
            
            # تحديث استخدام الشبكة
            self.update_network_usage()
            
        except Exception as e:
            print(f"خطأ في تحديث البيانات الفورية: {e}")
    
    def update_system_resources(self):
        """تحديث استخدام موارد النظام"""
        try:
            import psutil
            
            cpu_percent = psutil.cpu_percent()
            memory_percent = psutil.virtual_memory().percent
            disk_usage = psutil.disk_usage('/').percent
            
            resource_data = {
                'timestamp': datetime.now(),
                'cpu': cpu_percent,
                'memory': memory_percent,
                'disk': disk_usage
            }
            
            self.real_time_data['system_resources'].append(resource_data)
            
        except ImportError:
            pass  # psutil غير متوفر
        except Exception as e:
            print(f"خطأ في تحديث موارد النظام: {e}")
    
    def update_network_usage(self):
        """تحديث استخدام الشبكة"""
        try:
            import psutil
            
            net_io = psutil.net_io_counters()
            
            network_data = {
                'timestamp': datetime.now(),
                'bytes_sent': net_io.bytes_sent,
                'bytes_recv': net_io.bytes_recv,
                'packets_sent': net_io.packets_sent,
                'packets_recv': net_io.packets_recv
            }
            
            self.real_time_data['network_usage'].append(network_data)
            
        except ImportError:
            pass
        except Exception as e:
            print(f"خطأ في تحديث استخدام الشبكة: {e}")
    
    def record_download_start(self, download_id, url, filename, file_size=0):
        """تسجيل بدء تحميل"""
        self.session_data['downloads_started'] += 1
        
        download_record = {
            'id': download_id,
            'url': url,
            'filename': filename,
            'file_size': file_size,
            'start_time': datetime.now(),
            'status': 'started'
        }
        
        if 'download_history' not in self.historical_data:
            self.historical_data['download_history'] = []
        
        self.historical_data['download_history'].append(download_record)
        
        # تحديث الإحصائيات اليومية
        self.update_daily_stats('downloads_started', 1)
    
    def record_download_progress(self, download_id, downloaded_bytes, total_bytes, speed):
        """تسجيل تقدم التحميل"""
        progress_data = {
            'downloaded': downloaded_bytes,
            'total': total_bytes,
            'speed': speed,
            'percentage': (downloaded_bytes / total_bytes * 100) if total_bytes > 0 else 0,
            'timestamp': datetime.now()
        }
        
        self.real_time_data['download_progress'][download_id] = progress_data
        
        # تحديث سرعة الذروة
        if speed > self.session_data['peak_speed']:
            self.session_data['peak_speed'] = speed
        
        # إضافة إلى تاريخ السرعة
        self.real_time_data['speed_history'].append({
            'speed': speed,
            'timestamp': datetime.now()
        })
    
    def record_download_completion(self, download_id, success=True, error_message=None):
        """تسجيل اكتمال التحميل"""
        if success:
            self.session_data['downloads_completed'] += 1
            self.update_daily_stats('downloads_completed', 1)
        else:
            self.session_data['downloads_failed'] += 1
            self.update_daily_stats('downloads_failed', 1)
        
        # تحديث سجل التحميل
        for record in self.historical_data.get('download_history', []):
            if record.get('id') == download_id:
                record['end_time'] = datetime.now()
                record['status'] = 'completed' if success else 'failed'
                if error_message:
                    record['error'] = error_message
                
                # حساب الوقت المستغرق
                if 'start_time' in record:
                    start_time = datetime.fromisoformat(record['start_time']) if isinstance(record['start_time'], str) else record['start_time']
                    duration = (datetime.now() - start_time).total_seconds()
                    record['duration'] = duration
                    self.session_data['total_time_spent'] += duration
                break
        
        # إزالة من التقدم النشط
        if download_id in self.real_time_data['download_progress']:
            progress = self.real_time_data['download_progress'][download_id]
            if success:
                self.session_data['total_bytes_downloaded'] += progress['downloaded']
            del self.real_time_data['download_progress'][download_id]
    
    def update_daily_stats(self, metric, value):
        """تحديث الإحصائيات اليومية"""
        today = datetime.now().strftime('%Y-%m-%d')
        
        if 'daily_stats' not in self.historical_data:
            self.historical_data['daily_stats'] = {}
        
        if today not in self.historical_data['daily_stats']:
            self.historical_data['daily_stats'][today] = {}
        
        current_value = self.historical_data['daily_stats'][today].get(metric, 0)
        self.historical_data['daily_stats'][today][metric] = current_value + value
    
    def get_session_statistics(self):
        """الحصول على إحصائيات الجلسة الحالية"""
        session_duration = (datetime.now() - self.session_start).total_seconds()
        
        # حساب متوسط السرعة
        if self.session_data['total_time_spent'] > 0:
            avg_speed = self.session_data['total_bytes_downloaded'] / self.session_data['total_time_spent']
        else:
            avg_speed = 0
        
        return {
            'session_duration': session_duration,
            'downloads_started': self.session_data['downloads_started'],
            'downloads_completed': self.session_data['downloads_completed'],
            'downloads_failed': self.session_data['downloads_failed'],
            'success_rate': (self.session_data['downloads_completed'] / max(self.session_data['downloads_started'], 1)) * 100,
            'total_bytes_downloaded': self.session_data['total_bytes_downloaded'],
            'total_bytes_formatted': self.format_size(self.session_data['total_bytes_downloaded']),
            'peak_speed': self.session_data['peak_speed'],
            'peak_speed_formatted': self.format_speed(self.session_data['peak_speed']),
            'average_speed': avg_speed,
            'average_speed_formatted': self.format_speed(avg_speed),
            'active_downloads': len(self.real_time_data['download_progress'])
        }
    
    def get_daily_statistics(self, days=7):
        """الحصول على إحصائيات يومية"""
        daily_stats = []
        
        for i in range(days):
            date = (datetime.now() - timedelta(days=i)).strftime('%Y-%m-%d')
            day_data = self.historical_data.get('daily_stats', {}).get(date, {})
            
            daily_stats.append({
                'date': date,
                'downloads_started': day_data.get('downloads_started', 0),
                'downloads_completed': day_data.get('downloads_completed', 0),
                'downloads_failed': day_data.get('downloads_failed', 0),
                'total_bytes': day_data.get('total_bytes_downloaded', 0)
            })
        
        return list(reversed(daily_stats))
    
    def get_speed_analytics(self):
        """تحليل السرعة"""
        speed_data = list(self.real_time_data['speed_history'])
        
        if not speed_data:
            return {
                'current_speed': 0,
                'average_speed': 0,
                'max_speed': 0,
                'min_speed': 0,
                'speed_trend': 'stable'
            }
        
        speeds = [item['speed'] for item in speed_data]
        
        current_speed = speeds[-1] if speeds else 0
        average_speed = sum(speeds) / len(speeds)
        max_speed = max(speeds)
        min_speed = min(speeds)
        
        # تحديد اتجاه السرعة
        if len(speeds) >= 10:
            recent_avg = sum(speeds[-10:]) / 10
            older_avg = sum(speeds[-20:-10]) / 10 if len(speeds) >= 20 else average_speed
            
            if recent_avg > older_avg * 1.1:
                trend = 'increasing'
            elif recent_avg < older_avg * 0.9:
                trend = 'decreasing'
            else:
                trend = 'stable'
        else:
            trend = 'stable'
        
        return {
            'current_speed': current_speed,
            'current_speed_formatted': self.format_speed(current_speed),
            'average_speed': average_speed,
            'average_speed_formatted': self.format_speed(average_speed),
            'max_speed': max_speed,
            'max_speed_formatted': self.format_speed(max_speed),
            'min_speed': min_speed,
            'min_speed_formatted': self.format_speed(min_speed),
            'speed_trend': trend,
            'data_points': len(speeds)
        }
    
    def get_performance_metrics(self):
        """مقاييس الأداء"""
        total_downloads = self.session_data['downloads_started']
        completed_downloads = self.session_data['downloads_completed']
        failed_downloads = self.session_data['downloads_failed']
        
        # معدل النجاح
        success_rate = (completed_downloads / max(total_downloads, 1)) * 100
        
        # معدل الفشل
        failure_rate = (failed_downloads / max(total_downloads, 1)) * 100
        
        # متوسط وقت التحميل
        completed_records = [r for r in self.historical_data.get('download_history', []) 
                           if r.get('status') == 'completed' and 'duration' in r]
        
        avg_download_time = 0
        if completed_records:
            total_time = sum(r['duration'] for r in completed_records)
            avg_download_time = total_time / len(completed_records)
        
        return {
            'success_rate': success_rate,
            'failure_rate': failure_rate,
            'average_download_time': avg_download_time,
            'average_download_time_formatted': self.format_time(avg_download_time),
            'total_downloads': total_downloads,
            'completed_downloads': completed_downloads,
            'failed_downloads': failed_downloads,
            'efficiency_score': min(success_rate + (100 - failure_rate), 100)
        }
    
    def generate_report(self, report_type='daily'):
        """إنشاء تقرير مفصل"""
        report = {
            'generated_at': datetime.now(),
            'report_type': report_type,
            'session_stats': self.get_session_statistics(),
            'speed_analytics': self.get_speed_analytics(),
            'performance_metrics': self.get_performance_metrics()
        }
        
        if report_type == 'daily':
            report['daily_data'] = self.get_daily_statistics(7)
        elif report_type == 'weekly':
            report['weekly_data'] = self.get_daily_statistics(30)
        elif report_type == 'monthly':
            report['monthly_data'] = self.get_daily_statistics(365)
        
        return report
    
    def export_data(self, format='json', filename=None):
        """تصدير البيانات"""
        if not filename:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"analytics_export_{timestamp}.{format}"
        
        try:
            if format == 'json':
                export_data = {
                    'session_data': self.session_data,
                    'historical_data': self.historical_data,
                    'export_timestamp': datetime.now().isoformat()
                }
                
                with open(filename, 'w', encoding='utf-8') as f:
                    json.dump(export_data, f, ensure_ascii=False, indent=2, default=str)
            
            elif format == 'csv':
                import csv
                
                with open(filename, 'w', newline='', encoding='utf-8') as f:
                    writer = csv.writer(f)
                    
                    # كتابة رؤوس الأعمدة
                    writer.writerow(['Date', 'Downloads Started', 'Downloads Completed', 'Downloads Failed', 'Total Bytes'])
                    
                    # كتابة البيانات اليومية
                    for date, stats in self.historical_data.get('daily_stats', {}).items():
                        writer.writerow([
                            date,
                            stats.get('downloads_started', 0),
                            stats.get('downloads_completed', 0),
                            stats.get('downloads_failed', 0),
                            stats.get('total_bytes_downloaded', 0)
                        ])
            
            return filename
            
        except Exception as e:
            print(f"خطأ في تصدير البيانات: {e}")
            return None
    
    def save_session_data(self):
        """حفظ بيانات الجلسة"""
        # تحديث الإحصائيات التاريخية
        today = datetime.now().strftime('%Y-%m-%d')
        
        if 'daily_stats' not in self.historical_data:
            self.historical_data['daily_stats'] = {}
        
        if today not in self.historical_data['daily_stats']:
            self.historical_data['daily_stats'][today] = {}
        
        # دمج بيانات الجلسة مع البيانات اليومية
        daily_data = self.historical_data['daily_stats'][today]
        daily_data['total_bytes_downloaded'] = daily_data.get('total_bytes_downloaded', 0) + self.session_data['total_bytes_downloaded']
        daily_data['session_time'] = daily_data.get('session_time', 0) + (datetime.now() - self.session_start).total_seconds()
        
        # حفظ البيانات
        self.save_historical_data()
    
    def format_size(self, size_bytes):
        """تنسيق حجم الملف"""
        if size_bytes == 0:
            return "0 B"
        
        size_names = ["B", "KB", "MB", "GB", "TB"]
        i = 0
        while size_bytes >= 1024 and i < len(size_names) - 1:
            size_bytes /= 1024.0
            i += 1
        
        return f"{size_bytes:.2f} {size_names[i]}"
    
    def format_speed(self, speed_bps):
        """تنسيق السرعة"""
        return self.format_size(speed_bps) + "/s"
    
    def format_time(self, seconds):
        """تنسيق الوقت"""
        if seconds < 60:
            return f"{int(seconds)}s"
        elif seconds < 3600:
            return f"{int(seconds / 60)}m {int(seconds % 60)}s"
        else:
            hours = int(seconds / 3600)
            minutes = int((seconds % 3600) / 60)
            return f"{hours}h {minutes}m"
    
    def cleanup_old_data(self, days_to_keep=90):
        """تنظيف البيانات القديمة"""
        cutoff_date = datetime.now() - timedelta(days=days_to_keep)
        
        # تنظيف البيانات اليومية
        if 'daily_stats' in self.historical_data:
            dates_to_remove = []
            for date_str in self.historical_data['daily_stats']:
                try:
                    date_obj = datetime.strptime(date_str, '%Y-%m-%d')
                    if date_obj < cutoff_date:
                        dates_to_remove.append(date_str)
                except:
                    continue
            
            for date_str in dates_to_remove:
                del self.historical_data['daily_stats'][date_str]
        
        # تنظيف سجل التحميلات
        if 'download_history' in self.historical_data:
            filtered_history = []
            for record in self.historical_data['download_history']:
                try:
                    start_time = datetime.fromisoformat(record['start_time']) if isinstance(record['start_time'], str) else record['start_time']
                    if start_time >= cutoff_date:
                        filtered_history.append(record)
                except:
                    continue
            
            self.historical_data['download_history'] = filtered_history
        
        # حفظ البيانات المنظفة
        self.save_historical_data()

# إنشاء مثيل عام لنظام الإحصائيات
analytics_system = AnalyticsSystem()
