# دليل استكشاف الأخطاء - Troubleshooting Guide

## المشاكل الشائعة وحلولها

### 1. مشاكل التثبيت

#### خطأ: "Python غير مثبت"
**الحل:**
- تأكد من تثبيت Python 3.7 أو أحدث
- أضف Python إلى متغير PATH
- أعد تشغيل سطر الأوامر

#### خطأ: "pip غير مثبت"
**الحل:**
```bash
# Windows
python -m ensurepip --upgrade

# Linux/Mac
sudo apt install python3-pip  # Ubuntu/Debian
brew install python3          # macOS
```

#### خطأ في تثبيت المكتبات
**الحل:**
```bash
# تحديث pip
pip install --upgrade pip

# تثبيت المكتبات واحدة تلو الأخرى
pip install requests
pip install tqdm
pip install urllib3
```

### 2. مشاكل الواجهة الرسومية

#### خطأ: "tkinter غير متاح"
**الحل:**
```bash
# Ubuntu/Debian
sudo apt-get install python3-tk

# CentOS/RHEL
sudo yum install tkinter

# macOS (مع Homebrew)
brew install python-tk
```

#### الواجهة لا تظهر
**الحل:**
- تأكد من وجود بيئة رسومية
- جرب تشغيل البرنامج من سطر الأوامر
- تحقق من رسائل الخطأ

### 3. مشاكل التحميل

#### خطأ: "رابط غير صحيح"
**الأسباب المحتملة:**
- الرابط لا يبدأ بـ http:// أو https://
- الرابط يحتوي على أحرف غير صحيحة
- الرابط منتهي الصلاحية

**الحل:**
- تأكد من صحة الرابط
- انسخ الرابط مباشرة من المتصفح
- جرب فتح الرابط في المتصفح أولاً

#### خطأ: "خطأ في الشبكة"
**الأسباب المحتملة:**
- انقطاع الإنترنت
- حجب الموقع من قبل جدار الحماية
- مشكلة في الخادم

**الحل:**
- تحقق من اتصال الإنترنت
- جرب رابطاً آخر
- أعد المحاولة لاحقاً

#### خطأ: "ليس لديك صلاحية للكتابة"
**الحل:**
- اختر مجلد آخر للحفظ
- تشغيل البرنامج كمدير (Windows) أو sudo (Linux)
- تغيير صلاحيات المجلد

#### التحميل بطيء جداً
**الحلول:**
- تحقق من سرعة الإنترنت
- أغلق البرامج الأخرى التي تستخدم الإنترنت
- جرب في وقت آخر
- تحقق من إعدادات جدار الحماية

### 4. مشاكل الملفات

#### خطأ: "مساحة القرص ممتلئة"
**الحل:**
- احذف ملفات غير ضرورية
- اختر قرص آخر للحفظ
- نظف سلة المحذوفات

#### الملف المحمل تالف
**الأسباب المحتملة:**
- انقطاع التحميل
- مشكلة في الخادم
- تداخل في العملية

**الحل:**
- احذف الملف وأعد التحميل
- تأكد من استقرار الاتصال
- جرب رابطاً آخر للملف نفسه

### 5. مشاكل الأداء

#### البرنامج يتجمد
**الحل:**
- أعد تشغيل البرنامج
- تحقق من استخدام الذاكرة
- أغلق البرامج الأخرى

#### استهلاك عالي للذاكرة
**الحل:**
- أعد تشغيل البرنامج
- تجنب تحميل ملفات كبيرة جداً
- تحديث Python إلى أحدث إصدار

### 6. رسائل الخطأ الشائعة

#### "ModuleNotFoundError: No module named 'requests'"
**الحل:**
```bash
pip install requests
```

#### "Permission denied"
**الحل:**
- تشغيل البرنامج كمدير
- تغيير مجلد الحفظ
- تحقق من صلاحيات المجلد

#### "Connection timeout"
**الحل:**
- تحقق من الإنترنت
- أعد المحاولة
- جرب رابطاً آخر

#### "SSL Certificate error"
**الحل:**
- تحديث Python
- تحديث مكتبة requests
- تحقق من إعدادات الأمان

### 7. نصائح للاستخدام الأمثل

#### لتحسين الأداء:
- استخدم اتصال إنترنت مستقر
- أغلق البرامج غير الضرورية
- احفظ الملفات على قرص SSD إن أمكن

#### لتجنب المشاكل:
- تأكد من صحة الروابط قبل التحميل
- احتفظ بنسخة احتياطية من الملفات المهمة
- حدث البرنامج بانتظام

#### للحصول على أفضل تجربة:
- استخدم مجلد منفصل لكل نوع ملف
- نظم الملفات المحملة بانتظام
- احذف الملفات غير المرغوب فيها

### 8. الحصول على المساعدة

إذا واجهت مشكلة لم تُذكر هنا:

1. **تحقق من رسالة الخطأ**: اقرأ رسالة الخطأ بعناية
2. **ابحث في الإنترنت**: ابحث عن رسالة الخطأ
3. **جرب الحلول البسيطة**: أعد التشغيل، أعد المحاولة
4. **اطلب المساعدة**: تواصل مع المطورين

### 9. معلومات النظام المفيدة

لتشخيص المشاكل، قد تحتاج لهذه المعلومات:

```bash
# إصدار Python
python --version

# إصدار pip
pip --version

# قائمة المكتبات المثبتة
pip list

# معلومات النظام
python -c "import platform; print(platform.platform())"
```

### 10. إعادة التثبيت

إذا فشلت جميع الحلول:

```bash
# إلغاء تثبيت المكتبات
pip uninstall requests tqdm urllib3

# إعادة التثبيت
pip install -r requirements.txt

# أو تثبيت نظيف
pip install --force-reinstall -r requirements.txt
```

---

**ملاحظة**: إذا استمرت المشاكل، تأكد من أن نظامك يدعم Python 3.7+ وأن جميع المتطلبات مثبتة بشكل صحيح.
