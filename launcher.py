#!/usr/bin/env python3
"""
مشغل البرنامج المتقدم - Advanced Program Launcher
يتيح للمستخدم اختيار الإصدار المناسب
"""

import tkinter as tk
from tkinter import ttk, messagebox
import sys
import os
import subprocess

class ProgramLauncher:
    def __init__(self, root):
        self.root = root
        self.setup_window()
        self.setup_ui()
    
    def setup_window(self):
        """إعداد النافذة"""
        self.root.title("مشغل مدير التحميل - Download Manager Launcher")
        self.root.geometry("600x500")
        self.root.resizable(False, False)
        
        # ألوان
        self.colors = {
            'bg': '#f8f9fa',
            'panel': '#ffffff',
            'accent': '#0d6efd',
            'success': '#198754',
            'warning': '#fd7e14',
            'text': '#212529'
        }
        
        self.root.configure(bg=self.colors['bg'])
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # العنوان الرئيسي
        title_frame = tk.Frame(self.root, bg=self.colors['bg'])
        title_frame.pack(pady=20)
        
        title_label = tk.Label(title_frame,
                              text="🔽 Internet Download Manager",
                              font=('Segoe UI', 18, 'bold'),
                              bg=self.colors['bg'],
                              fg=self.colors['text'])
        title_label.pack()
        
        subtitle_label = tk.Label(title_frame,
                                 text="مدير التحميل المتقدم",
                                 font=('Segoe UI', 12),
                                 bg=self.colors['bg'],
                                 fg=self.colors['text'])
        subtitle_label.pack(pady=5)
        
        # إطار الخيارات
        options_frame = tk.LabelFrame(self.root,
                                     text=" اختر الإصدار المناسب ",
                                     font=('Segoe UI', 11, 'bold'),
                                     bg=self.colors['panel'],
                                     fg=self.colors['text'],
                                     padx=20, pady=20)
        options_frame.pack(padx=30, pady=20, fill='both', expand=True)
        
        # الإصدار الأساسي
        basic_frame = tk.Frame(options_frame, bg=self.colors['panel'], relief='solid', bd=1)
        basic_frame.pack(fill='x', pady=10)
        
        tk.Label(basic_frame,
                text="📋 الإصدار الأساسي",
                font=('Segoe UI', 12, 'bold'),
                bg=self.colors['panel'],
                fg=self.colors['text']).pack(anchor='w', padx=15, pady=(10, 5))
        
        tk.Label(basic_frame,
                text="• واجهة بسيطة وسهلة الاستخدام\n• جميع الوظائف الأساسية للتحميل\n• استهلاك أقل للموارد\n• مناسب للمبتدئين",
                font=('Segoe UI', 9),
                bg=self.colors['panel'],
                fg=self.colors['text'],
                justify='left').pack(anchor='w', padx=15, pady=5)
        
        basic_btn = tk.Button(basic_frame,
                             text="🚀 تشغيل الإصدار الأساسي",
                             font=('Segoe UI', 10, 'bold'),
                             bg=self.colors['success'],
                             fg='white',
                             relief='flat',
                             padx=20, pady=8,
                             command=self.launch_basic)
        basic_btn.pack(pady=15)
        
        # الإصدار المحسن
        enhanced_frame = tk.Frame(options_frame, bg=self.colors['panel'], relief='solid', bd=1)
        enhanced_frame.pack(fill='x', pady=10)
        
        tk.Label(enhanced_frame,
                text="🎨 الإصدار المحسن (IDM Style)",
                font=('Segoe UI', 12, 'bold'),
                bg=self.colors['panel'],
                fg=self.colors['text']).pack(anchor='w', padx=15, pady=(10, 5))
        
        tk.Label(enhanced_frame,
                text="• تصميم شبيه ببرنامج IDM الأصلي\n• شريط أدوات متقدم وشريط جانبي\n• تصنيف التحميلات وألوان مخصصة\n• مميزات متقدمة ومظهر احترافي",
                font=('Segoe UI', 9),
                bg=self.colors['panel'],
                fg=self.colors['text'],
                justify='left').pack(anchor='w', padx=15, pady=5)
        
        enhanced_btn = tk.Button(enhanced_frame,
                                text="✨ تشغيل الإصدار المحسن",
                                font=('Segoe UI', 10, 'bold'),
                                bg=self.colors['accent'],
                                fg='white',
                                relief='flat',
                                padx=20, pady=8,
                                command=self.launch_enhanced)
        enhanced_btn.pack(pady=15)
        
        # أزرار إضافية
        buttons_frame = tk.Frame(self.root, bg=self.colors['bg'])
        buttons_frame.pack(pady=20)
        
        test_btn = tk.Button(buttons_frame,
                            text="🧪 تشغيل الاختبارات",
                            font=('Segoe UI', 9),
                            bg=self.colors['warning'],
                            fg='white',
                            relief='flat',
                            padx=15, pady=5,
                            command=self.run_tests)
        test_btn.pack(side='left', padx=10)
        
        help_btn = tk.Button(buttons_frame,
                            text="❓ المساعدة",
                            font=('Segoe UI', 9),
                            bg=self.colors['panel'],
                            fg=self.colors['text'],
                            relief='solid',
                            bd=1,
                            padx=15, pady=5,
                            command=self.show_help)
        help_btn.pack(side='left', padx=10)
        
        exit_btn = tk.Button(buttons_frame,
                            text="❌ خروج",
                            font=('Segoe UI', 9),
                            bg=self.colors['panel'],
                            fg=self.colors['text'],
                            relief='solid',
                            bd=1,
                            padx=15, pady=5,
                            command=self.root.quit)
        exit_btn.pack(side='left', padx=10)
        
        # معلومات الإصدار
        version_label = tk.Label(self.root,
                                text="الإصدار 1.0.0 | مفتوح المصدر",
                                font=('Segoe UI', 8),
                                bg=self.colors['bg'],
                                fg=self.colors['text'])
        version_label.pack(side='bottom', pady=10)
    
    def launch_basic(self):
        """تشغيل الإصدار الأساسي"""
        try:
            self.root.withdraw()  # إخفاء نافذة المشغل
            subprocess.run([sys.executable, "main.py"], check=True)
        except subprocess.CalledProcessError:
            messagebox.showerror("خطأ", "فشل في تشغيل الإصدار الأساسي")
        except FileNotFoundError:
            messagebox.showerror("خطأ", "ملف main.py غير موجود")
        finally:
            self.root.deiconify()  # إظهار نافذة المشغل مرة أخرى
    
    def launch_enhanced(self):
        """تشغيل الإصدار المحسن"""
        try:
            self.root.withdraw()  # إخفاء نافذة المشغل
            
            # محاولة تشغيل الإصدار المحسن
            if os.path.exists("main_idm.py"):
                subprocess.run([sys.executable, "main_idm.py"], check=True)
            else:
                # إذا لم يكن متوفراً، تشغيل الإصدار الأساسي
                messagebox.showwarning("تحذير", "الإصدار المحسن غير متوفر، سيتم تشغيل الإصدار الأساسي")
                subprocess.run([sys.executable, "main.py"], check=True)
                
        except subprocess.CalledProcessError:
            messagebox.showerror("خطأ", "فشل في تشغيل البرنامج")
        except FileNotFoundError:
            messagebox.showerror("خطأ", "ملفات البرنامج غير موجودة")
        finally:
            self.root.deiconify()  # إظهار نافذة المشغل مرة أخرى
    
    def run_tests(self):
        """تشغيل الاختبارات"""
        try:
            if os.path.exists("test_download.py"):
                subprocess.run([sys.executable, "test_download.py"], check=True)
            else:
                messagebox.showerror("خطأ", "ملف الاختبارات غير موجود")
        except subprocess.CalledProcessError:
            messagebox.showerror("خطأ", "فشل في تشغيل الاختبارات")
    
    def show_help(self):
        """عرض المساعدة"""
        help_text = """
🔽 مدير التحميل المتقدم

📋 الإصدار الأساسي:
• واجهة بسيطة وسهلة
• جميع الوظائف الأساسية
• استهلاك أقل للموارد

🎨 الإصدار المحسن:
• تصميم شبيه بـ IDM
• مميزات متقدمة
• واجهة احترافية

📁 الملفات:
• main.py - الإصدار الأساسي
• main_idm.py - الإصدار المحسن
• test_download.py - الاختبارات

🔧 المتطلبات:
• Python 3.7+
• tkinter
• requests
• tqdm

📞 للدعم:
راجع ملف README.md أو troubleshooting.md
        """
        
        messagebox.showinfo("المساعدة", help_text)

def main():
    """الدالة الرئيسية"""
    root = tk.Tk()
    app = ProgramLauncher(root)
    root.mainloop()

if __name__ == "__main__":
    main()
