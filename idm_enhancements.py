"""
تحسينات إضافية لجعل الواجهة أكثر شبهاً بـ IDM
Additional enhancements to make the interface more IDM-like
"""

import tkinter as tk
from tkinter import ttk
import os

class IDMEnhancements:
    """فئة التحسينات الإضافية لواجهة IDM"""
    
    def __init__(self, parent_app):
        self.app = parent_app
        self.setup_advanced_features()
    
    def setup_advanced_features(self):
        """إعداد المميزات المتقدمة"""
        self.add_context_menu()
        self.add_keyboard_shortcuts()
        self.add_drag_drop_support()
        self.add_system_tray()
    
    def add_context_menu(self):
        """إضافة قائمة السياق للجدول"""
        self.context_menu = tk.Menu(self.app.root, tearoff=0)
        self.context_menu.add_command(label="🔄 إعادة تحميل", command=self.retry_download)
        self.context_menu.add_command(label="📁 فتح المجلد", command=self.open_folder)
        self.context_menu.add_command(label="📋 نسخ الرابط", command=self.copy_url)
        self.context_menu.add_separator()
        self.context_menu.add_command(label="🗑️ حذف من السجل", command=self.delete_from_history)
        
        # ربط القائمة بالجدول
        self.app.history_tree.bind("<Button-3>", self.show_context_menu)
    
    def show_context_menu(self, event):
        """عرض قائمة السياق"""
        try:
            self.context_menu.tk_popup(event.x_root, event.y_root)
        finally:
            self.context_menu.grab_release()
    
    def add_keyboard_shortcuts(self):
        """إضافة اختصارات لوحة المفاتيح"""
        self.app.root.bind('<Control-n>', lambda e: self.app.show_new_download_dialog())
        self.app.root.bind('<Control-s>', lambda e: self.app.show_settings())
        self.app.root.bind('<F5>', lambda e: self.refresh_downloads())
        self.app.root.bind('<Delete>', lambda e: self.delete_selected())
        self.app.root.bind('<Control-q>', lambda e: self.app.root.quit())
    
    def add_drag_drop_support(self):
        """إضافة دعم السحب والإفلات"""
        # يمكن تطبيق هذا باستخدام مكتبة tkinterdnd2
        pass
    
    def add_system_tray(self):
        """إضافة أيقونة في شريط النظام"""
        # يمكن تطبيق هذا باستخدام مكتبة pystray
        pass
    
    def retry_download(self):
        """إعادة محاولة التحميل"""
        selected = self.app.history_tree.selection()
        if selected:
            # الحصول على معلومات العنصر المحدد
            item = self.app.history_tree.item(selected[0])
            filename = item['values'][1]
            # يمكن إضافة منطق إعادة التحميل هنا
            tk.messagebox.showinfo("إعادة التحميل", f"سيتم إعادة تحميل: {filename}")
    
    def open_folder(self):
        """فتح مجلد الملف"""
        selected = self.app.history_tree.selection()
        if selected:
            # فتح مجلد التحميل الافتراضي
            os.startfile(self.app.default_save_path)
    
    def copy_url(self):
        """نسخ الرابط إلى الحافظة"""
        url = self.app.url_entry.get()
        if url:
            self.app.root.clipboard_clear()
            self.app.root.clipboard_append(url)
            tk.messagebox.showinfo("تم النسخ", "تم نسخ الرابط إلى الحافظة")
    
    def delete_from_history(self):
        """حذف عنصر من السجل"""
        selected = self.app.history_tree.selection()
        if selected:
            if tk.messagebox.askyesno("تأكيد الحذف", "هل تريد حذف هذا العنصر من السجل؟"):
                self.app.history_tree.delete(selected[0])
    
    def refresh_downloads(self):
        """تحديث قائمة التحميلات"""
        self.app.update_history_display()
        tk.messagebox.showinfo("تحديث", "تم تحديث قائمة التحميلات")
    
    def delete_selected(self):
        """حذف العنصر المحدد"""
        self.delete_from_history()

class IDMStatusBar:
    """شريط الحالة السفلي مثل IDM"""
    
    def __init__(self, parent, colors, fonts):
        self.parent = parent
        self.colors = colors
        self.fonts = fonts
        self.create_status_bar()
    
    def create_status_bar(self):
        """إنشاء شريط الحالة"""
        self.status_frame = tk.Frame(self.parent, 
                                    bg=self.colors['bg_toolbar'], 
                                    height=25,
                                    relief='sunken',
                                    bd=1)
        self.status_frame.pack(side=tk.BOTTOM, fill=tk.X)
        self.status_frame.pack_propagate(False)
        
        # معلومات الحالة
        self.status_label = tk.Label(self.status_frame,
                                    text="جاهز",
                                    font=self.fonts['small'],
                                    bg=self.colors['bg_toolbar'],
                                    fg=self.colors['text_secondary'])
        self.status_label.pack(side=tk.LEFT, padx=10, pady=2)
        
        # معلومات الشبكة
        self.network_label = tk.Label(self.status_frame,
                                     text="🌐 متصل",
                                     font=self.fonts['small'],
                                     bg=self.colors['bg_toolbar'],
                                     fg=self.colors['text_secondary'])
        self.network_label.pack(side=tk.RIGHT, padx=10, pady=2)
        
        # عداد التحميلات
        self.downloads_count = tk.Label(self.status_frame,
                                       text="📊 0 تحميل",
                                       font=self.fonts['small'],
                                       bg=self.colors['bg_toolbar'],
                                       fg=self.colors['text_secondary'])
        self.downloads_count.pack(side=tk.RIGHT, padx=10, pady=2)
    
    def update_status(self, text):
        """تحديث نص الحالة"""
        self.status_label.config(text=text)
    
    def update_downloads_count(self, count):
        """تحديث عداد التحميلات"""
        self.downloads_count.config(text=f"📊 {count} تحميل")

class IDMToolbar:
    """شريط أدوات متقدم مثل IDM"""
    
    def __init__(self, parent, colors, fonts, callbacks):
        self.parent = parent
        self.colors = colors
        self.fonts = fonts
        self.callbacks = callbacks
        self.create_advanced_toolbar()
    
    def create_advanced_toolbar(self):
        """إنشاء شريط أدوات متقدم"""
        # شريط الأدوات الرئيسي
        main_toolbar = tk.Frame(self.parent, bg=self.colors['bg_toolbar'], height=40)
        main_toolbar.pack(side=tk.TOP, fill=tk.X)
        main_toolbar.pack_propagate(False)
        
        # مجموعة أزرار الملف
        file_group = tk.Frame(main_toolbar, bg=self.colors['bg_toolbar'])
        file_group.pack(side=tk.LEFT, padx=10, pady=5)
        
        self.create_toolbar_button(file_group, "📥", "تحميل جديد", self.callbacks.get('new_download'))
        self.create_toolbar_button(file_group, "📁", "فتح مجلد", self.callbacks.get('open_folder'))
        
        # فاصل
        separator1 = tk.Frame(main_toolbar, bg=self.colors['border'], width=1)
        separator1.pack(side=tk.LEFT, fill=tk.Y, padx=5, pady=8)
        
        # مجموعة أزرار التحكم
        control_group = tk.Frame(main_toolbar, bg=self.colors['bg_toolbar'])
        control_group.pack(side=tk.LEFT, padx=10, pady=5)
        
        self.create_toolbar_button(control_group, "▶️", "بدء الكل", self.callbacks.get('start_all'))
        self.create_toolbar_button(control_group, "⏸️", "إيقاف الكل", self.callbacks.get('pause_all'))
        self.create_toolbar_button(control_group, "⏹️", "إيقاف الكل", self.callbacks.get('stop_all'))
        
        # فاصل
        separator2 = tk.Frame(main_toolbar, bg=self.colors['border'], width=1)
        separator2.pack(side=tk.LEFT, fill=tk.Y, padx=5, pady=8)
        
        # مجموعة أزرار الأدوات
        tools_group = tk.Frame(main_toolbar, bg=self.colors['bg_toolbar'])
        tools_group.pack(side=tk.LEFT, padx=10, pady=5)
        
        self.create_toolbar_button(tools_group, "⚙️", "إعدادات", self.callbacks.get('settings'))
        self.create_toolbar_button(tools_group, "📊", "إحصائيات", self.callbacks.get('statistics'))
        
        # معلومات السرعة في الجانب الأيمن
        speed_frame = tk.Frame(main_toolbar, bg=self.colors['bg_toolbar'])
        speed_frame.pack(side=tk.RIGHT, padx=15, pady=5)
        
        self.speed_label = tk.Label(speed_frame,
                                   text="🚀 0 KB/s",
                                   font=self.fonts['small'],
                                   bg=self.colors['bg_toolbar'],
                                   fg=self.colors['text_primary'])
        self.speed_label.pack()
    
    def create_toolbar_button(self, parent, icon, tooltip, command):
        """إنشاء زر في شريط الأدوات"""
        btn = tk.Button(parent,
                       text=icon,
                       font=('Segoe UI', 12),
                       bg=self.colors['bg_toolbar'],
                       fg=self.colors['text_primary'],
                       relief='flat',
                       padx=8, pady=4,
                       command=command)
        btn.pack(side=tk.LEFT, padx=2)
        
        # إضافة تأثير hover
        def on_enter(e):
            btn.config(bg=self.colors['bg_panel'])
        
        def on_leave(e):
            btn.config(bg=self.colors['bg_toolbar'])
        
        btn.bind("<Enter>", on_enter)
        btn.bind("<Leave>", on_leave)
        
        return btn
    
    def update_speed(self, speed):
        """تحديث عرض السرعة"""
        self.speed_label.config(text=f"🚀 {speed}")

# دوال مساعدة للتحسينات
def apply_idm_theme(widget, colors):
    """تطبيق ثيم IDM على عنصر"""
    if isinstance(widget, tk.Toplevel) or isinstance(widget, tk.Tk):
        widget.configure(bg=colors['bg_main'])
    elif isinstance(widget, tk.Frame):
        widget.configure(bg=colors['bg_panel'])
    elif isinstance(widget, tk.Label):
        widget.configure(bg=colors['bg_panel'], fg=colors['text_primary'])
    elif isinstance(widget, tk.Button):
        widget.configure(bg=colors['accent'], fg='white', relief='flat')

def create_idm_button(parent, text, command, colors, fonts, style='normal'):
    """إنشاء زر بتصميم IDM"""
    if style == 'primary':
        bg_color = colors['accent']
        fg_color = 'white'
    elif style == 'success':
        bg_color = colors['success']
        fg_color = 'white'
    elif style == 'warning':
        bg_color = colors['warning']
        fg_color = 'white'
    elif style == 'error':
        bg_color = colors['error']
        fg_color = 'white'
    else:
        bg_color = colors['bg_panel']
        fg_color = colors['text_primary']
    
    btn = tk.Button(parent,
                   text=text,
                   font=fonts['button'],
                   bg=bg_color,
                   fg=fg_color,
                   relief='flat',
                   padx=15, pady=8,
                   command=command)
    
    # تأثيرات hover
    def on_enter(e):
        if style == 'primary':
            btn.config(bg=colors['accent_hover'])
        else:
            btn.config(bg=colors['border'])
    
    def on_leave(e):
        btn.config(bg=bg_color)
    
    btn.bind("<Enter>", on_enter)
    btn.bind("<Leave>", on_leave)
    
    return btn
