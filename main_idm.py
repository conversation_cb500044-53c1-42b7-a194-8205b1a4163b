#!/usr/bin/env python3
"""
الإصدار المحسن من مدير التحميل بتصميم IDM
Enhanced version of Download Manager with IDM design
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import os
import sys
from datetime import datetime
import json
import threading

# استيراد الوحدات المحلية
try:
    from downloader import Downloader
    from idm_enhancements import IDMEnhancements, IDMStatusBar, IDMToolbar, create_idm_button
except ImportError as e:
    print(f"خطأ في استيراد الوحدات: {e}")
    sys.exit(1)

class IDMDownloadManager:
    """مدير التحميل بتصميم IDM محسن"""
    
    def __init__(self, root):
        self.root = root
        self.setup_window()
        self.setup_theme()
        self.setup_variables()
        self.setup_ui()
        self.setup_enhancements()
        
    def setup_window(self):
        """إعداد النافذة الرئيسية"""
        self.root.title("Internet Download Manager - مدير التحميل المتقدم")
        self.root.geometry("1200x800")
        self.root.minsize(1000, 600)
        
        # أيقونة النافذة (يمكن إضافة ملف أيقونة لاحقاً)
        try:
            # self.root.iconbitmap('idm_icon.ico')
            pass
        except:
            pass
    
    def setup_theme(self):
        """إعداد الثيم والألوان"""
        self.colors = {
            'bg_main': '#f8f9fa',
            'bg_toolbar': '#e9ecef',
            'bg_panel': '#ffffff',
            'bg_sidebar': '#f1f3f4',
            'border': '#dee2e6',
            'border_light': '#e9ecef',
            'accent': '#0d6efd',
            'accent_hover': '#0b5ed7',
            'accent_light': '#cfe2ff',
            'text_primary': '#212529',
            'text_secondary': '#6c757d',
            'text_muted': '#adb5bd',
            'success': '#198754',
            'success_light': '#d1e7dd',
            'warning': '#fd7e14',
            'warning_light': '#fff3cd',
            'error': '#dc3545',
            'error_light': '#f8d7da',
            'info': '#0dcaf0',
            'info_light': '#d1ecf1'
        }
        
        self.fonts = {
            'title': ('Segoe UI', 12, 'bold'),
            'heading': ('Segoe UI', 10, 'bold'),
            'normal': ('Segoe UI', 9),
            'small': ('Segoe UI', 8),
            'button': ('Segoe UI', 9),
            'monospace': ('Consolas', 9)
        }
        
        self.root.configure(bg=self.colors['bg_main'])
    
    def setup_variables(self):
        """إعداد المتغيرات"""
        self.downloader = Downloader()
        self.download_history = []
        self.active_downloads = []
        self.default_save_path = os.path.expanduser("~/Downloads")
        self.current_download = None
        
        # متغيرات الواجهة
        self.progress_var = tk.DoubleVar()
        self.save_path_var = tk.StringVar(value=self.default_save_path)
        
        # تحميل البيانات المحفوظة
        self.load_history()
        self.load_settings()
        
        # تعيين دوال الاستدعاء
        self.downloader.set_callbacks(
            progress_callback=self.update_progress,
            completion_callback=self.download_completed,
            error_callback=self.download_error
        )
    
    def setup_ui(self):
        """إعداد واجهة المستخدم الرئيسية"""
        # تكوين الشبكة الرئيسية
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(1, weight=1)
        
        # شريط الأدوات العلوي
        self.create_main_toolbar()
        
        # الإطار الرئيسي
        self.main_frame = tk.Frame(self.root, bg=self.colors['bg_main'])
        self.main_frame.grid(row=1, column=0, sticky='nsew', padx=5, pady=5)
        self.main_frame.columnconfigure(1, weight=1)
        self.main_frame.rowconfigure(0, weight=1)
        
        # الشريط الجانبي
        self.create_sidebar()
        
        # المنطقة الرئيسية
        self.create_main_area()
        
        # شريط الحالة السفلي
        self.status_bar = IDMStatusBar(self.root, self.colors, self.fonts)
    
    def create_main_toolbar(self):
        """إنشاء شريط الأدوات الرئيسي"""
        callbacks = {
            'new_download': self.show_new_download_dialog,
            'open_folder': self.open_downloads_folder,
            'start_all': self.start_all_downloads,
            'pause_all': self.pause_all_downloads,
            'stop_all': self.stop_all_downloads,
            'settings': self.show_settings,
            'statistics': self.show_statistics
        }
        
        self.toolbar = IDMToolbar(self.root, self.colors, self.fonts, callbacks)
    
    def create_sidebar(self):
        """إنشاء الشريط الجانبي"""
        sidebar = tk.Frame(self.main_frame, 
                          bg=self.colors['bg_sidebar'], 
                          width=200,
                          relief='solid',
                          bd=1)
        sidebar.grid(row=0, column=0, sticky='ns', padx=(0, 5))
        sidebar.grid_propagate(False)
        
        # عنوان الشريط الجانبي
        title_frame = tk.Frame(sidebar, bg=self.colors['bg_toolbar'], height=40)
        title_frame.pack(fill='x')
        title_frame.pack_propagate(False)
        
        tk.Label(title_frame,
                text="📂 الفئات",
                font=self.fonts['heading'],
                bg=self.colors['bg_toolbar'],
                fg=self.colors['text_primary']).pack(pady=10)
        
        # قائمة الفئات
        categories = [
            ("📥 جميع التحميلات", "all"),
            ("⏳ قيد التحميل", "downloading"),
            ("✅ مكتملة", "completed"),
            ("⏸️ متوقفة", "paused"),
            ("❌ فاشلة", "failed"),
            ("📊 إحصائيات", "stats")
        ]
        
        self.category_buttons = {}
        for text, category in categories:
            btn = tk.Button(sidebar,
                           text=text,
                           font=self.fonts['normal'],
                           bg=self.colors['bg_sidebar'],
                           fg=self.colors['text_primary'],
                           relief='flat',
                           anchor='w',
                           padx=20, pady=8,
                           command=lambda c=category: self.filter_downloads(c))
            btn.pack(fill='x', padx=5, pady=2)
            self.category_buttons[category] = btn
        
        # تحديد الفئة النشطة
        self.active_category = "all"
        self.category_buttons["all"].config(bg=self.colors['accent_light'])
    
    def create_main_area(self):
        """إنشاء المنطقة الرئيسية"""
        main_area = tk.Frame(self.main_frame, bg=self.colors['bg_main'])
        main_area.grid(row=0, column=1, sticky='nsew')
        main_area.columnconfigure(0, weight=1)
        main_area.rowconfigure(1, weight=1)
        
        # لوحة إضافة تحميل جديد
        self.create_new_download_panel(main_area)
        
        # لوحة التحميلات النشطة
        self.create_downloads_list(main_area)
    
    def create_new_download_panel(self, parent):
        """إنشاء لوحة إضافة تحميل جديد"""
        panel = tk.LabelFrame(parent,
                             text=" 📥 إضافة تحميل جديد ",
                             font=self.fonts['heading'],
                             bg=self.colors['bg_panel'],
                             fg=self.colors['text_primary'],
                             relief='solid',
                             bd=1)
        panel.grid(row=0, column=0, sticky='ew', pady=(0, 10))
        panel.columnconfigure(1, weight=1)
        
        # صف الرابط
        tk.Label(panel, text="🔗 الرابط:",
                font=self.fonts['normal'],
                bg=self.colors['bg_panel'],
                fg=self.colors['text_primary']).grid(row=0, column=0, sticky='w', padx=10, pady=8)
        
        self.url_entry = tk.Entry(panel,
                                 font=self.fonts['normal'],
                                 relief='solid', bd=1,
                                 highlightthickness=1,
                                 highlightcolor=self.colors['accent'])
        self.url_entry.grid(row=0, column=1, sticky='ew', padx=10, pady=8)
        
        # صف المجلد
        tk.Label(panel, text="📁 المجلد:",
                font=self.fonts['normal'],
                bg=self.colors['bg_panel'],
                fg=self.colors['text_primary']).grid(row=1, column=0, sticky='w', padx=10, pady=8)
        
        path_frame = tk.Frame(panel, bg=self.colors['bg_panel'])
        path_frame.grid(row=1, column=1, sticky='ew', padx=10, pady=8)
        path_frame.columnconfigure(0, weight=1)
        
        self.save_path_entry = tk.Entry(path_frame,
                                       textvariable=self.save_path_var,
                                       font=self.fonts['normal'],
                                       relief='solid', bd=1,
                                       highlightthickness=1,
                                       highlightcolor=self.colors['accent'])
        self.save_path_entry.grid(row=0, column=0, sticky='ew', padx=(0, 5))
        
        browse_btn = create_idm_button(path_frame, "تصفح", self.browse_folder,
                                      self.colors, self.fonts)
        browse_btn.grid(row=0, column=1)
        
        # صف اسم الملف
        tk.Label(panel, text="📄 اسم الملف:",
                font=self.fonts['normal'],
                bg=self.colors['bg_panel'],
                fg=self.colors['text_primary']).grid(row=2, column=0, sticky='w', padx=10, pady=8)
        
        self.filename_entry = tk.Entry(panel,
                                      font=self.fonts['normal'],
                                      relief='solid', bd=1,
                                      highlightthickness=1,
                                      highlightcolor=self.colors['accent'])
        self.filename_entry.grid(row=2, column=1, sticky='ew', padx=10, pady=8)
        
        # أزرار التحكم
        control_frame = tk.Frame(panel, bg=self.colors['bg_panel'])
        control_frame.grid(row=3, column=0, columnspan=2, pady=15)
        
        self.download_btn = create_idm_button(control_frame, "🚀 بدء التحميل", 
                                             self.start_download, self.colors, self.fonts, 'primary')
        self.download_btn.pack(side='left', padx=5)
        
        self.pause_btn = create_idm_button(control_frame, "⏸️ إيقاف مؤقت", 
                                          self.pause_download, self.colors, self.fonts, 'warning')
        self.pause_btn.pack(side='left', padx=5)
        self.pause_btn.config(state='disabled')
        
        self.resume_btn = create_idm_button(control_frame, "▶️ استئناف", 
                                           self.resume_download, self.colors, self.fonts, 'success')
        self.resume_btn.pack(side='left', padx=5)
        self.resume_btn.config(state='disabled')
        
        self.stop_btn = create_idm_button(control_frame, "⏹️ إيقاف", 
                                         self.stop_download, self.colors, self.fonts, 'error')
        self.stop_btn.pack(side='left', padx=5)
        self.stop_btn.config(state='disabled')
    
    def create_downloads_list(self, parent):
        """إنشاء قائمة التحميلات"""
        list_frame = tk.LabelFrame(parent,
                                  text=" 📋 قائمة التحميلات ",
                                  font=self.fonts['heading'],
                                  bg=self.colors['bg_panel'],
                                  fg=self.colors['text_primary'],
                                  relief='solid',
                                  bd=1)
        list_frame.grid(row=1, column=0, sticky='nsew')
        list_frame.columnconfigure(0, weight=1)
        list_frame.rowconfigure(1, weight=1)
        
        # شريط التقدم الحالي
        self.create_current_progress(list_frame)
        
        # جدول التحميلات
        self.create_downloads_table(list_frame)
    
    def create_current_progress(self, parent):
        """إنشاء شريط التقدم الحالي"""
        progress_frame = tk.Frame(parent, bg=self.colors['bg_panel'])
        progress_frame.grid(row=0, column=0, sticky='ew', padx=10, pady=10)
        progress_frame.columnconfigure(0, weight=1)
        
        # معلومات الملف الحالي
        self.current_file_label = tk.Label(progress_frame,
                                          text="لا يوجد تحميل نشط",
                                          font=self.fonts['normal'],
                                          bg=self.colors['bg_panel'],
                                          fg=self.colors['text_primary'])
        self.current_file_label.grid(row=0, column=0, sticky='w', pady=2)
        
        # شريط التقدم
        self.progress_bar = ttk.Progressbar(progress_frame,
                                           variable=self.progress_var,
                                           maximum=100,
                                           style='TProgressbar')
        self.progress_bar.grid(row=1, column=0, sticky='ew', pady=5)
        
        # تفاصيل التقدم
        details_frame = tk.Frame(progress_frame, bg=self.colors['bg_panel'])
        details_frame.grid(row=2, column=0, sticky='ew', pady=2)
        details_frame.columnconfigure(1, weight=1)
        
        self.status_label = tk.Label(details_frame,
                                    text="جاهز للتحميل",
                                    font=self.fonts['small'],
                                    bg=self.colors['bg_panel'],
                                    fg=self.colors['text_secondary'])
        self.status_label.grid(row=0, column=0, sticky='w')
        
        self.speed_label = tk.Label(details_frame,
                                   text="",
                                   font=self.fonts['small'],
                                   bg=self.colors['bg_panel'],
                                   fg=self.colors['text_secondary'])
        self.speed_label.grid(row=0, column=1, sticky='e')
        
        self.details_label = tk.Label(progress_frame,
                                     text="",
                                     font=self.fonts['small'],
                                     bg=self.colors['bg_panel'],
                                     fg=self.colors['text_secondary'])
        self.details_label.grid(row=3, column=0, sticky='w', pady=2)
    
    def create_downloads_table(self, parent):
        """إنشاء جدول التحميلات"""
        table_frame = tk.Frame(parent, bg=self.colors['bg_panel'])
        table_frame.grid(row=1, column=0, sticky='nsew', padx=10, pady=(0, 10))
        table_frame.columnconfigure(0, weight=1)
        table_frame.rowconfigure(0, weight=1)
        
        # جدول التحميلات
        columns = ('الوقت', 'اسم الملف', 'الحجم', 'السرعة', 'التقدم', 'الحالة')
        self.downloads_tree = ttk.Treeview(table_frame,
                                          columns=columns,
                                          show='headings',
                                          height=15)
        
        # تكوين الأعمدة
        column_widths = {
            'الوقت': 120,
            'اسم الملف': 300,
            'الحجم': 100,
            'السرعة': 100,
            'التقدم': 100,
            'الحالة': 100
        }
        
        for col in columns:
            self.downloads_tree.heading(col, text=col)
            self.downloads_tree.column(col, width=column_widths.get(col, 100), minwidth=80)
        
        # أشرطة التمرير
        v_scrollbar = ttk.Scrollbar(table_frame, orient='vertical', command=self.downloads_tree.yview)
        h_scrollbar = ttk.Scrollbar(table_frame, orient='horizontal', command=self.downloads_tree.xview)
        
        self.downloads_tree.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)
        
        # ترتيب العناصر
        self.downloads_tree.grid(row=0, column=0, sticky='nsew')
        v_scrollbar.grid(row=0, column=1, sticky='ns')
        h_scrollbar.grid(row=1, column=0, sticky='ew')
        
        # تحديث البيانات
        self.update_downloads_display()
    
    def setup_enhancements(self):
        """إعداد التحسينات الإضافية"""
        self.enhancements = IDMEnhancements(self)
    
    # باقي الدوال ستكون مشابهة للملف الأصلي مع تحسينات إضافية
    def start_download(self):
        """بدء التحميل"""
        # نفس المنطق مع تحسينات إضافية
        pass
    
    def update_progress(self, progress_data):
        """تحديث التقدم"""
        # نفس المنطق مع تحسينات إضافية
        pass
    
    # دوال إضافية للمميزات الجديدة
    def show_new_download_dialog(self):
        """عرض نافذة تحميل جديد"""
        pass
    
    def filter_downloads(self, category):
        """تصفية التحميلات حسب الفئة"""
        pass
    
    def load_settings(self):
        """تحميل الإعدادات"""
        pass
    
    def save_settings(self):
        """حفظ الإعدادات"""
        pass
    
    # باقي الدوال...
    def browse_folder(self):
        folder = filedialog.askdirectory(initialdir=self.save_path_var.get())
        if folder:
            self.save_path_var.set(folder)
    
    def load_history(self):
        try:
            with open('download_history.json', 'r', encoding='utf-8') as f:
                self.download_history = json.load(f)
        except:
            self.download_history = []
    
    def update_downloads_display(self):
        """تحديث عرض التحميلات"""
        pass

def main():
    """الدالة الرئيسية"""
    root = tk.Tk()
    app = IDMDownloadManager(root)
    root.mainloop()

if __name__ == "__main__":
    main()
