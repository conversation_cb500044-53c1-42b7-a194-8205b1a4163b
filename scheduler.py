"""
مجدول التحميلات المتقدم - Advanced Download Scheduler
يدير جدولة التحميلات حسب الوقت والشروط المختلفة
"""

import threading
import time
import json
import os
from datetime import datetime, timedelta
from enum import Enum
from download_manager import MultiDownloadManager, DownloadPriority

class ScheduleType(Enum):
    """أنواع الجدولة"""
    IMMEDIATE = "فوري"
    DELAYED = "مؤجل"
    SCHEDULED = "مجدول"
    RECURRING = "متكرر"
    CONDITIONAL = "مشروط"

class ScheduleCondition(Enum):
    """شروط الجدولة"""
    TIME_BASED = "حسب الوقت"
    BANDWIDTH_BASED = "حسب عرض النطاق"
    SYSTEM_IDLE = "عند خمول النظام"
    NETWORK_AVAILABLE = "عند توفر الشبكة"
    DISK_SPACE = "حسب مساحة القرص"

class ScheduledDownload:
    """تحميل مجدول"""
    
    def __init__(self, url, save_path, filename=None, priority=DownloadPriority.NORMAL):
        self.id = self.generate_id()
        self.url = url
        self.save_path = save_path
        self.filename = filename
        self.priority = priority
        
        # إعدادات الجدولة
        self.schedule_type = ScheduleType.IMMEDIATE
        self.scheduled_time = None
        self.conditions = []
        self.recurring_pattern = None
        self.max_attempts = 3
        self.attempt_count = 0
        
        # حالة الجدولة
        self.is_active = True
        self.last_attempt = None
        self.next_attempt = None
        self.created_at = datetime.now()
        
        # إعدادات متقدمة
        self.bandwidth_limit = None  # KB/s
        self.time_restrictions = []  # قائمة بالأوقات المسموحة
        self.retry_delay = 300  # 5 دقائق
        
    def generate_id(self):
        """توليد معرف فريد"""
        import hashlib
        timestamp = str(int(time.time() * 1000))
        return f"sched_{hashlib.md5(timestamp.encode()).hexdigest()[:8]}"
    
    def set_delayed_start(self, delay_minutes):
        """تعيين بداية مؤجلة"""
        self.schedule_type = ScheduleType.DELAYED
        self.scheduled_time = datetime.now() + timedelta(minutes=delay_minutes)
        self.next_attempt = self.scheduled_time
    
    def set_scheduled_time(self, scheduled_datetime):
        """تعيين وقت محدد للبداية"""
        self.schedule_type = ScheduleType.SCHEDULED
        self.scheduled_time = scheduled_datetime
        self.next_attempt = scheduled_datetime
    
    def set_recurring(self, pattern_minutes):
        """تعيين تكرار كل فترة معينة"""
        self.schedule_type = ScheduleType.RECURRING
        self.recurring_pattern = pattern_minutes
        if not self.scheduled_time:
            self.scheduled_time = datetime.now()
        self.next_attempt = self.scheduled_time
    
    def add_condition(self, condition, parameters=None):
        """إضافة شرط للجدولة"""
        self.conditions.append({
            'condition': condition,
            'parameters': parameters or {}
        })
        if self.schedule_type == ScheduleType.IMMEDIATE:
            self.schedule_type = ScheduleType.CONDITIONAL
    
    def set_time_restrictions(self, allowed_hours):
        """تعيين الساعات المسموحة للتحميل"""
        self.time_restrictions = allowed_hours
    
    def is_time_allowed(self):
        """التحقق من السماح بالتحميل في الوقت الحالي"""
        if not self.time_restrictions:
            return True
        
        current_hour = datetime.now().hour
        return current_hour in self.time_restrictions
    
    def should_start_now(self):
        """التحقق من إمكانية البداية الآن"""
        now = datetime.now()
        
        # التحقق من النشاط
        if not self.is_active:
            return False
        
        # التحقق من عدد المحاولات
        if self.attempt_count >= self.max_attempts:
            return False
        
        # التحقق من الوقت المجدول
        if self.next_attempt and now < self.next_attempt:
            return False
        
        # التحقق من قيود الوقت
        if not self.is_time_allowed():
            return False
        
        return True
    
    def update_next_attempt(self):
        """تحديث موعد المحاولة التالية"""
        if self.schedule_type == ScheduleType.RECURRING and self.recurring_pattern:
            self.next_attempt = datetime.now() + timedelta(minutes=self.recurring_pattern)
        elif self.attempt_count < self.max_attempts:
            # إعادة المحاولة بعد فترة
            self.next_attempt = datetime.now() + timedelta(seconds=self.retry_delay)
        else:
            self.next_attempt = None
    
    def to_dict(self):
        """تحويل إلى قاموس"""
        return {
            'id': self.id,
            'url': self.url,
            'save_path': self.save_path,
            'filename': self.filename,
            'priority': self.priority.value,
            'schedule_type': self.schedule_type.value,
            'scheduled_time': self.scheduled_time.isoformat() if self.scheduled_time else None,
            'conditions': self.conditions,
            'recurring_pattern': self.recurring_pattern,
            'max_attempts': self.max_attempts,
            'attempt_count': self.attempt_count,
            'is_active': self.is_active,
            'last_attempt': self.last_attempt.isoformat() if self.last_attempt else None,
            'next_attempt': self.next_attempt.isoformat() if self.next_attempt else None,
            'created_at': self.created_at.isoformat(),
            'bandwidth_limit': self.bandwidth_limit,
            'time_restrictions': self.time_restrictions,
            'retry_delay': self.retry_delay
        }

class DownloadScheduler:
    """مجدول التحميلات المتقدم"""
    
    def __init__(self, download_manager=None):
        self.download_manager = download_manager or MultiDownloadManager()
        self.scheduled_downloads = {}
        self.scheduler_thread = None
        self.is_running = False
        
        # إعدادات النظام
        self.check_interval = 30  # ثانية
        self.max_bandwidth = None  # KB/s
        self.current_bandwidth_usage = 0
        
        # ملف الحفظ
        self.schedule_file = "download_schedule.json"
        
        # دوال الاستدعاء
        self.schedule_callback = None
        self.condition_callback = None
        self.error_callback = None
        
        # تحميل الجدولة المحفوظة
        self.load_schedule()
    
    def set_callbacks(self, schedule_callback=None, condition_callback=None, error_callback=None):
        """تعيين دوال الاستدعاء"""
        self.schedule_callback = schedule_callback
        self.condition_callback = condition_callback
        self.error_callback = error_callback
    
    def add_scheduled_download(self, url, save_path, filename=None, priority=DownloadPriority.NORMAL):
        """إضافة تحميل مجدول"""
        scheduled_download = ScheduledDownload(url, save_path, filename, priority)
        self.scheduled_downloads[scheduled_download.id] = scheduled_download
        
        if self.schedule_callback:
            self.schedule_callback('added', scheduled_download.id)
        
        self.save_schedule()
        return scheduled_download.id
    
    def schedule_immediate(self, download_id):
        """جدولة فورية"""
        if download_id in self.scheduled_downloads:
            download = self.scheduled_downloads[download_id]
            download.schedule_type = ScheduleType.IMMEDIATE
            download.next_attempt = datetime.now()
            self.save_schedule()
            return True
        return False
    
    def schedule_delayed(self, download_id, delay_minutes):
        """جدولة مؤجلة"""
        if download_id in self.scheduled_downloads:
            download = self.scheduled_downloads[download_id]
            download.set_delayed_start(delay_minutes)
            self.save_schedule()
            return True
        return False
    
    def schedule_at_time(self, download_id, scheduled_datetime):
        """جدولة في وقت محدد"""
        if download_id in self.scheduled_downloads:
            download = self.scheduled_downloads[download_id]
            download.set_scheduled_time(scheduled_datetime)
            self.save_schedule()
            return True
        return False
    
    def schedule_recurring(self, download_id, interval_minutes):
        """جدولة متكررة"""
        if download_id in self.scheduled_downloads:
            download = self.scheduled_downloads[download_id]
            download.set_recurring(interval_minutes)
            self.save_schedule()
            return True
        return False
    
    def add_condition(self, download_id, condition, parameters=None):
        """إضافة شرط للتحميل"""
        if download_id in self.scheduled_downloads:
            download = self.scheduled_downloads[download_id]
            download.add_condition(condition, parameters)
            self.save_schedule()
            return True
        return False
    
    def set_bandwidth_limit(self, download_id, limit_kbps):
        """تعيين حد عرض النطاق"""
        if download_id in self.scheduled_downloads:
            download = self.scheduled_downloads[download_id]
            download.bandwidth_limit = limit_kbps
            self.save_schedule()
            return True
        return False
    
    def set_time_restrictions(self, download_id, allowed_hours):
        """تعيين قيود الوقت"""
        if download_id in self.scheduled_downloads:
            download = self.scheduled_downloads[download_id]
            download.set_time_restrictions(allowed_hours)
            self.save_schedule()
            return True
        return False
    
    def start_scheduler(self):
        """بدء المجدول"""
        if self.is_running:
            return
        
        self.is_running = True
        self.scheduler_thread = threading.Thread(target=self._scheduler_loop, daemon=True)
        self.scheduler_thread.start()
    
    def stop_scheduler(self):
        """إيقاف المجدول"""
        self.is_running = False
    
    def _scheduler_loop(self):
        """حلقة المجدول الرئيسية"""
        while self.is_running:
            try:
                self._check_scheduled_downloads()
                self._update_bandwidth_usage()
                time.sleep(self.check_interval)
            except Exception as e:
                if self.error_callback:
                    self.error_callback(f"خطأ في المجدول: {str(e)}")
                time.sleep(60)  # انتظار أطول عند الخطأ
    
    def _check_scheduled_downloads(self):
        """فحص التحميلات المجدولة"""
        for download_id, download in list(self.scheduled_downloads.items()):
            if download.should_start_now():
                if self._check_conditions(download):
                    self._start_scheduled_download(download)
    
    def _check_conditions(self, download):
        """فحص شروط التحميل"""
        for condition_data in download.conditions:
            condition = condition_data['condition']
            parameters = condition_data['parameters']
            
            if condition == ScheduleCondition.BANDWIDTH_BASED:
                max_bandwidth = parameters.get('max_bandwidth', self.max_bandwidth)
                if max_bandwidth and self.current_bandwidth_usage >= max_bandwidth:
                    return False
            
            elif condition == ScheduleCondition.DISK_SPACE:
                min_space_gb = parameters.get('min_space_gb', 1)
                if not self._check_disk_space(download.save_path, min_space_gb):
                    return False
            
            elif condition == ScheduleCondition.NETWORK_AVAILABLE:
                if not self._check_network_connectivity():
                    return False
            
            elif condition == ScheduleCondition.SYSTEM_IDLE:
                if not self._check_system_idle():
                    return False
            
            # يمكن إضافة شروط أخرى هنا
        
        return True
    
    def _start_scheduled_download(self, download):
        """بدء تحميل مجدول"""
        try:
            # إضافة التحميل إلى مدير التحميلات
            download_id = self.download_manager.add_download(
                download.url,
                download.save_path,
                download.filename,
                download.priority
            )
            
            # تحديث معلومات المحاولة
            download.attempt_count += 1
            download.last_attempt = datetime.now()
            download.update_next_attempt()
            
            if self.schedule_callback:
                self.schedule_callback('started', download.id, download_id)
            
            # إزالة من الجدولة إذا لم تعد متكررة
            if download.schedule_type != ScheduleType.RECURRING:
                if download.attempt_count >= download.max_attempts:
                    download.is_active = False
            
            self.save_schedule()
            
        except Exception as e:
            if self.error_callback:
                self.error_callback(f"فشل في بدء التحميل المجدول {download.id}: {str(e)}")
    
    def _update_bandwidth_usage(self):
        """تحديث استخدام عرض النطاق"""
        # هذه دالة مبسطة، يمكن تحسينها لقراءة الاستخدام الفعلي
        active_downloads = self.download_manager.get_downloads_by_status("جاري التحميل")
        self.current_bandwidth_usage = len(active_downloads) * 100  # تقدير مبسط
    
    def _check_disk_space(self, path, min_space_gb):
        """فحص مساحة القرص"""
        try:
            import shutil
            free_space = shutil.disk_usage(path).free
            free_space_gb = free_space / (1024**3)
            return free_space_gb >= min_space_gb
        except:
            return True  # افتراض وجود مساحة كافية عند الخطأ
    
    def _check_network_connectivity(self):
        """فحص الاتصال بالشبكة"""
        try:
            import socket
            socket.create_connection(("8.8.8.8", 53), timeout=3)
            return True
        except:
            return False
    
    def _check_system_idle(self):
        """فحص خمول النظام"""
        # هذه دالة مبسطة، يمكن تحسينها حسب نظام التشغيل
        try:
            import psutil
            cpu_percent = psutil.cpu_percent(interval=1)
            return cpu_percent < 20  # أقل من 20% استخدام
        except:
            return True  # افتراض الخمول عند عدم توفر المعلومات
    
    def get_scheduled_downloads(self):
        """الحصول على جميع التحميلات المجدولة"""
        return [download.to_dict() for download in self.scheduled_downloads.values()]
    
    def get_next_scheduled(self, count=5):
        """الحصول على التحميلات المجدولة التالية"""
        active_downloads = [d for d in self.scheduled_downloads.values() 
                          if d.is_active and d.next_attempt]
        
        # ترتيب حسب الوقت
        active_downloads.sort(key=lambda x: x.next_attempt)
        
        return [download.to_dict() for download in active_downloads[:count]]
    
    def cancel_scheduled_download(self, download_id):
        """إلغاء تحميل مجدول"""
        if download_id in self.scheduled_downloads:
            self.scheduled_downloads[download_id].is_active = False
            self.save_schedule()
            
            if self.schedule_callback:
                self.schedule_callback('cancelled', download_id)
            
            return True
        return False
    
    def remove_scheduled_download(self, download_id):
        """حذف تحميل مجدول"""
        if download_id in self.scheduled_downloads:
            del self.scheduled_downloads[download_id]
            self.save_schedule()
            
            if self.schedule_callback:
                self.schedule_callback('removed', download_id)
            
            return True
        return False
    
    def save_schedule(self):
        """حفظ الجدولة"""
        try:
            schedule_data = {
                'scheduled_downloads': [download.to_dict() for download in self.scheduled_downloads.values()],
                'settings': {
                    'check_interval': self.check_interval,
                    'max_bandwidth': self.max_bandwidth
                },
                'saved_at': datetime.now().isoformat()
            }
            
            with open(self.schedule_file, 'w', encoding='utf-8') as f:
                json.dump(schedule_data, f, ensure_ascii=False, indent=2)
                
        except Exception as e:
            if self.error_callback:
                self.error_callback(f"فشل في حفظ الجدولة: {str(e)}")
    
    def load_schedule(self):
        """تحميل الجدولة المحفوظة"""
        try:
            if os.path.exists(self.schedule_file):
                with open(self.schedule_file, 'r', encoding='utf-8') as f:
                    schedule_data = json.load(f)
                
                # استرجاع التحميلات المجدولة
                for download_data in schedule_data.get('scheduled_downloads', []):
                    if download_data['is_active']:
                        download = ScheduledDownload(
                            download_data['url'],
                            download_data['save_path'],
                            download_data['filename'],
                            DownloadPriority(download_data['priority'])
                        )
                        
                        # استرجاع البيانات
                        download.id = download_data['id']
                        download.schedule_type = ScheduleType(download_data['schedule_type'])
                        download.conditions = download_data.get('conditions', [])
                        download.recurring_pattern = download_data.get('recurring_pattern')
                        download.max_attempts = download_data.get('max_attempts', 3)
                        download.attempt_count = download_data.get('attempt_count', 0)
                        download.bandwidth_limit = download_data.get('bandwidth_limit')
                        download.time_restrictions = download_data.get('time_restrictions', [])
                        download.retry_delay = download_data.get('retry_delay', 300)
                        
                        # استرجاع التواريخ
                        if download_data.get('scheduled_time'):
                            download.scheduled_time = datetime.fromisoformat(download_data['scheduled_time'])
                        if download_data.get('next_attempt'):
                            download.next_attempt = datetime.fromisoformat(download_data['next_attempt'])
                        if download_data.get('last_attempt'):
                            download.last_attempt = datetime.fromisoformat(download_data['last_attempt'])
                        
                        self.scheduled_downloads[download.id] = download
                
                # استرجاع الإعدادات
                settings = schedule_data.get('settings', {})
                self.check_interval = settings.get('check_interval', self.check_interval)
                self.max_bandwidth = settings.get('max_bandwidth', self.max_bandwidth)
                
        except Exception as e:
            if self.error_callback:
                self.error_callback(f"فشل في تحميل الجدولة: {str(e)}")
    
    def get_statistics(self):
        """الحصول على إحصائيات الجدولة"""
        total_scheduled = len(self.scheduled_downloads)
        active_scheduled = len([d for d in self.scheduled_downloads.values() if d.is_active])
        next_download = min([d.next_attempt for d in self.scheduled_downloads.values() 
                           if d.is_active and d.next_attempt], default=None)
        
        return {
            'total_scheduled': total_scheduled,
            'active_scheduled': active_scheduled,
            'inactive_scheduled': total_scheduled - active_scheduled,
            'next_download': next_download.isoformat() if next_download else None,
            'current_bandwidth_usage': self.current_bandwidth_usage,
            'max_bandwidth': self.max_bandwidth,
            'check_interval': self.check_interval
        }
