import tkinter as tk
from tkinter import ttk, filedialog, messagebox, font, E, W, N, S
import os
import time
import json
from datetime import datetime
from downloader import Downloader, AdvancedDownloader
from download_manager import MultiDownloadManager, DownloadPriority, DownloadStatus
from scheduler import DownloadScheduler
from theme_manager import theme_manager
from language_manager import language_manager
from delete_manager import delete_manager
from notification_system import notification_system
from analytics_system import analytics_system
from smart_accelerator import smart_accelerator
from backup_system import backup_system
import threading
import json
from datetime import datetime, timedelta

class DownloadManagerGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("Internet Download Manager - مدير التحميل")
        self.root.geometry("1000x700")
        self.root.minsize(800, 600)

        # تحميل تفضيلات اللغة والثيم
        language_manager.load_language_preference()
        theme_manager.load_theme_preference()

        # تعيين عنوان النافذة حسب اللغة
        app_title = language_manager.get_text("app_title")
        self.root.title(app_title)

        # الحصول على الألوان من مدير الثيم
        self.colors = theme_manager.get_current_theme()

        # خطوط IDM
        self.fonts = {
            'title': ('Segoe UI', 11, 'bold'),
            'normal': ('Segoe UI', 9),
            'small': ('Segoe UI', 8),
            'button': ('Segoe UI', 9, 'bold')
        }

        self.root.configure(bg=self.colors['bg_main'])

        # إعداد المتغيرات المحسنة
        self.downloader = AdvancedDownloader()
        self.download_manager = MultiDownloadManager(max_concurrent=3, auto_start=True)
        self.scheduler = DownloadScheduler(self.download_manager)
        self.download_history = []
        self.default_save_path = os.path.expanduser("~/Downloads")
        self.current_download = None
        self.active_downloads = {}

        # إعدادات متقدمة
        self.max_connections = 4
        self.auto_retry = True
        self.bandwidth_limit = None
        self.download_notifications = True

        # تحميل السجل والإعدادات
        self.load_history()
        self.load_settings()

        # إعداد الأيقونات والستايل
        self.setup_style()

        # إعداد واجهة المستخدم
        self.setup_ui()

        # تعيين دوال الاستدعاء المحسنة
        self.setup_callbacks()

        # إعداد الإشعارات المحسنة
        self.setup_enhanced_notifications()

        # بدء الخدمات
        self.download_manager.start_manager()
        self.scheduler.start_scheduler()

    def setup_style(self):
        """إعداد الستايل والثيم"""
        style = ttk.Style()

        # تكوين الستايل العام
        style.configure('Title.TLabel',
                       font=self.fonts['title'],
                       foreground=self.colors['text_primary'],
                       background=self.colors['bg_main'])

        style.configure('Heading.TLabel',
                       font=self.fonts['normal'],
                       foreground=self.colors['text_primary'],
                       background=self.colors['bg_panel'])

        style.configure('Custom.TButton',
                       font=self.fonts['button'],
                       focuscolor='none')

        style.configure('Accent.TButton',
                       font=self.fonts['button'],
                       focuscolor='none')

        # ستايل شريط التقدم
        style.configure('Custom.Horizontal.TProgressbar',
                       background=self.colors['accent'],
                       troughcolor=self.colors['border'],
                       borderwidth=1,
                       lightcolor=self.colors['accent'],
                       darkcolor=self.colors['accent'])

    def setup_callbacks(self):
        """إعداد دوال الاستدعاء المحسنة"""
        # دوال استدعاء التحميل الأساسي
        self.downloader.set_callbacks(
            progress_callback=self.update_progress,
            completion_callback=self.download_completed,
            error_callback=self.download_error
        )

        # دوال استدعاء مدير التحميلات المتعددة
        self.download_manager.set_callbacks(
            progress_callback=self.on_multi_progress,
            status_change_callback=self.on_status_change,
            completion_callback=self.on_multi_completion,
            error_callback=self.on_multi_error
        )

        # دوال استدعاء المجدول
        self.scheduler.set_callbacks(
            schedule_callback=self.on_schedule_event,
            error_callback=self.on_scheduler_error
        )

    def load_settings(self):
        """تحميل الإعدادات المحفوظة"""
        try:
            with open('settings.json', 'r', encoding='utf-8') as f:
                settings = json.load(f)

            self.max_connections = settings.get('max_connections', 4)
            self.auto_retry = settings.get('auto_retry', True)
            self.bandwidth_limit = settings.get('bandwidth_limit')
            self.download_notifications = settings.get('download_notifications', True)
            self.default_save_path = settings.get('default_save_path', self.default_save_path)

            # تطبيق الإعدادات على المحمل
            self.downloader.set_advanced_options(
                max_connections=self.max_connections,
                max_retries=3 if self.auto_retry else 1
            )

        except FileNotFoundError:
            self.save_settings()  # إنشاء ملف إعدادات افتراضي
        except Exception as e:
            print(f"خطأ في تحميل الإعدادات: {e}")

    def save_settings(self):
        """حفظ الإعدادات"""
        try:
            settings = {
                'max_connections': self.max_connections,
                'auto_retry': self.auto_retry,
                'bandwidth_limit': self.bandwidth_limit,
                'download_notifications': self.download_notifications,
                'default_save_path': self.default_save_path,
                'saved_at': datetime.now().isoformat()
            }

            with open('settings.json', 'w', encoding='utf-8') as f:
                json.dump(settings, f, ensure_ascii=False, indent=2)

        except Exception as e:
            print(f"خطأ في حفظ الإعدادات: {e}")

    def setup_ui(self):
        """إعداد واجهة المستخدم بتصميم IDM"""
        # تكوين الشبكة الرئيسية
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(1, weight=1)

        # شريط الأدوات العلوي
        self.create_toolbar()

        # الإطار الرئيسي
        main_container = tk.Frame(self.root, bg=self.colors['bg_main'])
        main_container.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), padx=5, pady=5)
        main_container.columnconfigure(0, weight=1)
        main_container.rowconfigure(1, weight=1)

        # لوحة إدخال التحميل
        self.create_download_panel(main_container)

        # لوحة التحميلات النشطة والسجل
        self.create_downloads_panel(main_container)

    def create_toolbar(self):
        """إنشاء شريط الأدوات العلوي"""
        toolbar = tk.Frame(self.root, bg=self.colors['bg_toolbar'], height=50)
        toolbar.grid(row=0, column=0, sticky=(tk.W, tk.E), padx=0, pady=0)
        toolbar.grid_propagate(False)

        # عنوان البرنامج مع أيقونة
        title_frame = tk.Frame(toolbar, bg=self.colors['bg_toolbar'])
        title_frame.pack(side=tk.LEFT, padx=15, pady=10)

        title_label = tk.Label(title_frame,
                              text="🔽 Internet Download Manager",
                              font=self.fonts['title'],
                              fg=self.colors['text_primary'],
                              bg=self.colors['bg_toolbar'])
        title_label.pack(side=tk.LEFT)

        # أزرار شريط الأدوات
        buttons_frame = tk.Frame(toolbar, bg=self.colors['bg_toolbar'])
        buttons_frame.pack(side=tk.RIGHT, padx=15, pady=8)

        # زر إضافة تحميل جديد
        self.new_download_btn = tk.Button(buttons_frame,
                                         text="📥 تحميل جديد",
                                         font=self.fonts['button'],
                                         bg=self.colors['accent'],
                                         fg='white',
                                         relief='flat',
                                         padx=15, pady=5,
                                         command=self.show_new_download_dialog)
        self.new_download_btn.pack(side=tk.LEFT, padx=5)

        # زر الإعدادات
        settings_btn = tk.Button(buttons_frame,
                                text="⚙️ " + language_manager.get_text("settings"),
                                font=self.fonts['button'],
                                bg=self.colors['bg_panel'],
                                fg=self.colors['text_primary'],
                                relief='flat',
                                padx=15, pady=5,
                                command=self.show_settings)
        settings_btn.pack(side=tk.LEFT, padx=5)

        # قائمة الثيم
        theme_btn = tk.Button(buttons_frame,
                             text="🎨 " + language_manager.get_text("theme"),
                             font=self.fonts['button'],
                             bg=self.colors['bg_panel'],
                             fg=self.colors['text_primary'],
                             relief='flat',
                             padx=15, pady=5,
                             command=self.show_theme_menu)
        theme_btn.pack(side=tk.LEFT, padx=5)

        # قائمة اللغة
        language_btn = tk.Button(buttons_frame,
                                text="🌐 " + language_manager.get_text("language"),
                                font=self.fonts['button'],
                                bg=self.colors['bg_panel'],
                                fg=self.colors['text_primary'],
                                relief='flat',
                                padx=15, pady=5,
                                command=self.show_language_menu)
        language_btn.pack(side=tk.LEFT, padx=5)

        # زر سلة المحذوفات
        trash_btn = tk.Button(buttons_frame,
                             text="🗑️ " + language_manager.get_text("trash", "سلة المحذوفات"),
                             font=self.fonts['button'],
                             bg=self.colors['bg_panel'],
                             fg=self.colors['text_primary'],
                             relief='flat',
                             padx=15, pady=5,
                             command=self.show_trash_manager)
        trash_btn.pack(side=tk.LEFT, padx=5)

        # زر الإحصائيات
        analytics_btn = tk.Button(buttons_frame,
                                 text="📊 " + language_manager.get_text("analytics", "إحصائيات"),
                                 font=self.fonts['button'],
                                 bg=self.colors['bg_panel'],
                                 fg=self.colors['text_primary'],
                                 relief='flat',
                                 padx=15, pady=5,
                                 command=self.show_analytics_dashboard)
        analytics_btn.pack(side=tk.LEFT, padx=5)

        # زر النسخ الاحتياطي
        backup_btn = tk.Button(buttons_frame,
                              text="💾 " + language_manager.get_text("backup", "نسخ احتياطي"),
                              font=self.fonts['button'],
                              bg=self.colors['bg_panel'],
                              fg=self.colors['text_primary'],
                              relief='flat',
                              padx=15, pady=5,
                              command=self.show_backup_manager)
        backup_btn.pack(side=tk.LEFT, padx=5)

        # زر التسريع الذكي
        accelerator_btn = tk.Button(buttons_frame,
                                   text="⚡ " + language_manager.get_text("smart_accelerator", "تسريع ذكي"),
                                   font=self.fonts['button'],
                                   bg=self.colors['bg_panel'],
                                   fg=self.colors['text_primary'],
                                   relief='flat',
                                   padx=15, pady=5,
                                   command=self.show_accelerator_settings)
        accelerator_btn.pack(side=tk.LEFT, padx=5)

    def create_download_panel(self, parent):
        """إنشاء لوحة إدخال التحميل"""
        # إطار لوحة التحميل
        download_frame = tk.LabelFrame(parent,
                                      text=" إضافة تحميل جديد ",
                                      font=self.fonts['normal'],
                                      fg=self.colors['text_primary'],
                                      bg=self.colors['bg_panel'],
                                      relief='solid',
                                      bd=1)
        download_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        download_frame.columnconfigure(1, weight=1)

        # إدخال الرابط
        tk.Label(download_frame, text="🔗 رابط التحميل:",
                font=self.fonts['normal'],
                fg=self.colors['text_primary'],
                bg=self.colors['bg_panel']).grid(row=0, column=0, sticky=tk.W, padx=10, pady=8)

        self.url_entry = tk.Entry(download_frame,
                                 font=self.fonts['normal'],
                                 relief='solid', bd=1,
                                 highlightthickness=1,
                                 highlightcolor=self.colors['accent'])
        self.url_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=10, pady=8)

        # اختيار مجلد الحفظ
        tk.Label(download_frame, text="📁 مجلد الحفظ:",
                font=self.fonts['normal'],
                fg=self.colors['text_primary'],
                bg=self.colors['bg_panel']).grid(row=1, column=0, sticky=tk.W, padx=10, pady=8)

        path_frame = tk.Frame(download_frame, bg=self.colors['bg_panel'])
        path_frame.grid(row=1, column=1, sticky=(tk.W, tk.E), padx=10, pady=8)
        path_frame.columnconfigure(0, weight=1)

        self.save_path_var = tk.StringVar(value=self.default_save_path)
        self.save_path_entry = tk.Entry(path_frame,
                                       textvariable=self.save_path_var,
                                       font=self.fonts['normal'],
                                       relief='solid', bd=1,
                                       highlightthickness=1,
                                       highlightcolor=self.colors['accent'])
        self.save_path_entry.grid(row=0, column=0, sticky=(tk.W, tk.E), padx=(0, 5))

        browse_btn = tk.Button(path_frame, text="تصفح",
                              font=self.fonts['normal'],
                              bg=self.colors['bg_toolbar'],
                              fg=self.colors['text_primary'],
                              relief='solid', bd=1,
                              padx=10,
                              command=self.browse_folder)
        browse_btn.grid(row=0, column=1)

        # اسم الملف
        tk.Label(download_frame, text="📄 اسم الملف:",
                font=self.fonts['normal'],
                fg=self.colors['text_primary'],
                bg=self.colors['bg_panel']).grid(row=2, column=0, sticky=tk.W, padx=10, pady=8)

        self.filename_entry = tk.Entry(download_frame,
                                      font=self.fonts['normal'],
                                      relief='solid', bd=1,
                                      highlightthickness=1,
                                      highlightcolor=self.colors['accent'])
        self.filename_entry.grid(row=2, column=1, sticky=(tk.W, tk.E), padx=10, pady=8)

        # أزرار التحكم
        control_frame = tk.Frame(download_frame, bg=self.colors['bg_panel'])
        control_frame.grid(row=3, column=0, columnspan=2, pady=15, padx=10, sticky=(tk.W, tk.E))

        # زر بدء التحميل
        self.download_btn = tk.Button(control_frame,
                                     text="🚀 بدء التحميل",
                                     font=self.fonts['button'],
                                     bg=self.colors['accent'],
                                     fg='white',
                                     relief='flat',
                                     padx=20, pady=8,
                                     command=self.start_download)
        self.download_btn.pack(side=tk.LEFT, padx=5)

        # زر إيقاف مؤقت
        self.pause_btn = tk.Button(control_frame,
                                  text="⏸️ إيقاف مؤقت",
                                  font=self.fonts['button'],
                                  bg=self.colors['warning'],
                                  fg='white',
                                  relief='flat',
                                  padx=15, pady=8,
                                  state=tk.DISABLED,
                                  command=self.pause_download)
        self.pause_btn.pack(side=tk.LEFT, padx=5)

        # زر استئناف
        self.resume_btn = tk.Button(control_frame,
                                   text="▶️ استئناف",
                                   font=self.fonts['button'],
                                   bg=self.colors['success'],
                                   fg='white',
                                   relief='flat',
                                   padx=15, pady=8,
                                   state=tk.DISABLED,
                                   command=self.resume_download)
        self.resume_btn.pack(side=tk.LEFT, padx=5)

        # زر إيقاف
        self.stop_btn = tk.Button(control_frame,
                                 text="⏹️ إيقاف",
                                 font=self.fonts['button'],
                                 bg=self.colors['error'],
                                 fg='white',
                                 relief='flat',
                                 padx=15, pady=8,
                                 state=tk.DISABLED,
                                 command=self.stop_download)
        self.stop_btn.pack(side=tk.LEFT, padx=5)

    def create_downloads_panel(self, parent):
        """إنشاء لوحة التحميلات والتقدم"""
        # إطار التحميلات النشطة
        active_frame = tk.LabelFrame(parent,
                                    text=" التحميلات النشطة ",
                                    font=self.fonts['normal'],
                                    fg=self.colors['text_primary'],
                                    bg=self.colors['bg_panel'],
                                    relief='solid',
                                    bd=1)
        active_frame.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        active_frame.columnconfigure(0, weight=1)
        active_frame.rowconfigure(1, weight=1)

        # شريط التقدم الحالي
        progress_info_frame = tk.Frame(active_frame, bg=self.colors['bg_panel'])
        progress_info_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), padx=10, pady=10)
        progress_info_frame.columnconfigure(0, weight=1)

        # معلومات التحميل الحالي
        self.current_file_label = tk.Label(progress_info_frame,
                                          text="لا يوجد تحميل نشط",
                                          font=self.fonts['normal'],
                                          fg=self.colors['text_primary'],
                                          bg=self.colors['bg_panel'])
        self.current_file_label.grid(row=0, column=0, sticky=tk.W, pady=2)

        # شريط التقدم
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(progress_info_frame,
                                           variable=self.progress_var,
                                           maximum=100,
                                           style='Custom.Horizontal.TProgressbar')
        self.progress_bar.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=5)

        # تفاصيل التقدم
        details_frame = tk.Frame(progress_info_frame, bg=self.colors['bg_panel'])
        details_frame.grid(row=2, column=0, sticky=(tk.W, tk.E), pady=2)
        details_frame.columnconfigure(1, weight=1)

        self.status_label = tk.Label(details_frame,
                                    text="جاهز للتحميل",
                                    font=self.fonts['small'],
                                    fg=self.colors['text_secondary'],
                                    bg=self.colors['bg_panel'])
        self.status_label.grid(row=0, column=0, sticky=tk.W)

        self.speed_label = tk.Label(details_frame,
                                   text="",
                                   font=self.fonts['small'],
                                   fg=self.colors['text_secondary'],
                                   bg=self.colors['bg_panel'])
        self.speed_label.grid(row=0, column=1, sticky=tk.E)

        self.details_label = tk.Label(progress_info_frame,
                                     text="",
                                     font=self.fonts['small'],
                                     fg=self.colors['text_secondary'],
                                     bg=self.colors['bg_panel'])
        self.details_label.grid(row=3, column=0, sticky=tk.W, pady=2)

        # جدول سجل التحميلات
        history_container = tk.Frame(active_frame, bg=self.colors['bg_panel'])
        history_container.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), padx=10, pady=(0, 10))
        history_container.columnconfigure(0, weight=1)
        history_container.rowconfigure(0, weight=1)

        # عنوان الجدول
        table_header = tk.Frame(history_container, bg=self.colors['bg_toolbar'], height=30)
        table_header.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 1))
        table_header.grid_propagate(False)

        tk.Label(table_header,
                text="📋 سجل التحميلات",
                font=self.fonts['normal'],
                fg=self.colors['text_primary'],
                bg=self.colors['bg_toolbar']).pack(side=tk.LEFT, padx=10, pady=5)

        # إطار الجدول
        table_frame = tk.Frame(history_container, bg=self.colors['bg_panel'])
        table_frame.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        table_frame.columnconfigure(0, weight=1)
        table_frame.rowconfigure(0, weight=1)

        # جدول السجل
        columns = ('الوقت', 'اسم الملف', 'الحجم', 'السرعة', 'الحالة')
        self.history_tree = ttk.Treeview(table_frame,
                                        columns=columns,
                                        show='headings',
                                        height=12)

        # تكوين الأعمدة
        column_widths = {'الوقت': 150, 'اسم الملف': 300, 'الحجم': 100, 'السرعة': 100, 'الحالة': 100}
        for col in columns:
            self.history_tree.heading(col, text=col)
            self.history_tree.column(col, width=column_widths.get(col, 150), minwidth=80)

        # شريط التمرير
        scrollbar_y = ttk.Scrollbar(table_frame, orient=tk.VERTICAL, command=self.history_tree.yview)
        scrollbar_x = ttk.Scrollbar(table_frame, orient=tk.HORIZONTAL, command=self.history_tree.xview)

        self.history_tree.configure(yscrollcommand=scrollbar_y.set, xscrollcommand=scrollbar_x.set)

        # ترتيب العناصر
        self.history_tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        scrollbar_y.grid(row=0, column=1, sticky=(tk.N, tk.S))
        scrollbar_x.grid(row=1, column=0, sticky=(tk.W, tk.E))

        # تحديث السجل
        self.update_history_display()

        # إعداد قائمة السياق للحذف
        self.setup_context_menu()

    def on_multi_progress(self, download_id, progress_data):
        """معالج تقدم التحميلات المتعددة"""
        def update():
            # تحديث الجدول
            self.update_download_in_table(download_id, progress_data)

            # تحديث الإحصائيات
            self.update_statistics()

        self.root.after(0, update)

    def on_status_change(self, download_id, status):
        """معالج تغيير حالة التحميل"""
        def update():
            # تحديث لون الصف في الجدول
            self.update_download_status_in_table(download_id, status)

            # إشعار إذا كان مفعلاً
            if self.download_notifications and status in [DownloadStatus.COMPLETED, DownloadStatus.FAILED]:
                self.show_notification(download_id, status)

        self.root.after(0, update)

    def on_multi_completion(self, download_id, file_path):
        """معالج اكتمال التحميل المتعدد"""
        def complete():
            # إضافة إلى السجل
            download_info = self.download_manager.get_download_info(download_id)
            if download_info:
                self.add_to_history(
                    download_info['filename'] or os.path.basename(file_path),
                    download_info.get('progress_data', {}).get('total', 0),
                    download_info.get('progress_data', {}).get('speed', '0 B/s'),
                    "✅ مكتمل"
                )

            # إشعار
            if self.download_notifications:
                messagebox.showinfo("🎉 اكتمل التحميل",
                                  f"تم تحميل الملف بنجاح!\n{os.path.basename(file_path)}")

        self.root.after(0, complete)

    def on_multi_error(self, error_message):
        """معالج أخطاء التحميلات المتعددة"""
        def error():
            if self.download_notifications:
                messagebox.showerror("❌ خطأ", f"خطأ في التحميل:\n{error_message}")

        self.root.after(0, error)

    def on_schedule_event(self, event_type, schedule_id, download_id=None):
        """معالج أحداث الجدولة"""
        def update():
            if event_type == 'started':
                print(f"بدء تحميل مجدول: {schedule_id} -> {download_id}")
            elif event_type == 'cancelled':
                print(f"إلغاء تحميل مجدول: {schedule_id}")

            # تحديث واجهة الجدولة إذا كانت مفتوحة
            self.update_schedule_display()

        self.root.after(0, update)

    def on_scheduler_error(self, error_message):
        """معالج أخطاء المجدول"""
        print(f"خطأ في المجدول: {error_message}")

    def show_notification(self, download_id, status):
        """عرض إشعار"""
        # يمكن تحسين هذا لاستخدام إشعارات النظام
        if status == DownloadStatus.COMPLETED:
            title = "✅ اكتمل التحميل"
            message = f"تم تحميل الملف بنجاح!"
        elif status == DownloadStatus.FAILED:
            title = "❌ فشل التحميل"
            message = f"فشل في تحميل الملف"
        else:
            return

        # يمكن استخدام مكتبة plyer للإشعارات
        print(f"{title}: {message}")

    def update_download_in_table(self, download_id, progress_data):
        """تحديث التحميل في الجدول"""
        # البحث عن العنصر في الجدول وتحديثه
        for item in self.history_tree.get_children():
            values = self.history_tree.item(item, 'values')
            if len(values) > 5 and values[5] == download_id:  # معرف التحميل
                # تحديث القيم
                new_values = list(values)
                new_values[2] = progress_data.get('downloaded_formatted', values[2])  # الحجم
                new_values[3] = progress_data.get('speed', values[3])  # السرعة
                new_values[4] = f"{progress_data.get('percentage', 0):.1f}%"  # التقدم

                self.history_tree.item(item, values=new_values)
                break

    def update_download_status_in_table(self, download_id, status):
        """تحديث حالة التحميل في الجدول"""
        for item in self.history_tree.get_children():
            values = self.history_tree.item(item, 'values')
            if len(values) > 5 and values[5] == download_id:
                new_values = list(values)
                new_values[4] = status.value  # الحالة

                # تحديد لون الصف
                if status == DownloadStatus.COMPLETED:
                    tags = ['success']
                elif status == DownloadStatus.FAILED:
                    tags = ['error']
                elif status == DownloadStatus.DOWNLOADING:
                    tags = ['downloading']
                else:
                    tags = ['normal']

                self.history_tree.item(item, values=new_values, tags=tags)
                break

    def update_statistics(self):
        """تحديث الإحصائيات"""
        stats = self.download_manager.get_statistics()
        # يمكن عرض الإحصائيات في شريط الحالة أو نافذة منفصلة
        status_text = f"نشط: {stats['active_downloads']} | مكتمل: {stats['completed_downloads']} | فاشل: {stats['failed_downloads']}"
        # تحديث شريط الحالة إذا كان موجوداً

    def update_schedule_display(self):
        """تحديث عرض الجدولة"""
        # تحديث واجهة الجدولة إذا كانت مفتوحة
        pass

    def show_new_download_dialog(self):
        """عرض نافذة تحميل جديد متقدمة"""
        try:
            from advanced_dialogs import AdvancedDownloadDialog
            dialog = AdvancedDownloadDialog(self.root, self.download_manager, self.scheduler)
            result = dialog.show()

            if result:
                if result['type'] == 'immediate':
                    print(f"تم إضافة تحميل فوري: {result['id']}")
                elif result['type'] == 'scheduled':
                    print(f"تم إضافة تحميل مجدول: {result['id']}")

                # تحديث الواجهة
                self.update_downloads_display()

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في فتح نافذة التحميل:\n{str(e)}")

    def show_settings(self):
        """عرض نافذة الإعدادات المتقدمة"""
        try:
            from advanced_dialogs import AdvancedSettingsDialog

            # الحصول على الإعدادات الحالية
            current_settings = {
                'max_connections': self.max_connections,
                'auto_retry': self.auto_retry,
                'bandwidth_limit': self.bandwidth_limit,
                'download_notifications': self.download_notifications,
                'default_save_path': self.default_save_path
            }

            dialog = AdvancedSettingsDialog(self.root, current_settings)
            result = dialog.show()

            if result:
                # تطبيق الإعدادات الجديدة
                self.apply_new_settings(result)

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في فتح نافذة الإعدادات:\n{str(e)}")
    
    def browse_folder(self):
        """تصفح واختيار مجلد الحفظ"""
        folder = filedialog.askdirectory(initialdir=self.save_path_var.get())
        if folder:
            self.save_path_var.set(folder)
    
    def start_download(self):
        """بدء التحميل"""
        url = self.url_entry.get().strip()
        save_path = self.save_path_var.get().strip()
        filename = self.filename_entry.get().strip() or None

        if not url:
            messagebox.showerror("خطأ", "يرجى إدخال رابط التحميل")
            return

        if not self.downloader.is_valid_url(url):
            messagebox.showerror("خطأ", "رابط غير صحيح")
            return

        if not os.path.exists(save_path):
            try:
                os.makedirs(save_path)
            except:
                messagebox.showerror("خطأ", "لا يمكن إنشاء مجلد الحفظ")
                return

        # تحديث معلومات التحميل الحالي
        display_filename = filename or self.downloader.get_filename_from_url(url)
        self.current_download = {
            'url': url,
            'filename': display_filename,
            'save_path': save_path
        }

        # بدء التحميل
        if self.downloader.start_download(url, save_path, filename):
            self.update_ui_downloading_state()
            self.current_file_label.config(text=f"📥 {display_filename}")
            self.status_label.config(text="جاري الاتصال...")
        else:
            messagebox.showerror("خطأ", "التحميل قيد التشغيل بالفعل")
    
    def update_ui_downloading_state(self):
        """تحديث حالة الواجهة أثناء التحميل"""
        self.download_btn.config(state=tk.DISABLED, bg=self.colors['border'])
        self.pause_btn.config(state=tk.NORMAL, bg=self.colors['warning'])
        self.resume_btn.config(state=tk.DISABLED, bg=self.colors['border'])
        self.stop_btn.config(state=tk.NORMAL, bg=self.colors['error'])

    def pause_download(self):
        """إيقاف التحميل مؤقتاً"""
        self.downloader.pause_download()
        self.pause_btn.config(state=tk.DISABLED, bg=self.colors['border'])
        self.resume_btn.config(state=tk.NORMAL, bg=self.colors['success'])
        self.status_label.config(text="⏸️ متوقف مؤقتاً")

    def resume_download(self):
        """استئناف التحميل"""
        self.downloader.resume_download()
        self.pause_btn.config(state=tk.NORMAL, bg=self.colors['warning'])
        self.resume_btn.config(state=tk.DISABLED, bg=self.colors['border'])
        self.status_label.config(text="▶️ جاري التحميل...")

    def stop_download(self):
        """إيقاف التحميل نهائياً"""
        self.downloader.stop_download()
        self.reset_ui()
        self.status_label.config(text="⏹️ تم إيقاف التحميل")
        if self.current_download:
            self.add_to_history(
                self.current_download['filename'],
                0,
                "0 B/s",
                "متوقف"
            )

    def reset_ui(self):
        """إعادة تعيين واجهة المستخدم"""
        self.download_btn.config(state=tk.NORMAL, bg=self.colors['accent'])
        self.pause_btn.config(state=tk.DISABLED, bg=self.colors['border'])
        self.resume_btn.config(state=tk.DISABLED, bg=self.colors['border'])
        self.stop_btn.config(state=tk.DISABLED, bg=self.colors['border'])
        self.progress_var.set(0)
        self.current_file_label.config(text="لا يوجد تحميل نشط")
        self.status_label.config(text="جاهز للتحميل")
        self.speed_label.config(text="")
        self.details_label.config(text="")
        self.current_download = None
    
    def update_progress(self, progress_data):
        """تحديث شريط التقدم بتصميم IDM"""
        def update():
            percentage = progress_data['percentage']
            self.progress_var.set(percentage)

            # تحديث النص الرئيسي
            status_text = f"📊 {percentage:.1f}% مكتمل"
            self.status_label.config(text=status_text)

            # تحديث السرعة
            speed_text = f"🚀 {progress_data['speed']}"
            self.speed_label.config(text=speed_text)

            # تحديث التفاصيل
            details_text = (f"📦 {progress_data['downloaded_formatted']} / "
                          f"{progress_data['total_formatted']} | "
                          f"⏱️ متبقي: {progress_data['eta']}")
            self.details_label.config(text=details_text)

            # تحديث لون شريط التقدم حسب التقدم
            if percentage < 30:
                color = self.colors['error']
            elif percentage < 70:
                color = self.colors['warning']
            else:
                color = self.colors['success']

        self.root.after(0, update)
    
    def download_completed(self, file_path):
        """عند اكتمال التحميل"""
        def complete():
            self.reset_ui()
            self.status_label.config(text="✅ تم التحميل بنجاح!")

            # إضافة إلى السجل
            filename = os.path.basename(file_path)
            file_size = os.path.getsize(file_path)

            # حساب متوسط السرعة (تقديري)
            avg_speed = "مكتمل"
            if self.current_download:
                avg_speed = "متوسط"

            self.add_to_history(filename, file_size, avg_speed, "✅ مكتمل")

            # إشعار بالإكمال
            messagebox.showinfo("🎉 نجح التحميل",
                              f"تم تحميل الملف بنجاح!\n\n"
                              f"📁 الملف: {filename}\n"
                              f"📍 المسار: {file_path}\n"
                              f"📦 الحجم: {self.downloader.format_size(file_size)}")

        self.root.after(0, complete)

    def download_error(self, error_message):
        """عند حدوث خطأ في التحميل"""
        def error():
            self.reset_ui()
            self.status_label.config(text="❌ حدث خطأ في التحميل")

            # إضافة إلى السجل
            if self.current_download:
                self.add_to_history(
                    self.current_download['filename'],
                    0,
                    "0 B/s",
                    "❌ فشل"
                )

            messagebox.showerror("❌ خطأ في التحميل",
                               f"فشل في تحميل الملف:\n\n{error_message}")

        self.root.after(0, error)
    
    def add_to_history(self, filename, file_size, speed, status):
        """إضافة عنصر إلى سجل التحميلات"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M")
        size_formatted = self.downloader.format_size(file_size) if file_size > 0 else "غير محدد"

        history_item = {
            'timestamp': timestamp,
            'filename': filename,
            'size': file_size,
            'size_formatted': size_formatted,
            'speed': speed,
            'status': status
        }

        self.download_history.append(history_item)
        self.save_history()
        self.update_history_display()

    def update_history_display(self):
        """تحديث عرض السجل بتصميم IDM"""
        # مسح العناصر الحالية
        for item in self.history_tree.get_children():
            self.history_tree.delete(item)

        # إضافة العناصر الجديدة (آخر 50 عنصر)
        for item in reversed(self.download_history[-50:]):
            # تحديد لون الصف حسب الحالة
            tags = []
            if "مكتمل" in item['status']:
                tags = ['success']
            elif "فشل" in item['status']:
                tags = ['error']
            elif "متوقف" in item['status']:
                tags = ['warning']

            self.history_tree.insert('', 0, values=(
                item['timestamp'],
                item['filename'],
                item['size_formatted'],
                item.get('speed', 'غير محدد'),
                item['status']
            ), tags=tags)

        # تكوين ألوان الصفوف
        self.history_tree.tag_configure('success', background='#e8f5e8')
        self.history_tree.tag_configure('error', background='#fde8e8')
        self.history_tree.tag_configure('warning', background='#fff4e6')
    
    def save_history(self):
        """حفظ السجل"""
        try:
            with open('download_history.json', 'w', encoding='utf-8') as f:
                json.dump(self.download_history, f, ensure_ascii=False, indent=2)
        except:
            pass
    
    def load_history(self):
        """تحميل السجل"""
        try:
            with open('download_history.json', 'r', encoding='utf-8') as f:
                self.download_history = json.load(f)
        except:
            self.download_history = []

    def apply_new_settings(self, new_settings):
        """تطبيق الإعدادات الجديدة"""
        try:
            # تحديث المتغيرات
            self.max_connections = new_settings.get('max_connections', self.max_connections)
            self.auto_retry = new_settings.get('auto_retry', self.auto_retry)
            self.bandwidth_limit = new_settings.get('bandwidth_limit', self.bandwidth_limit)
            self.download_notifications = new_settings.get('download_notifications', self.download_notifications)
            self.default_save_path = new_settings.get('default_save_path', self.default_save_path)

            # تطبيق على المحمل
            self.downloader.set_advanced_options(
                max_connections=self.max_connections,
                chunk_size=new_settings.get('chunk_size', 8192),
                timeout=new_settings.get('timeout', 30),
                max_retries=3 if self.auto_retry else 1
            )

            # تحديث مسار الحفظ الافتراضي
            self.save_path_var.set(self.default_save_path)

            # حفظ الإعدادات
            self.save_settings()

            success_msg = language_manager.get_text("operation_completed")
            messagebox.showinfo(language_manager.get_text("success"), success_msg)

        except Exception as e:
            error_msg = language_manager.get_text("error")
            messagebox.showerror(error_msg, f"فشل في تطبيق الإعدادات:\n{str(e)}")

    def setup_context_menu(self):
        """إعداد قائمة السياق للحذف"""
        self.context_menu = tk.Menu(self.root, tearoff=0)

        # إضافة خيارات القائمة
        self.context_menu.add_command(
            label=language_manager.get_text("remove_download"),
            command=self.remove_selected_download
        )

        self.context_menu.add_command(
            label=language_manager.get_text("delete_download"),
            command=self.delete_selected_download
        )

        self.context_menu.add_separator()

        self.context_menu.add_command(
            label=language_manager.get_text("copy"),
            command=self.copy_download_info
        )

        self.context_menu.add_command(
            label=language_manager.get_text("restart_download"),
            command=self.restart_selected_download
        )

        # ربط القائمة بالجدول
        self.history_tree.bind("<Button-3>", self.show_context_menu)

    def show_context_menu(self, event):
        """عرض قائمة السياق"""
        try:
            # تحديد العنصر المحدد
            item = self.history_tree.selection()[0]
            if item:
                self.context_menu.post(event.x_root, event.y_root)
        except IndexError:
            pass

    def remove_selected_download(self):
        """إزالة التحميل المحدد من القائمة فقط"""
        try:
            selected_item = self.history_tree.selection()[0]
            if selected_item:
                confirm_msg = language_manager.get_text("confirm_delete")
                if messagebox.askyesno(language_manager.get_text("warning"), confirm_msg):
                    self.history_tree.delete(selected_item)
                    success_msg = language_manager.get_text("operation_completed")
                    messagebox.showinfo(language_manager.get_text("success"), success_msg)
        except IndexError:
            pass

    def delete_selected_download(self):
        """حذف التحميل المحدد مع الملف"""
        try:
            selected_item = self.history_tree.selection()[0]
            if selected_item:
                values = self.history_tree.item(selected_item, 'values')

                # الحصول على معرف التحميل إذا كان متوفراً
                download_id = values[6] if len(values) > 6 else None

                confirm_msg = language_manager.get_text("confirm_delete")
                if messagebox.askyesno(language_manager.get_text("warning"), confirm_msg):

                    # إنشاء معلومات التحميل للحذف
                    download_info = {
                        'filename': values[1] if len(values) > 1 else 'unknown',
                        'url': 'unknown',
                        'file_path': None  # يمكن تحسين هذا لاحقاً
                    }

                    # حذف باستخدام مدير الحذف
                    if delete_manager.delete_download(download_id or 'unknown', download_info, delete_file=True):
                        self.history_tree.delete(selected_item)
                        success_msg = language_manager.get_text("operation_completed")
                        messagebox.showinfo(language_manager.get_text("success"), success_msg)
                    else:
                        error_msg = language_manager.get_text("error")
                        messagebox.showerror(error_msg, "فشل في حذف التحميل")

        except IndexError:
            pass
        except Exception as e:
            error_msg = language_manager.get_text("error")
            messagebox.showerror(error_msg, f"خطأ في الحذف: {str(e)}")

    def copy_download_info(self):
        """نسخ معلومات التحميل"""
        try:
            selected_item = self.history_tree.selection()[0]
            if selected_item:
                values = self.history_tree.item(selected_item, 'values')
                info = f"الملف: {values[1]}\nالحجم: {values[2]}\nالحالة: {values[4]}"

                self.root.clipboard_clear()
                self.root.clipboard_append(info)

                success_msg = language_manager.get_text("operation_completed")
                messagebox.showinfo(language_manager.get_text("success"), "تم نسخ المعلومات")
        except IndexError:
            pass

    def restart_selected_download(self):
        """إعادة بدء التحميل المحدد"""
        try:
            selected_item = self.history_tree.selection()[0]
            if selected_item:
                # يمكن تحسين هذا لإعادة بدء التحميل فعلياً
                messagebox.showinfo(language_manager.get_text("info"), "ميزة إعادة البدء ستتوفر قريباً")
        except IndexError:
            pass

    def change_language(self, language_code):
        """تغيير اللغة"""
        if language_manager.set_language(language_code):
            # تحديث عنوان النافذة
            app_title = language_manager.get_text("app_title")
            self.root.title(app_title)

            # تحديث النصوص في الواجهة
            self.update_interface_texts()

            success_msg = language_manager.get_text("operation_completed")
            messagebox.showinfo(language_manager.get_text("success"), success_msg)

    def change_theme(self, theme_name):
        """تغيير الثيم"""
        if theme_manager.set_theme(theme_name):
            # تحديث الألوان
            self.colors = theme_manager.get_current_theme()

            # تطبيق الثيم الجديد
            self.apply_theme_to_interface()

            # حفظ التفضيلات
            theme_manager.save_theme_preference()

            success_msg = language_manager.get_text("operation_completed")
            messagebox.showinfo(language_manager.get_text("success"), success_msg)

    def update_interface_texts(self):
        """تحديث النصوص في الواجهة"""
        try:
            # تحديث أزرار شريط الأدوات
            if hasattr(self, 'new_download_btn'):
                self.new_download_btn.config(text=f"📥 {language_manager.get_text('new_download')}")

            # تحديث أزرار التحكم
            if hasattr(self, 'download_btn'):
                self.download_btn.config(text=f"🚀 {language_manager.get_text('start_download')}")

            if hasattr(self, 'pause_btn'):
                self.pause_btn.config(text=f"⏸️ {language_manager.get_text('pause_download')}")

            if hasattr(self, 'resume_btn'):
                self.resume_btn.config(text=f"▶️ {language_manager.get_text('resume_download')}")

            if hasattr(self, 'stop_btn'):
                self.stop_btn.config(text=f"⏹️ {language_manager.get_text('stop_download')}")

            # تحديث تسميات الأعمدة
            if hasattr(self, 'history_tree'):
                columns = [
                    language_manager.get_text("time", "الوقت"),
                    language_manager.get_text("filename", "اسم الملف"),
                    language_manager.get_text("file_size", "الحجم"),
                    language_manager.get_text("download_speed", "السرعة"),
                    language_manager.get_text("status", "الحالة")
                ]

                for i, col in enumerate(self.history_tree['columns']):
                    if i < len(columns):
                        self.history_tree.heading(col, text=columns[i])

        except Exception as e:
            print(f"خطأ في تحديث النصوص: {e}")

    def apply_theme_to_interface(self):
        """تطبيق الثيم على الواجهة"""
        try:
            # تطبيق على النافذة الرئيسية
            theme_manager.apply_theme_to_widget(self.root, "main_window")

            # تطبيق على الأزرار
            if hasattr(self, 'new_download_btn'):
                theme_manager.apply_theme_to_widget(self.new_download_btn, "button_primary")

            if hasattr(self, 'download_btn'):
                theme_manager.apply_theme_to_widget(self.download_btn, "button_primary")

            if hasattr(self, 'pause_btn'):
                theme_manager.apply_theme_to_widget(self.pause_btn, "button_warning")

            if hasattr(self, 'resume_btn'):
                theme_manager.apply_theme_to_widget(self.resume_btn, "button_success")

            if hasattr(self, 'stop_btn'):
                theme_manager.apply_theme_to_widget(self.stop_btn, "button_error")

            # تطبيق على حقول الإدخال
            if hasattr(self, 'url_entry'):
                theme_manager.apply_theme_to_widget(self.url_entry, "entry")

            if hasattr(self, 'save_path_entry'):
                theme_manager.apply_theme_to_widget(self.save_path_entry, "entry")

            if hasattr(self, 'filename_entry'):
                theme_manager.apply_theme_to_widget(self.filename_entry, "entry")

            # تحديث ألوان حالات التحميل في الجدول
            self.update_download_colors()

        except Exception as e:
            print(f"خطأ في تطبيق الثيم: {e}")

    def update_download_colors(self):
        """تحديث ألوان حالات التحميل"""
        try:
            if hasattr(self, 'history_tree'):
                # تكوين ألوان الصفوف حسب الثيم الحالي
                theme = theme_manager.get_current_theme()

                self.history_tree.tag_configure('success',
                    background=theme['success_light'] if 'success_light' in theme else theme['success'])

                self.history_tree.tag_configure('error',
                    background=theme['error_light'] if 'error_light' in theme else theme['error'])

                self.history_tree.tag_configure('warning',
                    background=theme['warning_light'] if 'warning_light' in theme else theme['warning'])

                self.history_tree.tag_configure('downloading',
                    background=theme['info_light'] if 'info_light' in theme else theme['info'])

        except Exception as e:
            print(f"خطأ في تحديث ألوان التحميل: {e}")

    def show_trash_manager(self):
        """عرض مدير سلة المحذوفات"""
        try:
            from trash_dialog import TrashManagerDialog
            dialog = TrashManagerDialog(self.root, delete_manager)
            dialog.show()
        except Exception as e:
            error_msg = language_manager.get_text("error")
            messagebox.showerror(error_msg, f"فشل في فتح مدير سلة المحذوفات:\n{str(e)}")

    def show_theme_menu(self):
        """عرض قائمة الثيمات"""
        theme_menu = tk.Menu(self.root, tearoff=0)

        # إضافة الثيمات المتاحة
        for theme_name in theme_manager.get_theme_names():
            theme_menu.add_command(
                label=theme_name.replace('_', ' ').title(),
                command=lambda t=theme_name: self.change_theme(t)
            )

        # عرض القائمة
        try:
            theme_menu.post(self.root.winfo_pointerx(), self.root.winfo_pointery())
        except:
            pass

    def show_language_menu(self):
        """عرض قائمة اللغات"""
        language_menu = tk.Menu(self.root, tearoff=0)

        # إضافة اللغات المتاحة
        language_names = language_manager.get_language_names()
        for code, name in language_names.items():
            language_menu.add_command(
                label=name,
                command=lambda c=code: self.change_language(c)
            )

        # عرض القائمة
        try:
            language_menu.post(self.root.winfo_pointerx(), self.root.winfo_pointery())
        except:
            pass

    def show_analytics_dashboard(self):
        """عرض لوحة الإحصائيات"""
        try:
            from analytics_dashboard import AnalyticsDashboard
            dashboard = AnalyticsDashboard(self.root, analytics_system, smart_accelerator)
            dashboard.show()
        except Exception as e:
            error_msg = language_manager.get_text("error")
            messagebox.showerror(error_msg, f"فشل في فتح لوحة الإحصائيات:\n{str(e)}")

    def show_backup_manager(self):
        """عرض مدير النسخ الاحتياطية"""
        try:
            from backup_dialog import BackupManagerDialog
            dialog = BackupManagerDialog(self.root, backup_system)
            dialog.show()
        except Exception as e:
            error_msg = language_manager.get_text("error")
            messagebox.showerror(error_msg, f"فشل في فتح مدير النسخ الاحتياطية:\n{str(e)}")

    def show_accelerator_settings(self):
        """عرض إعدادات التسريع الذكي"""
        try:
            from accelerator_dialog import AcceleratorSettingsDialog
            dialog = AcceleratorSettingsDialog(self.root, smart_accelerator)
            dialog.show()
        except Exception as e:
            error_msg = language_manager.get_text("error")
            messagebox.showerror(error_msg, f"فشل في فتح إعدادات التسريع:\n{str(e)}")

    def setup_enhanced_notifications(self):
        """إعداد الإشعارات المحسنة"""
        # ربط الإشعارات بأحداث التحميل
        self.download_manager.on_download_completed = self.on_download_completed_enhanced
        self.download_manager.on_download_failed = self.on_download_failed_enhanced
        self.download_manager.on_download_paused = self.on_download_paused_enhanced
        self.download_manager.on_download_resumed = self.on_download_resumed_enhanced

    def on_download_completed_enhanced(self, download_id, filename, file_size, download_time):
        """معالج اكتمال التحميل المحسن"""
        # إشعار النظام
        notification_system.download_completed_notification(filename, file_size, download_time)

        # تسجيل في الإحصائيات
        analytics_system.record_download_completion(download_id, success=True)

        # تسجيل الأداء للتسريع الذكي
        performance_data = {
            'success': True,
            'average_speed': file_size / download_time if download_time > 0 else 0,
            'efficiency': 100  # يمكن حساب هذا بشكل أكثر تعقيداً
        }

        # الحصول على الإعدادات المستخدمة (يمكن تحسين هذا)
        settings = {
            'connections': self.max_connections,
            'chunk_size': 8192,
            'timeout': 30
        }

        smart_accelerator.record_performance(
            self.url_var.get(),  # يمكن تحسين هذا للحصول على الرابط الصحيح
            settings,
            performance_data
        )

    def on_download_failed_enhanced(self, download_id, filename, error_message):
        """معالج فشل التحميل المحسن"""
        # إشعار النظام
        notification_system.download_failed_notification(filename, error_message)

        # تسجيل في الإحصائيات
        analytics_system.record_download_completion(download_id, success=False, error_message=error_message)

    def on_download_paused_enhanced(self, download_id, filename):
        """معالج إيقاف التحميل المحسن"""
        # إشعار النظام
        notification_system.download_paused_notification(filename)

    def on_download_resumed_enhanced(self, download_id, filename):
        """معالج استئناف التحميل المحسن"""
        # إشعار النظام
        notification_system.download_resumed_notification(filename)

    def enhanced_download_start(self):
        """بدء تحميل محسن مع جميع الأنظمة الجديدة"""
        url = self.url_var.get().strip()
        if not url:
            messagebox.showerror(language_manager.get_text("error"),
                               language_manager.get_text("invalid_url"))
            return

        save_path = self.save_path_var.get()
        if not save_path:
            messagebox.showerror(language_manager.get_text("error"),
                               "يرجى تحديد مجلد الحفظ")
            return

        try:
            # الحصول على الإعدادات المثلى من التسريع الذكي
            optimal_settings = smart_accelerator.optimize_download_settings(url)

            # تطبيق الإعدادات المثلى
            self.downloader.set_advanced_options(
                max_connections=optimal_settings['connections'],
                chunk_size=optimal_settings['chunk_size'],
                timeout=optimal_settings['timeout'],
                max_retries=3
            )

            # إنشاء معرف فريد للتحميل
            download_id = f"download_{int(time.time())}"

            # تسجيل بدء التحميل في الإحصائيات
            filename = self.filename_var.get() or url.split('/')[-1]
            analytics_system.record_download_start(download_id, url, filename)

            # بدء التحميل العادي
            self.start_download()

            # إشعار بدء التحميل
            notification_system.show_notification(
                language_manager.get_text("download_started", "بدء التحميل"),
                f"{language_manager.get_text('filename')}: {filename}",
                "info",
                duration=3000
            )

        except Exception as e:
            error_msg = language_manager.get_text("error")
            messagebox.showerror(error_msg, f"خطأ في بدء التحميل المحسن:\n{str(e)}")

    def create_automatic_backup(self):
        """إنشاء نسخة احتياطية تلقائية"""
        try:
            success = backup_system.create_automatic_backup()
            if success:
                notification_system.show_notification(
                    language_manager.get_text("backup_created", "تم إنشاء نسخة احتياطية"),
                    language_manager.get_text("auto_backup_success", "تم إنشاء نسخة احتياطية تلقائية بنجاح"),
                    "success",
                    duration=5000
                )
        except Exception as e:
            print(f"خطأ في النسخة الاحتياطية التلقائية: {e}")

def main():
    """الدالة الرئيسية المحسنة"""
    try:
        root = tk.Tk()
        app = DownloadManagerGUI(root)

        # إعداد إغلاق نظيف
        def on_closing():
            try:
                # إيقاف الخدمات
                if hasattr(app, 'download_manager'):
                    app.download_manager.stop_manager()
                if hasattr(app, 'scheduler'):
                    app.scheduler.stop_scheduler()

                # حفظ البيانات
                app.save_settings()
                app.save_history()

                root.destroy()
            except:
                root.destroy()

        root.protocol("WM_DELETE_WINDOW", on_closing)
        root.mainloop()

    except Exception as e:
        messagebox.showerror("خطأ فادح", f"فشل في تشغيل البرنامج:\n{str(e)}")

if __name__ == "__main__":
    main()
