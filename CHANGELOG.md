# سجل التغييرات - Changelog

جميع التغييرات المهمة في هذا المشروع سيتم توثيقها في هذا الملف.

## [الإصدار 1.0.0] - 2024-01-XX

### المميزات الجديدة ✨
- واجهة رسومية باللغة العربية مع دعم كامل للنصوص العربية
- تحميل الملفات من الروابط المباشرة مع دعم HTTP/HTTPS
- شريط تقدم متقدم يعرض:
  - النسبة المئوية للتحميل
  - الحجم المحمل والحجم الإجمالي
  - سرعة التحميل الحالية
  - الوقت المتبقي المتوقع
- أزرار تحكم شاملة:
  - بدء التحميل
  - إيقاف مؤقت
  - استئناف التحميل
  - إيقاف نهائي
- اختيار مجلد الحفظ المخصص مع تصفح المجلدات
- تسمية الملفات المخصصة (اختياري)
- سجل التحميلات مع حفظ تلقائي:
  - الوقت والتاريخ
  - اسم الملف
  - حجم الملف
  - حالة التحميل

### المميزات التقنية 🔧
- استئناف التحميل المتقطع (Resume Support)
- تحميل متعدد الخيوط لتحسين الأداء
- التحقق من صحة الروابط قبل التحميل
- التحقق من صلاحيات الكتابة في مجلد الحفظ
- تنظيف أسماء الملفات من الأحرف غير المسموحة
- دعم أنواع ملفات متعددة
- معالجة شاملة للأخطاء مع رسائل واضحة

### الأمان والموثوقية 🛡️
- التحقق من صحة الروابط
- حماية من الكتابة فوق الملفات الموجودة
- إنشاء أسماء ملفات فريدة تلقائياً
- معالجة انقطاع الاتصال
- حفظ تلقائي لسجل التحميلات

### واجهة المستخدم 🎨
- تصميم نظيف وسهل الاستخدام
- دعم كامل للغة العربية
- ألوان وخطوط متناسقة
- رسائل حالة واضحة
- جدول سجل التحميلات مع إمكانية التمرير

### الملفات المضافة 📁
- `main.py` - الملف الرئيسي وواجهة المستخدم
- `downloader.py` - وحدة التحميل الأساسية
- `utils.py` - أدوات مساعدة للملفات والروابط والوقت
- `config.py` - إعدادات البرنامج والثوابت
- `requirements.txt` - قائمة المكتبات المطلوبة
- `README.md` - دليل الاستخدام الشامل
- `troubleshooting.md` - دليل استكشاف الأخطاء
- `test_download.py` - ملف اختبار الوظائف
- `run.bat` - ملف تشغيل لنظام Windows
- `run.sh` - ملف تشغيل لأنظمة Linux/Mac

### المتطلبات 📋
- Python 3.7 أو أحدث
- requests >= 2.31.0
- tqdm >= 4.65.0
- urllib3 >= 2.0.0
- tkinter (مدمج مع Python)

### الاختبارات ✅
- اختبار التحقق من صحة الروابط
- اختبار استخراج أسماء الملفات
- اختبار تحويل أحجام الملفات
- اختبار تنظيف أسماء الملفات
- اختبار صحة مسارات التحميل
- اختبار التحميل الفعلي

---

## خطط التطوير المستقبلية 🚀

### الإصدار 1.1.0 (مخطط)
- [ ] دعم تحميل متعدد الملفات
- [ ] دعم قوائم التحميل (Batch Download)
- [ ] إعدادات متقدمة للتحميل
- [ ] دعم البروكسي
- [ ] تحسينات الأداء

### الإصدار 1.2.0 (مخطط)
- [ ] دعم المواقع المحمية بكلمة مرور
- [ ] دعم ملفات التورنت
- [ ] جدولة التحميلات
- [ ] إشعارات سطح المكتب
- [ ] سمات مختلفة للواجهة

### الإصدار 2.0.0 (مخطط)
- [ ] واجهة ويب
- [ ] API للتحكم عن بُعد
- [ ] دعم التحميل السحابي
- [ ] تطبيق موبايل
- [ ] مزامنة عبر الأجهزة

---

## المساهمة 🤝

نرحب بالمساهمات من المجتمع! يمكنك المساهمة عبر:

- الإبلاغ عن الأخطاء
- اقتراح مميزات جديدة
- تحسين الكود
- ترجمة البرنامج
- كتابة الوثائق

---

## الترخيص 📄

هذا المشروع مرخص تحت رخصة MIT - انظر ملف LICENSE للتفاصيل.

---

## الشكر والتقدير 🙏

شكر خاص لجميع المساهمين والمختبرين الذين ساعدوا في تطوير هذا البرنامج.

---

**ملاحظة**: التواريخ في هذا الملف تتبع تنسيق YYYY-MM-DD.
