"""
اختبار المميزات المتقدمة الجديدة - Advanced Features Test
اختبار شامل لجميع الأنظمة الجديدة المضافة
"""

import unittest
import os
import json
import time
from datetime import datetime, timedelta
import tempfile
import shutil

# استيراد الأنظمة الجديدة
from notification_system import notification_system
from analytics_system import analytics_system
from smart_accelerator import smart_accelerator
from backup_system import backup_system

class TestNotificationSystem(unittest.TestCase):
    """اختبار نظام الإشعارات"""
    
    def setUp(self):
        """إعداد الاختبار"""
        self.notification_system = notification_system
    
    def test_notification_creation(self):
        """اختبار إنشاء الإشعارات"""
        # اختبار إشعار عادي
        self.notification_system.show_notification(
            "اختبار", "هذا إشعار اختبار", "info", duration=1000
        )
        
        # التحقق من إضافة الإشعار للسجل
        self.assertGreater(len(self.notification_system.notification_history), 0)
        
        # اختبار أنواع مختلفة من الإشعارات
        types = ['success', 'error', 'warning', 'info']
        for notification_type in types:
            self.notification_system.show_notification(
                f"اختبار {notification_type}", 
                f"إشعار من نوع {notification_type}", 
                notification_type, 
                duration=500
            )
    
    def test_notification_history(self):
        """اختبار سجل الإشعارات"""
        initial_count = len(self.notification_system.notification_history)
        
        # إضافة إشعارات
        for i in range(5):
            self.notification_system.show_notification(
                f"اختبار {i}", f"رسالة اختبار {i}", "info", duration=100
            )
        
        # التحقق من زيادة العدد
        self.assertEqual(
            len(self.notification_system.notification_history), 
            initial_count + 5
        )
        
        # اختبار تمييز كمقروء
        unread_before = self.notification_system.get_unread_count()
        self.notification_system.mark_as_read(0)
        unread_after = self.notification_system.get_unread_count()
        
        self.assertEqual(unread_after, unread_before - 1)
    
    def test_specialized_notifications(self):
        """اختبار الإشعارات المتخصصة"""
        # إشعار اكتمال التحميل
        self.notification_system.download_completed_notification(
            "test_file.zip", 1024*1024, 30
        )
        
        # إشعار فشل التحميل
        self.notification_system.download_failed_notification(
            "failed_file.zip", "خطأ في الشبكة"
        )
        
        # إشعار إيقاف التحميل
        self.notification_system.download_paused_notification("paused_file.zip")
        
        # إشعار استئناف التحميل
        self.notification_system.download_resumed_notification("resumed_file.zip")
        
        # التحقق من إضافة الإشعارات
        self.assertGreaterEqual(len(self.notification_system.notification_history), 4)

class TestAnalyticsSystem(unittest.TestCase):
    """اختبار نظام الإحصائيات"""
    
    def setUp(self):
        """إعداد الاختبار"""
        self.analytics_system = analytics_system
    
    def test_download_recording(self):
        """اختبار تسجيل التحميلات"""
        # تسجيل بدء تحميل
        download_id = "test_download_1"
        self.analytics_system.record_download_start(
            download_id, "http://example.com/file.zip", "file.zip", 1024*1024
        )
        
        # تسجيل تقدم التحميل
        self.analytics_system.record_download_progress(
            download_id, 512*1024, 1024*1024, 100*1024
        )
        
        # تسجيل اكتمال التحميل
        self.analytics_system.record_download_completion(download_id, success=True)
        
        # التحقق من الإحصائيات
        stats = self.analytics_system.get_session_statistics()
        self.assertGreater(stats['downloads_started'], 0)
        self.assertGreater(stats['downloads_completed'], 0)
    
    def test_speed_analytics(self):
        """اختبار تحليل السرعة"""
        # إضافة بيانات سرعة وهمية
        for i in range(10):
            speed = 100*1024 + i*10*1024  # سرعة متزايدة
            self.analytics_system.real_time_data['speed_history'].append({
                'speed': speed,
                'timestamp': datetime.now()
            })
        
        # الحصول على تحليل السرعة
        speed_analytics = self.analytics_system.get_speed_analytics()
        
        self.assertGreater(speed_analytics['current_speed'], 0)
        self.assertGreater(speed_analytics['average_speed'], 0)
        self.assertGreater(speed_analytics['max_speed'], 0)
        self.assertIn(speed_analytics['speed_trend'], ['increasing', 'decreasing', 'stable'])
    
    def test_performance_metrics(self):
        """اختبار مقاييس الأداء"""
        # محاكاة بعض التحميلات
        for i in range(5):
            download_id = f"test_download_{i}"
            self.analytics_system.record_download_start(
                download_id, f"http://example.com/file{i}.zip", f"file{i}.zip"
            )
            
            # محاكاة نجاح أو فشل
            success = i % 2 == 0
            self.analytics_system.record_download_completion(download_id, success=success)
        
        # الحصول على مقاييس الأداء
        performance = self.analytics_system.get_performance_metrics()
        
        self.assertIsInstance(performance['success_rate'], (int, float))
        self.assertIsInstance(performance['failure_rate'], (int, float))
        self.assertIsInstance(performance['efficiency_score'], (int, float))
    
    def test_data_export(self):
        """اختبار تصدير البيانات"""
        # تصدير البيانات
        filename = self.analytics_system.export_data('json')
        
        if filename:
            self.assertTrue(os.path.exists(filename))
            
            # قراءة الملف المصدر
            with open(filename, 'r', encoding='utf-8') as f:
                exported_data = json.load(f)
            
            self.assertIn('session_data', exported_data)
            self.assertIn('historical_data', exported_data)
            
            # تنظيف
            os.remove(filename)

class TestSmartAccelerator(unittest.TestCase):
    """اختبار نظام التسريع الذكي"""
    
    def setUp(self):
        """إعداد الاختبار"""
        self.smart_accelerator = smart_accelerator
    
    def test_optimization_settings(self):
        """اختبار إعدادات التحسين"""
        # اختبار تحسين إعدادات التحميل
        url = "http://example.com/largefile.zip"
        settings = self.smart_accelerator.optimize_download_settings(url, file_size=100*1024*1024)
        
        self.assertIn('connections', settings)
        self.assertIn('chunk_size', settings)
        self.assertIn('retry_delay', settings)
        self.assertIn('timeout', settings)
        
        # التحقق من القيم المعقولة
        self.assertGreaterEqual(settings['connections'], 1)
        self.assertLessEqual(settings['connections'], 32)
        self.assertGreater(settings['chunk_size'], 0)
        self.assertGreater(settings['timeout'], 0)
    
    def test_server_analysis(self):
        """اختبار تحليل الخادم"""
        url = "http://example.com/file.zip"
        server_info = self.smart_accelerator.analyze_server(url)
        
        self.assertIsInstance(server_info, dict)
        self.assertIn('response_times', server_info)
        self.assertIn('success_rate', server_info)
        self.assertIn('optimal_connections', server_info)
    
    def test_file_size_categorization(self):
        """اختبار تصنيف حجم الملف"""
        # اختبار أحجام مختلفة
        test_sizes = [
            (500*1024, "small"),           # 500KB
            (50*1024*1024, "medium"),      # 50MB
            (500*1024*1024, "large"),      # 500MB
            (2*1024*1024*1024, "very_large") # 2GB
        ]
        
        for size, expected_category in test_sizes:
            category = self.smart_accelerator.categorize_file_size(size)
            self.assertEqual(category, expected_category)
    
    def test_performance_recording(self):
        """اختبار تسجيل الأداء"""
        url = "http://example.com/testfile.zip"
        settings = {
            'connections': 4,
            'chunk_size': 8192,
            'timeout': 30
        }
        performance_data = {
            'success': True,
            'average_speed': 1024*1024,  # 1MB/s
            'efficiency': 85
        }
        
        # تسجيل الأداء
        self.smart_accelerator.record_performance(url, settings, performance_data)
        
        # التحقق من إضافة البيانات
        self.assertGreater(len(self.smart_accelerator.performance_history), 0)
    
    def test_acceleration_status(self):
        """اختبار حالة التسريع"""
        status = self.smart_accelerator.get_acceleration_status()
        
        self.assertIn('enabled', status)
        self.assertIn('learning_mode', status)
        self.assertIn('optimization_level', status)
        self.assertIn('optimal_connections', status)
        self.assertIn('optimal_chunk_size', status)
        self.assertIn('network_quality', status)

class TestBackupSystem(unittest.TestCase):
    """اختبار نظام النسخ الاحتياطي"""
    
    def setUp(self):
        """إعداد الاختبار"""
        self.backup_system = backup_system
        self.test_dir = tempfile.mkdtemp()
        
        # إنشاء ملفات اختبار
        self.create_test_files()
    
    def tearDown(self):
        """تنظيف بعد الاختبار"""
        if os.path.exists(self.test_dir):
            shutil.rmtree(self.test_dir)
    
    def create_test_files(self):
        """إنشاء ملفات اختبار"""
        test_files = [
            'download_history.json',
            'settings.json',
            'analytics_data.json'
        ]
        
        for filename in test_files:
            filepath = os.path.join(self.test_dir, filename)
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump({'test': True, 'created_at': datetime.now().isoformat()}, f)
    
    def test_backup_creation(self):
        """اختبار إنشاء النسخ الاحتياطية"""
        # تغيير مجلد العمل مؤقتاً
        original_cwd = os.getcwd()
        os.chdir(self.test_dir)
        
        try:
            # إنشاء نسخة احتياطية
            backup_path, backup_info = self.backup_system.create_backup("test_backup")
            
            if backup_path:
                self.assertTrue(os.path.exists(backup_path))
                self.assertIsInstance(backup_info, dict)
                self.assertIn('created_at', backup_info)
                self.assertIn('files_count', backup_info)
        finally:
            os.chdir(original_cwd)
    
    def test_backup_verification(self):
        """اختبار التحقق من النسخ الاحتياطية"""
        original_cwd = os.getcwd()
        os.chdir(self.test_dir)
        
        try:
            # إنشاء نسخة احتياطية
            backup_path, _ = self.backup_system.create_backup("verification_test")
            
            if backup_path:
                # التحقق من النسخة الاحتياطية
                is_valid, message = self.backup_system.verify_backup(backup_path)
                self.assertTrue(is_valid)
                self.assertIn("سليمة", message)
        finally:
            os.chdir(original_cwd)
    
    def test_backup_statistics(self):
        """اختبار إحصائيات النسخ الاحتياطية"""
        stats = self.backup_system.get_backup_statistics()
        
        self.assertIn('total_backups', stats)
        self.assertIn('total_size', stats)
        self.assertIn('auto_backup_enabled', stats)
        self.assertIn('backup_interval_hours', stats)
        self.assertIn('max_backups', stats)
    
    def test_backup_settings(self):
        """اختبار إعدادات النسخ الاحتياطي"""
        # تغيير الإعدادات
        original_interval = self.backup_system.backup_interval_hours
        original_max = self.backup_system.max_backups
        
        self.backup_system.backup_interval_hours = 12
        self.backup_system.max_backups = 5
        
        # التحقق من التغيير
        self.assertEqual(self.backup_system.backup_interval_hours, 12)
        self.assertEqual(self.backup_system.max_backups, 5)
        
        # إعادة الإعدادات الأصلية
        self.backup_system.backup_interval_hours = original_interval
        self.backup_system.max_backups = original_max

class TestIntegration(unittest.TestCase):
    """اختبار التكامل بين الأنظمة"""
    
    def test_notification_analytics_integration(self):
        """اختبار التكامل بين الإشعارات والإحصائيات"""
        # محاكاة تحميل مكتمل
        download_id = "integration_test_1"
        filename = "integration_test.zip"
        file_size = 1024*1024
        download_time = 30
        
        # تسجيل في الإحصائيات
        analytics_system.record_download_start(download_id, "http://example.com/file.zip", filename, file_size)
        analytics_system.record_download_completion(download_id, success=True)
        
        # إرسال إشعار
        notification_system.download_completed_notification(filename, file_size, download_time)
        
        # التحقق من التسجيل في كلا النظامين
        stats = analytics_system.get_session_statistics()
        self.assertGreater(stats['downloads_completed'], 0)
        
        notifications = notification_system.notification_history
        self.assertGreater(len(notifications), 0)
    
    def test_accelerator_analytics_integration(self):
        """اختبار التكامل بين التسريع والإحصائيات"""
        url = "http://example.com/integration_test.zip"
        
        # الحصول على إعدادات محسنة
        settings = smart_accelerator.optimize_download_settings(url, file_size=10*1024*1024)
        
        # محاكاة أداء
        performance_data = {
            'success': True,
            'average_speed': 2*1024*1024,  # 2MB/s
            'efficiency': 90
        }
        
        # تسجيل الأداء
        smart_accelerator.record_performance(url, settings, performance_data)
        
        # التحقق من التسجيل
        acceleration_status = smart_accelerator.get_acceleration_status()
        self.assertGreater(acceleration_status['performance_samples'], 0)

def run_all_tests():
    """تشغيل جميع الاختبارات"""
    print("🧪 بدء اختبار المميزات المتقدمة الجديدة...")
    print("=" * 60)

    # إنشاء مجموعة الاختبارات
    test_suite = unittest.TestSuite()

    # إضافة اختبارات نظام الإشعارات
    test_suite.addTest(unittest.TestLoader().loadTestsFromTestCase(TestNotificationSystem))

    # إضافة اختبارات نظام الإحصائيات
    test_suite.addTest(unittest.TestLoader().loadTestsFromTestCase(TestAnalyticsSystem))

    # إضافة اختبارات نظام التسريع الذكي
    test_suite.addTest(unittest.TestLoader().loadTestsFromTestCase(TestSmartAccelerator))

    # إضافة اختبارات نظام النسخ الاحتياطي
    test_suite.addTest(unittest.TestLoader().loadTestsFromTestCase(TestBackupSystem))

    # إضافة اختبارات التكامل
    test_suite.addTest(unittest.TestLoader().loadTestsFromTestCase(TestIntegration))

    # تشغيل الاختبارات
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # عرض النتائج
    print("\n" + "=" * 60)
    print("📊 نتائج اختبار المميزات المتقدمة:")
    print(f"✅ اختبارات نجحت: {result.testsRun - len(result.failures) - len(result.errors)}")
    print(f"❌ اختبارات فشلت: {len(result.failures)}")
    print(f"⚠️ أخطاء: {len(result.errors)}")
    print(f"📈 معدل النجاح: {((result.testsRun - len(result.failures) - len(result.errors)) / result.testsRun * 100):.1f}%")
    
    if result.failures:
        print("\n❌ الاختبارات الفاشلة:")
        for test, traceback in result.failures:
            print(f"  - {test}: {traceback.split('AssertionError:')[-1].strip()}")
    
    if result.errors:
        print("\n⚠️ الأخطاء:")
        for test, traceback in result.errors:
            print(f"  - {test}: {traceback.split('Exception:')[-1].strip()}")
    
    return result.wasSuccessful()

if __name__ == "__main__":
    success = run_all_tests()
    
    if success:
        print("\n🎉 جميع الاختبارات نجحت! المميزات المتقدمة جاهزة للاستخدام.")
    else:
        print("\n⚠️ بعض الاختبارات فشلت. يرجى مراجعة الأخطاء أعلاه.")
    
    input("\nاضغط Enter للخروج...")
