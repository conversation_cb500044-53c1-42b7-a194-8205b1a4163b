@echo off
echo ========================================
echo       مدير التحميل - Download Manager
echo ========================================
echo.
echo جاري تشغيل البرنامج...
echo Starting the application...
echo.

REM التحقق من وجود Python
python --version >nul 2>&1
if errorlevel 1 (
    echo خطأ: Python غير مثبت على النظام
    echo Error: Python is not installed
    pause
    exit /b 1
)

REM تثبيت المتطلبات إذا لزم الأمر
echo تحقق من المتطلبات...
echo Checking requirements...
pip install -r requirements.txt >nul 2>&1

REM تشغيل البرنامج
echo تشغيل البرنامج...
echo Running the application...
python main.py

REM في حالة إغلاق البرنامج
echo.
echo تم إغلاق البرنامج
echo Application closed
pause
