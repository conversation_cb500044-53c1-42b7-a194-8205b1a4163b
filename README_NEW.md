# 🚀 Internet Download Manager - م<PERSON>ير التحميل المتقدم

برنامج تحميل احترافي متقدم مكتوب بـ Python مع واجهة مستخدم رسومية تشبه Internet Download Manager الشهير، مع مميزات حديثة ومتقدمة.

## ✨ المميزات الأساسية

- 📥 **تحميل متقدم** مع دعم التحميل المتوازي (حتى 16 اتصال)
- 🔄 **تحميلات متعددة** (حتى 10 ملفات في نفس الوقت)
- ⏸️ **إيقاف واستئناف ذكي** مع حفظ الحالة
- 📊 **إحصائيات مفصلة** للسرعة والتقدم
- 📋 **سجل شامل** مع تفاصيل كاملة
- 🎯 **نظام أولويات** للتحميلات (منخفضة، عادية، عالية، عاجلة)
- ⏰ **جدولة متقدمة** للتحميلات (فوري، مؤجل، مجدول، متكرر)

## 🎨 المميزات الجديدة

### نظام الثيمات المتقدم
- 🧡 **ثيم البرتقالي الداكن** (الافتراضي الجديد)
- 🟠 **ثيم البرتقالي الفاتح**
- 🔵 **ثيم IDM الكلاسيكي**
- ✅ **36 لون مختلف** لكل ثيم
- 🎨 **تطبيق فوري** للثيمات

### دعم اللغات المتعددة
- 🇸🇦 **العربية** (الافتراضية)
- 🇺🇸 **الإنجليزية** 
- 🌐 **تغيير فوري** للغة
- 📝 **ترجمة شاملة** لجميع العناصر

### نظام الحذف المتقدم
- 🗑️ **سلة محذوفات آمنة**
- ♻️ **استعادة العناصر المحذوفة**
- 🧹 **تنظيف تلقائي** (30 يوم)
- 📊 **إحصائيات مفصلة**

## 🖼️ واجهة المستخدم المحسنة

### شريط الأدوات الجديد
- 📥 **تحميل جديد** - إضافة تحميلات جديدة
- ⚙️ **الإعدادات** - إعدادات متقدمة شاملة
- 🎨 **الثيم** - تغيير سريع للثيم
- 🌐 **اللغة** - تغيير سريع للغة
- 🗑️ **سلة المحذوفات** - إدارة العناصر المحذوفة

### قوائم السياق
- **كليك يمين** على التحميلات للخيارات
- **حذف وإزالة** مع تأكيد
- **نسخ معلومات** التحميل
- **إعادة بدء** التحميل

## 🚀 التشغيل السريع

### المتطلبات
```bash
Python >= 3.7
tkinter (مدمج مع Python)
requests >= 2.31.0
tqdm >= 4.65.0
```

### التشغيل
```bash
# التشغيل العادي
python main.py

# اختبار المميزات الجديدة
python test_new_features.py

# اختبار جميع الوظائف
python test_enhanced.py
```

## 📁 هيكل المشروع

```
📦 Internet Download Manager
├── 📄 main.py                    # الملف الرئيسي
├── 📄 downloader.py              # محرك التحميل المتقدم
├── 📄 download_manager.py        # مدير التحميلات المتعددة
├── 📄 scheduler.py               # مجدول التحميلات
├── 📄 theme_manager.py           # مدير الثيمات (جديد)
├── 📄 language_manager.py        # مدير اللغات (جديد)
├── 📄 delete_manager.py          # مدير الحذف (جديد)
├── 📄 advanced_dialogs.py        # النوافذ المتقدمة
├── 📄 trash_dialog.py            # نافذة سلة المحذوفات (جديد)
├── 📄 utils.py                   # أدوات مساعدة
├── 📄 config.py                  # إعدادات التطبيق
├── 📄 launcher.py                # مشغل متعدد الإصدارات
└── 📁 .trash/                    # مجلد سلة المحذوفات
```

## 🎯 كيفية الاستخدام

### 1. تحميل ملف جديد
1. اضغط على **📥 تحميل جديد**
2. أدخل رابط التحميل
3. اختر مجلد الحفظ
4. اضغط **🚀 بدء التحميل**

### 2. تغيير الثيم
1. اضغط على **🎨 الثيم** في شريط الأدوات
2. اختر الثيم المطلوب من القائمة
3. سيتم تطبيق الثيم فوراً

### 3. تغيير اللغة
1. اضغط على **🌐 اللغة** في شريط الأدوات
2. اختر اللغة المطلوبة
3. ستتغير جميع النصوص فوراً

### 4. إدارة سلة المحذوفات
1. اضغط على **🗑️ سلة المحذوفات**
2. تصفح العناصر المحذوفة
3. استعد أو احذف نهائياً حسب الحاجة

## ⚙️ الإعدادات المتقدمة

### إعدادات التحميل
- **عدد الاتصالات المتوازية**: 1-16
- **عدد التحميلات المتزامنة**: 1-10
- **حد عرض النطاق**: قابل للتخصيص
- **إعادة المحاولة التلقائية**: تفعيل/إلغاء

### إعدادات الشبكة
- **مهلة الاتصال**: 5-300 ثانية
- **حجم الجزء**: 1KB-1MB
- **التحقق من SSL**: تفعيل/إلغاء

### إعدادات المظهر
- **اختيار الثيم**: 3 ثيمات متاحة
- **اختيار اللغة**: عربي/إنجليزي
- **نوع الخط**: 5 خطوط متاحة
- **حجم الخط**: 8-16

## 🔧 للمطورين

### إضافة ثيم جديد
```python
from theme_manager import theme_manager

# إضافة ثيم جديد
new_theme = {
    'bg_main': '#your_color',
    'primary': '#your_primary',
    # ... باقي الألوان
}

theme_manager.themes['my_theme'] = new_theme
```

### إضافة لغة جديدة
```python
from language_manager import language_manager

# إضافة ترجمات جديدة
new_language = {
    'name': 'Français',
    'code': 'fr',
    'direction': 'ltr',
    'translations': {
        'app_title': 'Gestionnaire de Téléchargement',
        # ... باقي الترجمات
    }
}

language_manager.languages['fr'] = new_language
```

## 📊 الإحصائيات والأداء

### تحسن الأداء
- **حتى 16x أسرع** للملفات الكبيرة (التحميل المتوازي)
- **حتى 10x أكثر كفاءة** (التحميل المتعدد)
- **استئناف فوري** بدون فقدان البيانات

### الإحصائيات المتاحة
- إجمالي التحميلات (نشطة/مكتملة/فاشلة)
- إجمالي البيانات المحملة
- متوسط السرعة للجلسة
- وقت الجلسة الحالية

## 🧪 الاختبارات

### اختبار المميزات الأساسية
```bash
python test_enhanced.py
```

### اختبار المميزات الجديدة
```bash
python test_new_features.py
```

### نتائج الاختبار الأخيرة
```
✅ جميع الاختبارات نجحت: 12/12 (100%)
✅ المحمل المتقدم: يعمل بنجاح
✅ مدير التحميلات المتعددة: يعمل بنجاح
✅ مجدول التحميلات: يعمل بنجاح
✅ مدير الثيمات: يعمل بنجاح
✅ مدير اللغات: يعمل بنجاح
✅ مدير الحذف: يعمل بنجاح
```

## 🎉 الخلاصة

تم تطوير برنامج مدير التحميل ليصبح **أداة احترافية متكاملة** تضاهي أفضل البرامج التجارية مع:

- 🎨 **ثيمات جميلة** مع البرتقالي الداكن المميز
- 🌐 **دعم متعدد اللغات** للعربية والإنجليزية
- 🗑️ **نظام حذف متقدم** مع سلة محذوفات آمنة
- ⚙️ **إعدادات شاملة** للمظهر والسلوك
- 🖼️ **واجهات محسنة** مع قوائم سياق مفيدة
- 🚀 **أداء فائق** مع التحميل المتوازي والمتعدد

**البرنامج الآن جاهز للاستخدام الاحترافي! 🎊**

---

## 📞 الدعم والمساهمة

- 📖 **الوثائق**: راجع ملفات `ENHANCED_FEATURES.md` و `NEW_FEATURES_SUMMARY.md`
- 🐛 **الأخطاء**: أبلغ عن الأخطاء في قسم Issues
- 💡 **الاقتراحات**: نرحب بالاقتراحات والتحسينات
- 🤝 **المساهمة**: Fork المشروع وأرسل Pull Request

**مع تحيات فريق التطوير** 💙
