import requests
import os
import threading
import time
import hashlib
import json
from urllib.parse import urlparse, unquote
from pathlib import Path
from datetime import datetime
import queue
import concurrent.futures

class AdvancedDownloader:
    def __init__(self):
        self.is_downloading = False
        self.is_paused = False
        self.download_thread = None
        self.total_size = 0
        self.downloaded_size = 0
        self.start_time = 0
        self.pause_time = 0
        self.resume_time = 0

        # معلومات متقدمة
        self.download_id = None
        self.file_hash = None
        self.segments = []
        self.max_connections = 4
        self.chunk_size = 8192
        self.retry_count = 0
        self.max_retries = 3

        # إحصائيات
        self.total_downloaded = 0
        self.session_downloaded = 0
        self.average_speed = 0
        self.peak_speed = 0
        self.speed_history = []

        # دوال الاستدعاء
        self.progress_callback = None
        self.completion_callback = None
        self.error_callback = None
        self.segment_callback = None

        # إعدادات متقدمة
        self.user_agent = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        self.timeout = 30
        self.verify_ssl = True
        self.use_segments = True
        
    def set_callbacks(self, progress_callback=None, completion_callback=None, error_callback=None, segment_callback=None):
        """تعيين دوال الاستدعاء للتقدم والإكمال والأخطاء"""
        self.progress_callback = progress_callback
        self.completion_callback = completion_callback
        self.error_callback = error_callback
        self.segment_callback = segment_callback

    def set_advanced_options(self, max_connections=4, chunk_size=8192, max_retries=3, timeout=30, user_agent=None):
        """تعيين الخيارات المتقدمة"""
        self.max_connections = max_connections
        self.chunk_size = chunk_size
        self.max_retries = max_retries
        self.timeout = timeout
        if user_agent:
            self.user_agent = user_agent

    def generate_download_id(self, url):
        """توليد معرف فريد للتحميل"""
        timestamp = str(int(time.time()))
        url_hash = hashlib.md5(url.encode()).hexdigest()[:8]
        return f"dl_{timestamp}_{url_hash}"
    
    def get_filename_from_url(self, url):
        """استخراج اسم الملف من الرابط"""
        parsed_url = urlparse(url)
        filename = os.path.basename(parsed_url.path)
        if not filename or '.' not in filename:
            filename = "downloaded_file"
        return filename
    
    def get_file_info(self, url):
        """الحصول على معلومات شاملة عن الملف"""
        try:
            headers = {'User-Agent': self.user_agent}
            response = requests.head(url, headers=headers, allow_redirects=True, timeout=self.timeout)

            file_info = {
                'size': 0,
                'supports_range': False,
                'content_type': 'application/octet-stream',
                'last_modified': None,
                'etag': None,
                'server': None,
                'filename': self.get_filename_from_url(url)
            }

            # حجم الملف
            if 'content-length' in response.headers:
                file_info['size'] = int(response.headers['content-length'])

            # دعم التحميل المجزأ
            if 'accept-ranges' in response.headers:
                file_info['supports_range'] = response.headers['accept-ranges'].lower() == 'bytes'

            # نوع المحتوى
            if 'content-type' in response.headers:
                file_info['content_type'] = response.headers['content-type']

            # تاريخ آخر تعديل
            if 'last-modified' in response.headers:
                file_info['last_modified'] = response.headers['last-modified']

            # ETag للتحقق من التكامل
            if 'etag' in response.headers:
                file_info['etag'] = response.headers['etag']

            # معلومات الخادم
            if 'server' in response.headers:
                file_info['server'] = response.headers['server']

            return file_info
        except Exception as e:
            return {'size': 0, 'supports_range': False, 'error': str(e)}

    def get_file_size(self, url):
        """الحصول على حجم الملف (للتوافق مع الكود القديم)"""
        file_info = self.get_file_info(url)
        return file_info.get('size', 0)
    
    def format_size(self, size_bytes):
        """تحويل الحجم إلى وحدة مناسبة"""
        if size_bytes == 0:
            return "0 B"
        size_names = ["B", "KB", "MB", "GB", "TB"]
        i = 0
        while size_bytes >= 1024 and i < len(size_names) - 1:
            size_bytes /= 1024.0
            i += 1
        return f"{size_bytes:.2f} {size_names[i]}"
    
    def calculate_speed(self, downloaded, elapsed_time):
        """حساب سرعة التحميل"""
        if elapsed_time > 0:
            speed = downloaded / elapsed_time
            return self.format_size(speed) + "/s"
        return "0 B/s"
    
    def calculate_eta(self, downloaded, total, elapsed_time):
        """حساب الوقت المتبقي مع تحسينات"""
        if downloaded > 0 and elapsed_time > 0 and total > downloaded:
            # استخدام متوسط السرعة للدقة
            if len(self.speed_history) > 0:
                avg_speed = sum(self.speed_history[-10:]) / len(self.speed_history[-10:])
            else:
                avg_speed = downloaded / elapsed_time

            remaining = total - downloaded
            eta_seconds = remaining / avg_speed if avg_speed > 0 else 0

            if eta_seconds < 60:
                return f"{int(eta_seconds)} ثانية"
            elif eta_seconds < 3600:
                return f"{int(eta_seconds / 60)} دقيقة"
            else:
                hours = int(eta_seconds / 3600)
                minutes = int((eta_seconds % 3600) / 60)
                return f"{hours}:{minutes:02d} ساعة"
        return "غير محدد"

    def create_download_segments(self, file_size, num_segments=None):
        """إنشاء مقاطع التحميل للتحميل المتوازي"""
        if not num_segments:
            num_segments = min(self.max_connections, 8)  # حد أقصى 8 مقاطع

        if file_size < 1024 * 1024:  # أقل من 1 ميجا، لا نحتاج تقسيم
            return [(0, file_size - 1)]

        segment_size = file_size // num_segments
        segments = []

        for i in range(num_segments):
            start = i * segment_size
            end = start + segment_size - 1

            # المقطع الأخير يأخذ الباقي
            if i == num_segments - 1:
                end = file_size - 1

            segments.append((start, end))

        return segments

    def download_segment(self, url, start, end, segment_id, temp_file):
        """تحميل مقطع واحد من الملف"""
        try:
            headers = {
                'User-Agent': self.user_agent,
                'Range': f'bytes={start}-{end}'
            }

            response = requests.get(url, headers=headers, stream=True, timeout=self.timeout)
            response.raise_for_status()

            downloaded = 0
            with open(temp_file, 'wb') as f:
                for chunk in response.iter_content(chunk_size=self.chunk_size):
                    if not self.is_downloading:
                        break

                    while self.is_paused:
                        time.sleep(0.1)
                        if not self.is_downloading:
                            break

                    if chunk:
                        f.write(chunk)
                        downloaded += len(chunk)

                        if self.segment_callback:
                            self.segment_callback(segment_id, downloaded, end - start + 1)

            return True
        except Exception as e:
            if self.error_callback:
                self.error_callback(f"خطأ في المقطع {segment_id}: {str(e)}")
            return False
    
    def download_file_advanced(self, url, save_path, filename=None):
        """تحميل متقدم مع دعم التحميل المجزأ والاستئناف"""
        try:
            if filename is None:
                filename = self.get_filename_from_url(url)

            full_path = os.path.join(save_path, filename)
            self.download_id = self.generate_download_id(url)

            # الحصول على معلومات الملف
            file_info = self.get_file_info(url)
            self.total_size = file_info.get('size', 0)

            if self.total_size == 0:
                raise Exception("لا يمكن تحديد حجم الملف")

            # التحقق من إمكانية الاستئناف
            existing_size = 0
            if os.path.exists(full_path):
                existing_size = os.path.getsize(full_path)
                if existing_size >= self.total_size:
                    if self.completion_callback:
                        self.completion_callback(full_path)
                    return

            self.downloaded_size = existing_size
            self.start_time = time.time()
            self.is_downloading = True

            # اختيار طريقة التحميل
            if file_info.get('supports_range') and self.use_segments and self.total_size > 1024 * 1024:
                # تحميل مجزأ
                success = self.download_with_segments(url, full_path, existing_size)
            else:
                # تحميل عادي مع استئناف
                success = self.download_single_stream(url, full_path, existing_size)

            if success and self.is_downloading:
                # التحقق من تكامل الملف
                if self.verify_download(full_path):
                    if self.completion_callback:
                        self.completion_callback(full_path)
                else:
                    raise Exception("فشل في التحقق من تكامل الملف")

        except Exception as e:
            if self.error_callback:
                self.error_callback(str(e))
        finally:
            self.is_downloading = False
            self.is_paused = False

    def download_with_segments(self, url, full_path, existing_size):
        """تحميل مجزأ متوازي"""
        try:
            remaining_size = self.total_size - existing_size
            segments = self.create_download_segments(remaining_size)

            # إنشاء ملفات مؤقتة للمقاطع
            temp_files = []
            for i, (start, end) in enumerate(segments):
                temp_file = f"{full_path}.part{i}"
                temp_files.append(temp_file)

            # تحميل المقاطع بالتوازي
            with concurrent.futures.ThreadPoolExecutor(max_workers=self.max_connections) as executor:
                futures = []
                for i, (start, end) in enumerate(segments):
                    adjusted_start = start + existing_size
                    adjusted_end = end + existing_size
                    future = executor.submit(self.download_segment, url, adjusted_start, adjusted_end, i, temp_files[i])
                    futures.append(future)

                # انتظار اكتمال جميع المقاطع
                for future in concurrent.futures.as_completed(futures):
                    if not future.result():
                        return False

            # دمج المقاطع
            self.merge_segments(full_path, temp_files, existing_size > 0)

            # حذف الملفات المؤقتة
            for temp_file in temp_files:
                if os.path.exists(temp_file):
                    os.remove(temp_file)

            return True

        except Exception as e:
            if self.error_callback:
                self.error_callback(f"خطأ في التحميل المجزأ: {str(e)}")
            return False

    def download_single_stream(self, url, full_path, existing_size):
        """تحميل عادي مع استئناف"""
        try:
            headers = {'User-Agent': self.user_agent}
            if existing_size > 0:
                headers['Range'] = f'bytes={existing_size}-'

            response = requests.get(url, headers=headers, stream=True, timeout=self.timeout)
            response.raise_for_status()

            mode = 'ab' if existing_size > 0 else 'wb'

            with open(full_path, mode) as file:
                for chunk in response.iter_content(chunk_size=self.chunk_size):
                    if not self.is_downloading:
                        break

                    while self.is_paused:
                        time.sleep(0.1)
                        if not self.is_downloading:
                            break

                    if chunk:
                        file.write(chunk)
                        self.downloaded_size += len(chunk)
                        self.update_progress()

            return True

        except Exception as e:
            if self.error_callback:
                self.error_callback(f"خطأ في التحميل: {str(e)}")
            return False

    def merge_segments(self, full_path, temp_files, append_mode=False):
        """دمج مقاطع الملف"""
        mode = 'ab' if append_mode else 'wb'
        with open(full_path, mode) as output_file:
            for temp_file in temp_files:
                if os.path.exists(temp_file):
                    with open(temp_file, 'rb') as input_file:
                        output_file.write(input_file.read())

    def verify_download(self, file_path):
        """التحقق من تكامل التحميل"""
        try:
            actual_size = os.path.getsize(file_path)
            return actual_size == self.total_size
        except:
            return False

    def update_progress(self):
        """تحديث معلومات التقدم"""
        if self.progress_callback:
            current_time = time.time()
            elapsed_time = current_time - self.start_time

            # حساب السرعة الحالية
            current_speed = self.downloaded_size / elapsed_time if elapsed_time > 0 else 0
            self.speed_history.append(current_speed)

            # الاحتفاظ بآخر 20 قراءة فقط
            if len(self.speed_history) > 20:
                self.speed_history.pop(0)

            # تحديث الإحصائيات
            self.average_speed = sum(self.speed_history) / len(self.speed_history)
            self.peak_speed = max(self.speed_history)

            progress_data = {
                'downloaded': self.downloaded_size,
                'total': self.total_size,
                'percentage': (self.downloaded_size / self.total_size * 100) if self.total_size > 0 else 0,
                'speed': self.calculate_speed(self.downloaded_size, elapsed_time),
                'average_speed': self.format_size(self.average_speed) + "/s",
                'peak_speed': self.format_size(self.peak_speed) + "/s",
                'eta': self.calculate_eta(self.downloaded_size, self.total_size, elapsed_time),
                'downloaded_formatted': self.format_size(self.downloaded_size),
                'total_formatted': self.format_size(self.total_size),
                'download_id': self.download_id,
                'segments_count': len(self.segments) if self.segments else 1
            }
            self.progress_callback(progress_data)

    def download_file(self, url, save_path, filename=None):
        """تحميل الملف (للتوافق مع الكود القديم)"""
        return self.download_file_advanced(url, save_path, filename)
    
    def start_download(self, url, save_path, filename=None):
        """بدء التحميل في خيط منفصل مع إعادة المحاولة"""
        if self.is_downloading:
            return False

        self.retry_count = 0
        self.download_thread = threading.Thread(
            target=self.download_with_retry,
            args=(url, save_path, filename)
        )
        self.download_thread.daemon = True
        self.download_thread.start()
        return True

    def download_with_retry(self, url, save_path, filename=None):
        """تحميل مع إعادة المحاولة عند الفشل"""
        while self.retry_count < self.max_retries:
            try:
                self.download_file_advanced(url, save_path, filename)
                break  # نجح التحميل
            except Exception as e:
                self.retry_count += 1
                if self.retry_count < self.max_retries:
                    if self.error_callback:
                        self.error_callback(f"المحاولة {self.retry_count} فشلت، إعادة المحاولة... ({str(e)})")
                    time.sleep(2 ** self.retry_count)  # انتظار متزايد
                else:
                    if self.error_callback:
                        self.error_callback(f"فشل التحميل بعد {self.max_retries} محاولات: {str(e)}")

    def get_download_statistics(self):
        """الحصول على إحصائيات التحميل"""
        current_time = time.time()
        elapsed_time = current_time - self.start_time if self.start_time > 0 else 0

        stats = {
            'download_id': self.download_id,
            'total_size': self.total_size,
            'downloaded_size': self.downloaded_size,
            'remaining_size': self.total_size - self.downloaded_size,
            'percentage': (self.downloaded_size / self.total_size * 100) if self.total_size > 0 else 0,
            'elapsed_time': elapsed_time,
            'current_speed': self.calculate_speed(self.downloaded_size, elapsed_time),
            'average_speed': self.format_size(self.average_speed) + "/s" if self.average_speed > 0 else "0 B/s",
            'peak_speed': self.format_size(self.peak_speed) + "/s" if self.peak_speed > 0 else "0 B/s",
            'eta': self.calculate_eta(self.downloaded_size, self.total_size, elapsed_time),
            'retry_count': self.retry_count,
            'is_downloading': self.is_downloading,
            'is_paused': self.is_paused,
            'segments_count': len(self.segments) if self.segments else 1,
            'connections_used': self.max_connections
        }

        return stats

    def save_download_state(self, state_file):
        """حفظ حالة التحميل للاستئناف لاحقاً"""
        try:
            state = {
                'download_id': self.download_id,
                'url': getattr(self, 'current_url', ''),
                'save_path': getattr(self, 'current_save_path', ''),
                'filename': getattr(self, 'current_filename', ''),
                'total_size': self.total_size,
                'downloaded_size': self.downloaded_size,
                'timestamp': datetime.now().isoformat(),
                'segments': self.segments,
                'file_hash': self.file_hash
            }

            with open(state_file, 'w', encoding='utf-8') as f:
                json.dump(state, f, ensure_ascii=False, indent=2)

            return True
        except Exception as e:
            if self.error_callback:
                self.error_callback(f"فشل في حفظ حالة التحميل: {str(e)}")
            return False

    def load_download_state(self, state_file):
        """تحميل حالة التحميل المحفوظة"""
        try:
            with open(state_file, 'r', encoding='utf-8') as f:
                state = json.load(f)

            self.download_id = state.get('download_id')
            self.total_size = state.get('total_size', 0)
            self.downloaded_size = state.get('downloaded_size', 0)
            self.segments = state.get('segments', [])
            self.file_hash = state.get('file_hash')

            return state
        except Exception as e:
            if self.error_callback:
                self.error_callback(f"فشل في تحميل حالة التحميل: {str(e)}")
            return None

    def calculate_file_hash(self, file_path, algorithm='md5'):
        """حساب hash للملف للتحقق من التكامل"""
        try:
            hash_func = hashlib.new(algorithm)
            with open(file_path, 'rb') as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    hash_func.update(chunk)
            return hash_func.hexdigest()
        except Exception as e:
            if self.error_callback:
                self.error_callback(f"فشل في حساب hash: {str(e)}")
            return None

    def estimate_time_remaining(self):
        """تقدير الوقت المتبقي بدقة أكبر"""
        if not self.speed_history or self.total_size <= self.downloaded_size:
            return "غير محدد"

        # استخدام متوسط السرعة للثواني الأخيرة
        recent_speeds = self.speed_history[-5:] if len(self.speed_history) >= 5 else self.speed_history
        avg_speed = sum(recent_speeds) / len(recent_speeds)

        if avg_speed <= 0:
            return "غير محدد"

        remaining_bytes = self.total_size - self.downloaded_size
        eta_seconds = remaining_bytes / avg_speed

        return self.format_time_duration(eta_seconds)

    def format_time_duration(self, seconds):
        """تنسيق مدة زمنية بالثواني"""
        if seconds < 0:
            return "غير محدد"

        if seconds < 60:
            return f"{int(seconds)} ثانية"
        elif seconds < 3600:
            minutes = int(seconds / 60)
            secs = int(seconds % 60)
            return f"{minutes}:{secs:02d} دقيقة"
        elif seconds < 86400:
            hours = int(seconds / 3600)
            minutes = int((seconds % 3600) / 60)
            return f"{hours}:{minutes:02d} ساعة"
        else:
            days = int(seconds / 86400)
            hours = int((seconds % 86400) / 3600)
            return f"{days} يوم و {hours} ساعة"
    
    def pause_download(self):
        """إيقاف التحميل مؤقتاً"""
        self.is_paused = True
    
    def resume_download(self):
        """استئناف التحميل"""
        self.is_paused = False
    
    def stop_download(self):
        """إيقاف التحميل نهائياً"""
        self.is_downloading = False
        self.is_paused = False
    
    def is_valid_url(self, url):
        """التحقق من صحة الرابط"""
        try:
            result = urlparse(url)
            return all([result.scheme, result.netloc])
        except:
            return False

# فئة للتوافق مع الكود القديم
class Downloader(AdvancedDownloader):
    """فئة التحميل الأساسية للتوافق مع الكود القديم"""

    def __init__(self):
        super().__init__()
        # إعدادات أبسط للإصدار الأساسي
        self.use_segments = False
        self.max_connections = 1
