#!/usr/bin/env python3
"""
اختبار الوظائف المحسنة - Enhanced Features Test
اختبار سريع للتأكد من عمل جميع الوظائف الجديدة
"""

import sys
import os
import time
from datetime import datetime, timed<PERSON><PERSON>

def test_advanced_downloader():
    """اختبار المحمل المتقدم"""
    print("🔧 اختبار المحمل المتقدم...")
    
    try:
        from downloader import AdvancedDownloader
        
        downloader = AdvancedDownloader()
        
        # اختبار الإعدادات المتقدمة
        downloader.set_advanced_options(
            max_connections=4,
            chunk_size=8192,
            max_retries=3,
            timeout=30
        )
        
        # اختبار فحص الرابط
        test_url = "https://httpbin.org/json"
        if downloader.is_valid_url(test_url):
            print("✅ فحص الرابط يعمل")
            
            # اختبار الحصول على معلومات الملف
            file_info = downloader.get_file_info(test_url)
            if file_info:
                print(f"✅ معلومات الملف: {file_info.get('content_type', 'غير محدد')}")
            else:
                print("⚠️ لا يمكن الحصول على معلومات الملف")
        else:
            print("❌ فحص الرابط فشل")
        
        # اختبار الإحصائيات
        stats = downloader.get_download_statistics()
        print(f"✅ الإحصائيات: {len(stats)} عنصر")
        
        print("✅ المحمل المتقدم يعمل بنجاح!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في المحمل المتقدم: {e}")
        return False

def test_download_manager():
    """اختبار مدير التحميلات المتعددة"""
    print("\n📋 اختبار مدير التحميلات المتعددة...")
    
    try:
        from download_manager import MultiDownloadManager, DownloadPriority
        
        # إنشاء مدير بدون بدء تلقائي للاختبار
        manager = MultiDownloadManager(max_concurrent=2, auto_start=False)
        
        # اختبار إضافة تحميل
        download_id = manager.add_download(
            "https://httpbin.org/json",
            os.path.expanduser("~/Downloads"),
            "test_file.json",
            DownloadPriority.HIGH
        )
        
        if download_id:
            print(f"✅ تم إضافة تحميل: {download_id}")
            
            # اختبار الحصول على معلومات التحميل
            info = manager.get_download_info(download_id)
            if info:
                print(f"✅ معلومات التحميل: {info['priority']}")
            
            # اختبار الإحصائيات
            stats = manager.get_statistics()
            print(f"✅ إحصائيات المدير: {stats['total_downloads']} تحميل")
            
            # اختبار إلغاء التحميل
            if manager.cancel_download(download_id):
                print("✅ تم إلغاء التحميل")
        
        print("✅ مدير التحميلات المتعددة يعمل بنجاح!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في مدير التحميلات: {e}")
        return False

def test_scheduler():
    """اختبار مجدول التحميلات"""
    print("\n⏰ اختبار مجدول التحميلات...")
    
    try:
        from scheduler import DownloadScheduler, ScheduleType
        from download_manager import MultiDownloadManager, DownloadPriority
        
        # إنشاء مدير ومجدول
        manager = MultiDownloadManager(auto_start=False)
        scheduler = DownloadScheduler(manager)
        
        # اختبار إضافة تحميل مجدول
        schedule_id = scheduler.add_scheduled_download(
            "https://httpbin.org/json",
            os.path.expanduser("~/Downloads"),
            "scheduled_test.json",
            DownloadPriority.NORMAL
        )
        
        if schedule_id:
            print(f"✅ تم إضافة تحميل مجدول: {schedule_id}")
            
            # اختبار جدولة مؤجلة
            if scheduler.schedule_delayed(schedule_id, 1):  # دقيقة واحدة
                print("✅ تم تعيين جدولة مؤجلة")
            
            # اختبار الحصول على التحميلات المجدولة
            scheduled = scheduler.get_scheduled_downloads()
            print(f"✅ التحميلات المجدولة: {len(scheduled)}")
            
            # اختبار الإحصائيات
            stats = scheduler.get_statistics()
            print(f"✅ إحصائيات المجدول: {stats['total_scheduled']} مجدول")
            
            # اختبار إلغاء التحميل المجدول
            if scheduler.cancel_scheduled_download(schedule_id):
                print("✅ تم إلغاء التحميل المجدول")
        
        print("✅ مجدول التحميلات يعمل بنجاح!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في المجدول: {e}")
        return False

def test_advanced_dialogs():
    """اختبار النوافذ المتقدمة"""
    print("\n🖼️ اختبار النوافذ المتقدمة...")
    
    try:
        # اختبار استيراد النوافذ
        from advanced_dialogs import AdvancedDownloadDialog, AdvancedSettingsDialog
        print("✅ تم استيراد النوافذ المتقدمة بنجاح")
        
        # يمكن إضافة اختبارات أخرى هنا بدون فتح النوافذ فعلياً
        print("✅ النوافذ المتقدمة جاهزة للاستخدام!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في النوافذ المتقدمة: {e}")
        return False

def test_file_operations():
    """اختبار عمليات الملفات"""
    print("\n📁 اختبار عمليات الملفات...")
    
    try:
        from utils import FileUtils, URLUtils, ValidationUtils
        
        # اختبار تنسيق الحجم
        size_test = FileUtils.get_file_size_formatted(1024 * 1024)
        print(f"✅ تنسيق الحجم: {size_test}")
        
        # اختبار تنظيف اسم الملف
        clean_name = FileUtils.sanitize_filename("test<file>name.txt")
        print(f"✅ تنظيف اسم الملف: {clean_name}")
        
        # اختبار فحص الرابط
        url_valid = URLUtils.is_valid_url("https://example.com")
        print(f"✅ فحص الرابط: {'صحيح' if url_valid else 'خطأ'}")
        
        # اختبار فحص المسار
        path_valid, msg = ValidationUtils.validate_download_path(os.path.expanduser("~/Downloads"))
        print(f"✅ فحص المسار: {'صحيح' if path_valid else 'خطأ'}")
        
        print("✅ عمليات الملفات تعمل بنجاح!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في عمليات الملفات: {e}")
        return False

def test_configuration():
    """اختبار الإعدادات"""
    print("\n⚙️ اختبار الإعدادات...")
    
    try:
        from config import Config
        
        # اختبار الحصول على مجلد التحميل
        download_folder = Config.get_download_folder()
        print(f"✅ مجلد التحميل: {download_folder}")
        
        # اختبار فحص نوع الملف
        is_supported = Config.is_supported_file("test.pdf")
        print(f"✅ دعم نوع الملف: {'مدعوم' if is_supported else 'غير مدعوم'}")
        
        # اختبار تنظيف اسم الملف
        safe_name = Config.get_safe_filename("test<>file.txt")
        print(f"✅ اسم ملف آمن: {safe_name}")
        
        print("✅ الإعدادات تعمل بنجاح!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في الإعدادات: {e}")
        return False

def main():
    """الدالة الرئيسية للاختبار"""
    print("🧪 بدء اختبار الوظائف المحسنة")
    print("=" * 50)
    
    tests = [
        ("المحمل المتقدم", test_advanced_downloader),
        ("مدير التحميلات المتعددة", test_download_manager),
        ("مجدول التحميلات", test_scheduler),
        ("النوافذ المتقدمة", test_advanced_dialogs),
        ("عمليات الملفات", test_file_operations),
        ("الإعدادات", test_configuration)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"❌ خطأ في اختبار {test_name}: {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 نتائج الاختبار: {passed}/{total} نجح")
    
    if passed == total:
        print("🎉 جميع الاختبارات نجحت! البرنامج جاهز للاستخدام.")
    else:
        print("⚠️ بعض الاختبارات فشلت. راجع الأخطاء أعلاه.")
    
    print("\n🚀 يمكنك الآن تشغيل البرنامج باستخدام:")
    print("   python main.py")
    print("   أو")
    print("   python launcher.py")

if __name__ == "__main__":
    main()
