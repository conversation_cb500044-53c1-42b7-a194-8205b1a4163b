"""
نظام النسخ الاحتياطي والاستعادة - Backup & Restore System
نظام شامل للنسخ الاحتياطي واستعادة البيانات والإعدادات
"""

import os
import json
import shutil
import zipfile
import threading
import time
from datetime import datetime, timedelta
from pathlib import Path
import hashlib
from language_manager import language_manager

class BackupSystem:
    """نظام النسخ الاحتياطي والاستعادة"""
    
    def __init__(self):
        self.backup_folder = Path("backups")
        self.auto_backup_enabled = True
        self.backup_interval_hours = 24  # نسخة احتياطية كل 24 ساعة
        self.max_backups = 10  # الحد الأقصى للنسخ الاحتياطية
        self.compression_enabled = True
        
        # إنشاء مجلد النسخ الاحتياطية
        self.backup_folder.mkdir(exist_ok=True)
        
        # ملفات النسخ الاحتياطي
        self.backup_files = [
            'download_history.json',
            'settings.json',
            'analytics_data.json',
            'accelerator_learning.json',
            'theme_settings.json',
            'language_settings.json',
            'deleted_items.json'
        ]
        
        # مجلدات النسخ الاحتياطي
        self.backup_folders = [
            '.trash'
        ]
        
        # بدء النسخ الاحتياطي التلقائي
        self.start_auto_backup()
    
    def start_auto_backup(self):
        """بدء النسخ الاحتياطي التلقائي"""
        if not self.auto_backup_enabled:
            return
        
        def backup_loop():
            while self.auto_backup_enabled:
                try:
                    # فحص آخر نسخة احتياطية
                    last_backup = self.get_last_backup_time()
                    now = datetime.now()
                    
                    if not last_backup or (now - last_backup).total_seconds() >= self.backup_interval_hours * 3600:
                        self.create_automatic_backup()
                    
                    # انتظار ساعة قبل الفحص التالي
                    time.sleep(3600)
                    
                except Exception as e:
                    print(f"خطأ في النسخ الاحتياطي التلقائي: {e}")
                    time.sleep(3600)
        
        backup_thread = threading.Thread(target=backup_loop, daemon=True)
        backup_thread.start()
    
    def create_backup(self, backup_name=None, include_downloads=True, include_settings=True, include_analytics=True):
        """إنشاء نسخة احتياطية"""
        try:
            # تحديد اسم النسخة الاحتياطية
            if not backup_name:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                backup_name = f"backup_{timestamp}"
            
            backup_path = self.backup_folder / f"{backup_name}.zip"
            
            # إنشاء النسخة الاحتياطية
            with zipfile.ZipFile(backup_path, 'w', zipfile.ZIP_DEFLATED if self.compression_enabled else zipfile.ZIP_STORED) as zipf:
                
                # نسخ الملفات
                files_backed_up = 0
                
                if include_downloads:
                    files_backed_up += self.backup_download_data(zipf)
                
                if include_settings:
                    files_backed_up += self.backup_settings_data(zipf)
                
                if include_analytics:
                    files_backed_up += self.backup_analytics_data(zipf)
                
                # نسخ المجلدات
                folders_backed_up = self.backup_folders_data(zipf)
                
                # إضافة معلومات النسخة الاحتياطية
                backup_info = {
                    'created_at': datetime.now().isoformat(),
                    'backup_name': backup_name,
                    'files_count': files_backed_up,
                    'folders_count': folders_backed_up,
                    'include_downloads': include_downloads,
                    'include_settings': include_settings,
                    'include_analytics': include_analytics,
                    'app_version': '1.0.0',
                    'backup_version': '1.0'
                }
                
                zipf.writestr('backup_info.json', json.dumps(backup_info, ensure_ascii=False, indent=2))
            
            # تنظيف النسخ القديمة
            self.cleanup_old_backups()
            
            return backup_path, backup_info
            
        except Exception as e:
            print(f"خطأ في إنشاء النسخة الاحتياطية: {e}")
            return None, None
    
    def backup_download_data(self, zipf):
        """نسخ بيانات التحميلات"""
        files_count = 0
        
        download_files = [
            'download_history.json',
            'deleted_items.json'
        ]
        
        for filename in download_files:
            if os.path.exists(filename):
                zipf.write(filename, f"downloads/{filename}")
                files_count += 1
        
        return files_count
    
    def backup_settings_data(self, zipf):
        """نسخ بيانات الإعدادات"""
        files_count = 0
        
        settings_files = [
            'settings.json',
            'theme_settings.json',
            'language_settings.json'
        ]
        
        for filename in settings_files:
            if os.path.exists(filename):
                zipf.write(filename, f"settings/{filename}")
                files_count += 1
        
        return files_count
    
    def backup_analytics_data(self, zipf):
        """نسخ بيانات التحليلات"""
        files_count = 0
        
        analytics_files = [
            'analytics_data.json',
            'accelerator_learning.json'
        ]
        
        for filename in analytics_files:
            if os.path.exists(filename):
                zipf.write(filename, f"analytics/{filename}")
                files_count += 1
        
        return files_count
    
    def backup_folders_data(self, zipf):
        """نسخ المجلدات"""
        folders_count = 0
        
        for folder_name in self.backup_folders:
            folder_path = Path(folder_name)
            if folder_path.exists() and folder_path.is_dir():
                for file_path in folder_path.rglob('*'):
                    if file_path.is_file():
                        arcname = f"folders/{folder_name}/{file_path.relative_to(folder_path)}"
                        zipf.write(file_path, arcname)
                folders_count += 1
        
        return folders_count
    
    def create_automatic_backup(self):
        """إنشاء نسخة احتياطية تلقائية"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_name = f"auto_backup_{timestamp}"
            
            backup_path, backup_info = self.create_backup(backup_name)
            
            if backup_path:
                print(f"تم إنشاء نسخة احتياطية تلقائية: {backup_path}")
                return True
            
            return False
            
        except Exception as e:
            print(f"خطأ في النسخة الاحتياطية التلقائية: {e}")
            return False
    
    def restore_backup(self, backup_path, restore_downloads=True, restore_settings=True, restore_analytics=True):
        """استعادة نسخة احتياطية"""
        try:
            backup_path = Path(backup_path)
            
            if not backup_path.exists():
                return False, "ملف النسخة الاحتياطية غير موجود"
            
            # إنشاء نسخة احتياطية من الحالة الحالية قبل الاستعادة
            current_backup_name = f"before_restore_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            self.create_backup(current_backup_name)
            
            restored_files = 0
            
            with zipfile.ZipFile(backup_path, 'r') as zipf:
                
                # قراءة معلومات النسخة الاحتياطية
                try:
                    backup_info_data = zipf.read('backup_info.json')
                    backup_info = json.loads(backup_info_data.decode('utf-8'))
                except:
                    backup_info = {}
                
                # استعادة بيانات التحميلات
                if restore_downloads and backup_info.get('include_downloads', True):
                    restored_files += self.restore_download_data(zipf)
                
                # استعادة الإعدادات
                if restore_settings and backup_info.get('include_settings', True):
                    restored_files += self.restore_settings_data(zipf)
                
                # استعادة بيانات التحليلات
                if restore_analytics and backup_info.get('include_analytics', True):
                    restored_files += self.restore_analytics_data(zipf)
                
                # استعادة المجلدات
                restored_files += self.restore_folders_data(zipf)
            
            return True, f"تم استعادة {restored_files} ملف بنجاح"
            
        except Exception as e:
            return False, f"خطأ في الاستعادة: {str(e)}"
    
    def restore_download_data(self, zipf):
        """استعادة بيانات التحميلات"""
        restored_count = 0
        
        download_files = [
            'downloads/download_history.json',
            'downloads/deleted_items.json'
        ]
        
        for archive_path in download_files:
            try:
                if archive_path in zipf.namelist():
                    filename = os.path.basename(archive_path)
                    data = zipf.read(archive_path)
                    
                    with open(filename, 'wb') as f:
                        f.write(data)
                    
                    restored_count += 1
            except Exception as e:
                print(f"خطأ في استعادة {archive_path}: {e}")
        
        return restored_count
    
    def restore_settings_data(self, zipf):
        """استعادة بيانات الإعدادات"""
        restored_count = 0
        
        settings_files = [
            'settings/settings.json',
            'settings/theme_settings.json',
            'settings/language_settings.json'
        ]
        
        for archive_path in settings_files:
            try:
                if archive_path in zipf.namelist():
                    filename = os.path.basename(archive_path)
                    data = zipf.read(archive_path)
                    
                    with open(filename, 'wb') as f:
                        f.write(data)
                    
                    restored_count += 1
            except Exception as e:
                print(f"خطأ في استعادة {archive_path}: {e}")
        
        return restored_count
    
    def restore_analytics_data(self, zipf):
        """استعادة بيانات التحليلات"""
        restored_count = 0
        
        analytics_files = [
            'analytics/analytics_data.json',
            'analytics/accelerator_learning.json'
        ]
        
        for archive_path in analytics_files:
            try:
                if archive_path in zipf.namelist():
                    filename = os.path.basename(archive_path)
                    data = zipf.read(archive_path)
                    
                    with open(filename, 'wb') as f:
                        f.write(data)
                    
                    restored_count += 1
            except Exception as e:
                print(f"خطأ في استعادة {archive_path}: {e}")
        
        return restored_count
    
    def restore_folders_data(self, zipf):
        """استعادة المجلدات"""
        restored_count = 0
        
        for archive_path in zipf.namelist():
            if archive_path.startswith('folders/'):
                try:
                    # استخراج المسار النسبي
                    relative_path = archive_path[8:]  # إزالة 'folders/'
                    
                    # إنشاء المجلدات إذا لم تكن موجودة
                    file_path = Path(relative_path)
                    file_path.parent.mkdir(parents=True, exist_ok=True)
                    
                    # استخراج الملف
                    data = zipf.read(archive_path)
                    with open(file_path, 'wb') as f:
                        f.write(data)
                    
                    restored_count += 1
                    
                except Exception as e:
                    print(f"خطأ في استعادة {archive_path}: {e}")
        
        return restored_count
    
    def get_available_backups(self):
        """الحصول على النسخ الاحتياطية المتاحة"""
        backups = []
        
        for backup_file in self.backup_folder.glob("*.zip"):
            try:
                # قراءة معلومات النسخة الاحتياطية
                with zipfile.ZipFile(backup_file, 'r') as zipf:
                    if 'backup_info.json' in zipf.namelist():
                        backup_info_data = zipf.read('backup_info.json')
                        backup_info = json.loads(backup_info_data.decode('utf-8'))
                    else:
                        # نسخة احتياطية قديمة بدون معلومات
                        backup_info = {
                            'created_at': datetime.fromtimestamp(backup_file.stat().st_mtime).isoformat(),
                            'backup_name': backup_file.stem,
                            'files_count': len(zipf.namelist()),
                            'folders_count': 0
                        }
                
                backup_info['file_path'] = str(backup_file)
                backup_info['file_size'] = backup_file.stat().st_size
                backup_info['file_size_formatted'] = self.format_size(backup_info['file_size'])
                
                backups.append(backup_info)
                
            except Exception as e:
                print(f"خطأ في قراءة النسخة الاحتياطية {backup_file}: {e}")
        
        # ترتيب حسب التاريخ (الأحدث أولاً)
        backups.sort(key=lambda x: x['created_at'], reverse=True)
        
        return backups
    
    def delete_backup(self, backup_path):
        """حذف نسخة احتياطية"""
        try:
            backup_path = Path(backup_path)
            if backup_path.exists():
                backup_path.unlink()
                return True
            return False
        except Exception as e:
            print(f"خطأ في حذف النسخة الاحتياطية: {e}")
            return False
    
    def cleanup_old_backups(self):
        """تنظيف النسخ الاحتياطية القديمة"""
        try:
            backups = self.get_available_backups()
            
            # حذف النسخ الزائدة عن الحد الأقصى
            if len(backups) > self.max_backups:
                backups_to_delete = backups[self.max_backups:]
                
                for backup in backups_to_delete:
                    self.delete_backup(backup['file_path'])
                    print(f"تم حذف النسخة الاحتياطية القديمة: {backup['backup_name']}")
        
        except Exception as e:
            print(f"خطأ في تنظيف النسخ الاحتياطية: {e}")
    
    def get_last_backup_time(self):
        """الحصول على وقت آخر نسخة احتياطية"""
        try:
            backups = self.get_available_backups()
            if backups:
                return datetime.fromisoformat(backups[0]['created_at'])
            return None
        except Exception as e:
            print(f"خطأ في الحصول على وقت آخر نسخة احتياطية: {e}")
            return None
    
    def verify_backup(self, backup_path):
        """التحقق من سلامة النسخة الاحتياطية"""
        try:
            backup_path = Path(backup_path)
            
            if not backup_path.exists():
                return False, "الملف غير موجود"
            
            # فحص الملف المضغوط
            with zipfile.ZipFile(backup_path, 'r') as zipf:
                # اختبار سلامة الملف المضغوط
                bad_file = zipf.testzip()
                if bad_file:
                    return False, f"ملف تالف: {bad_file}"
                
                # التحقق من وجود ملف المعلومات
                if 'backup_info.json' not in zipf.namelist():
                    return False, "ملف معلومات النسخة الاحتياطية مفقود"
                
                # قراءة وفحص ملف المعلومات
                backup_info_data = zipf.read('backup_info.json')
                backup_info = json.loads(backup_info_data.decode('utf-8'))
                
                # فحص الحقول المطلوبة
                required_fields = ['created_at', 'backup_name', 'files_count']
                for field in required_fields:
                    if field not in backup_info:
                        return False, f"حقل مفقود في معلومات النسخة الاحتياطية: {field}"
            
            return True, "النسخة الاحتياطية سليمة"
            
        except zipfile.BadZipFile:
            return False, "ملف مضغوط تالف"
        except json.JSONDecodeError:
            return False, "ملف معلومات النسخة الاحتياطية تالف"
        except Exception as e:
            return False, f"خطأ في التحقق: {str(e)}"
    
    def export_backup_to_location(self, backup_path, destination_folder):
        """تصدير نسخة احتياطية إلى مكان محدد"""
        try:
            backup_path = Path(backup_path)
            destination_folder = Path(destination_folder)
            
            if not backup_path.exists():
                return False, "النسخة الاحتياطية غير موجودة"
            
            if not destination_folder.exists():
                destination_folder.mkdir(parents=True, exist_ok=True)
            
            # نسخ الملف
            destination_path = destination_folder / backup_path.name
            shutil.copy2(backup_path, destination_path)
            
            return True, f"تم تصدير النسخة الاحتياطية إلى: {destination_path}"
            
        except Exception as e:
            return False, f"خطأ في التصدير: {str(e)}"
    
    def import_backup_from_location(self, source_path):
        """استيراد نسخة احتياطية من مكان محدد"""
        try:
            source_path = Path(source_path)
            
            if not source_path.exists():
                return False, "الملف المصدر غير موجود"
            
            # التحقق من سلامة النسخة الاحتياطية
            is_valid, message = self.verify_backup(source_path)
            if not is_valid:
                return False, f"النسخة الاحتياطية غير صالحة: {message}"
            
            # نسخ الملف إلى مجلد النسخ الاحتياطية
            destination_path = self.backup_folder / source_path.name
            shutil.copy2(source_path, destination_path)
            
            return True, f"تم استيراد النسخة الاحتياطية: {destination_path}"
            
        except Exception as e:
            return False, f"خطأ في الاستيراد: {str(e)}"
    
    def get_backup_statistics(self):
        """الحصول على إحصائيات النسخ الاحتياطية"""
        try:
            backups = self.get_available_backups()
            
            if not backups:
                return {
                    'total_backups': 0,
                    'total_size': 0,
                    'total_size_formatted': '0 B',
                    'oldest_backup': None,
                    'newest_backup': None,
                    'auto_backup_enabled': self.auto_backup_enabled,
                    'backup_interval_hours': self.backup_interval_hours,
                    'max_backups': self.max_backups
                }
            
            total_size = sum(backup['file_size'] for backup in backups)
            oldest_backup = min(backups, key=lambda x: x['created_at'])
            newest_backup = max(backups, key=lambda x: x['created_at'])
            
            return {
                'total_backups': len(backups),
                'total_size': total_size,
                'total_size_formatted': self.format_size(total_size),
                'oldest_backup': oldest_backup,
                'newest_backup': newest_backup,
                'auto_backup_enabled': self.auto_backup_enabled,
                'backup_interval_hours': self.backup_interval_hours,
                'max_backups': self.max_backups
            }
            
        except Exception as e:
            print(f"خطأ في حساب إحصائيات النسخ الاحتياطية: {e}")
            return {}
    
    def format_size(self, size_bytes):
        """تنسيق حجم الملف"""
        if size_bytes == 0:
            return "0 B"
        
        size_names = ["B", "KB", "MB", "GB", "TB"]
        i = 0
        while size_bytes >= 1024 and i < len(size_names) - 1:
            size_bytes /= 1024.0
            i += 1
        
        return f"{size_bytes:.2f} {size_names[i]}"

# إنشاء مثيل عام لنظام النسخ الاحتياطي
backup_system = BackupSystem()
